<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能最终测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            margin-top: 20px;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 3px 0;
        }
        
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warn { color: #ffc107; }
        
        .instructions {
            background: #e9ecef;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🌟 收藏功能最终测试</h1>
            <p>彻底验证收藏功能是否正常工作</p>
        </div>

        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ol>
                <li>点击下方"打开主系统"按钮</li>
                <li>使用 test/test123 或 admin/admin123 登录</li>
                <li>进入任意文件夹，找到图片文件</li>
                <li>快速连续点击星星图标（收藏按钮）</li>
                <li>观察是否还会出现反复切换的问题</li>
                <li>测试右键菜单和预览模式的收藏功能</li>
                <li>检查收藏夹页面是否正确显示</li>
            </ol>
        </div>

        <div style="text-align: center;">
            <button class="test-button" onclick="openMainSystem()">
                🚀 打开主系统
            </button>
            
            <button class="test-button" onclick="testAPI()">
                🔧 测试API连接
            </button>
            
            <button class="test-button" onclick="clearLog()">
                🗑️ 清空日志
            </button>
        </div>

        <div id="status" class="status info">
            等待开始测试...
        </div>

        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">[系统] 收藏功能测试页面已加载</div>
        </div>
    </div>

    <script>
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function setStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function openMainSystem() {
            addLog('正在打开主系统...', 'info');
            setStatus('主系统已在新窗口打开，请进行测试', 'success');
            window.open('http://localhost:8084/index.html', '_blank');
        }

        async function testAPI() {
            addLog('开始测试API连接...', 'info');
            setStatus('正在测试API连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:8086/api/system/status');
                if (response.ok) {
                    const data = await response.json();
                    addLog('API连接成功', 'success');
                    addLog(`服务器状态: ${JSON.stringify(data)}`, 'info');
                    setStatus('API连接正常，可以开始测试', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`API连接失败: ${error.message}`, 'error');
                setStatus('API连接失败，请检查服务器状态', 'error');
            }
        }

        function clearLog() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry log-info">[系统] 日志已清空</div>';
            setStatus('日志已清空', 'info');
        }

        // 监听来自主系统的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'favorite-test') {
                addLog(event.data.message, event.data.level || 'info');
            }
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            addLog('测试页面初始化完成', 'success');
            setStatus('准备就绪，可以开始测试', 'success');
            
            // 自动测试API连接
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
