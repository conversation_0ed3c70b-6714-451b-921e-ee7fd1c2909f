<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单缩略图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .thumbnail-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .thumbnail-container img {
            max-width: 150px;
            max-height: 150px;
            border: 1px solid #ccc;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🖼️ 简单缩略图测试</h1>
    
    <div class="controls">
        <button class="btn" onclick="testDirectURLs()">测试直接URL</button>
        <button class="btn" onclick="clearResults()">清空结果</button>
    </div>
    
    <div id="results"></div>

    <script>
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function addResult(title, content, status = 'info') {
            const results = document.getElementById('results');
            const item = document.createElement('div');
            item.className = 'test-item';
            item.innerHTML = `
                <h3>${title}</h3>
                <div class="status ${status}">${status.toUpperCase()}</div>
                <div>${content}</div>
            `;
            results.appendChild(item);
        }

        function testDirectURLs() {
            clearResults();
            
            // 测试已知的文件ID（从之前的日志中获得）
            const testFiles = [
                { id: 3, name: '5efedafd65dce35beeadd6ad884233a.jpeg' },
                { id: 4, name: '664ace4d9f28cb621a39679e3d665673.jpg' }
            ];
            
            testFiles.forEach(file => {
                testSingleThumbnail(file);
            });
        }

        function testSingleThumbnail(file) {
            const baseUrl = 'http://localhost:8086';
            const thumbnailUrl = `${baseUrl}/api/files/${file.id}/thumbnail?size=medium`;
            
            console.log(`测试缩略图: ${thumbnailUrl}`);
            
            const img = new Image();
            
            img.onload = function() {
                console.log(`✅ 缩略图加载成功: ${file.name}`);
                addResult(
                    `文件: ${file.name}`,
                    `
                    <div class="thumbnail-container">
                        <img src="${thumbnailUrl}" alt="${file.name}">
                        <div>
                            <p><strong>文件ID:</strong> ${file.id}</p>
                            <p><strong>缩略图URL:</strong> <a href="${thumbnailUrl}" target="_blank">${thumbnailUrl}</a></p>
                            <p><strong>状态:</strong> 加载成功</p>
                            <p><strong>尺寸:</strong> ${this.naturalWidth} x ${this.naturalHeight}</p>
                        </div>
                    </div>
                    `,
                    'success'
                );
            };
            
            img.onerror = function() {
                console.log(`❌ 缩略图加载失败: ${file.name}`);
                addResult(
                    `文件: ${file.name}`,
                    `
                    <div>
                        <p><strong>文件ID:</strong> ${file.id}</p>
                        <p><strong>缩略图URL:</strong> <a href="${thumbnailUrl}" target="_blank">${thumbnailUrl}</a></p>
                        <p><strong>状态:</strong> 加载失败</p>
                        <p><strong>错误:</strong> 图片无法加载</p>
                    </div>
                    `,
                    'error'
                );
            };
            
            // 添加初始状态
            addResult(
                `文件: ${file.name}`,
                `
                <div>
                    <p><strong>文件ID:</strong> ${file.id}</p>
                    <p><strong>缩略图URL:</strong> <a href="${thumbnailUrl}" target="_blank">${thumbnailUrl}</a></p>
                    <p><strong>状态:</strong> 正在加载...</p>
                </div>
                `,
                'info'
            );
            
            img.src = thumbnailUrl;
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            console.log('页面加载完成，准备测试缩略图...');
        });
    </script>
</body>
</html>
