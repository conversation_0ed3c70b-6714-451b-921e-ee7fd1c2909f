#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限模型
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base
from typing import Dict, Any

class Permission(Base):
    """权限模型"""
    
    __tablename__ = 'permissions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), unique=True, nullable=False, comment='权限名称')
    description = Column(Text, nullable=True, comment='权限描述')
    
    # 权限类型
    permission_type = Column(String(20), default='file', comment='权限类型')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'permission_type': self.permission_type,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class UserPermission(Base):
    """用户权限关联模型"""
    
    __tablename__ = 'user_permissions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    permission_id = Column(Integer, ForeignKey('permissions.id'), nullable=False)
    
    # 权限范围
    resource_type = Column(String(50), nullable=True, comment='资源类型')
    resource_id = Column(Integer, nullable=True, comment='资源ID')
    
    # 权限状态
    is_active = Column(Boolean, default=True, comment='是否激活')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    expires_at = Column(DateTime, nullable=True, comment='过期时间')
    
    # 关联关系
    permission = relationship("Permission")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'permission_id': self.permission_id,
            'permission_name': self.permission.name if self.permission else None,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }
