#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载记录模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base
from datetime import datetime
from typing import Dict, Any

class DownloadRecord(Base):
    """下载记录模型"""
    
    __tablename__ = 'download_records'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_id = Column(Integer, nullable=True, comment='文件ID（单文件/批量下载时使用）')
    folder_id = Column(Integer, nullable=True, comment='文件夹ID（文件夹下载时使用）')
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)

    # 下载信息
    download_type = Column(String(20), nullable=False, comment='下载类型: single/batch/folder')
    zip_filename = Column(String(255), nullable=False, comment='压缩文件名')
    zip_path = Column(Text, nullable=False, comment='压缩文件路径')
    file_size = Column(Integer, nullable=False, comment='文件大小')
    
    # 加密信息
    is_encrypted = Column(Boolean, default=False, comment='是否加密')
    password = Column(String(50), nullable=True, comment='解压密码')
    password_hint = Column(String(255), nullable=True, comment='密码提示')
    
    # 状态信息
    download_status = Column(String(20), default='pending', comment='下载状态: pending/completed/expired')
    expires_at = Column(DateTime, nullable=True, comment='过期时间')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    downloaded_at = Column(DateTime, nullable=True, comment='下载时间')
    
    # 关联关系
    user = relationship("User", backref="download_records")

    def __init__(self, download_type: str, zip_filename: str,
                 zip_path: str, file_size: int, file_id: int = None,
                 folder_id: int = None, **kwargs):
        self.download_type = download_type
        self.zip_filename = zip_filename
        self.zip_path = zip_path
        self.file_size = file_size
        self.file_id = file_id
        self.folder_id = folder_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def mark_downloaded(self):
        """标记为已下载"""
        self.download_status = 'completed'
        self.downloaded_at = datetime.now()
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'file_id': self.file_id,
            'folder_id': self.folder_id,
            'user_id': self.user_id,
            'download_type': self.download_type,
            'zip_filename': self.zip_filename,
            'file_size': self.file_size,
            'is_encrypted': self.is_encrypted,
            'password': self.password if self.is_encrypted else None,
            'download_status': self.download_status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'downloaded_at': self.downloaded_at.isoformat() if self.downloaded_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_expired': self.is_expired()
        }

class PasswordRequest(Base):
    """密码申请记录模型"""
    
    __tablename__ = 'password_requests'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_id = Column(Integer, ForeignKey('shared_files.id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)
    download_record_id = Column(Integer, ForeignKey('download_records.id'), nullable=True)
    
    # 申请信息
    request_reason = Column(Text, nullable=True, comment='申请原因')
    request_ip = Column(String(45), nullable=True, comment='申请IP地址')
    user_agent = Column(Text, nullable=True, comment='用户代理')
    
    # 状态信息
    status = Column(String(20), default='pending', comment='申请状态: pending/approved/rejected')
    approved_by = Column(Integer, ForeignKey('users.id'), nullable=True, comment='审批人')
    approval_reason = Column(Text, nullable=True, comment='审批原因')
    
    # 密码信息
    password_provided = Column(String(50), nullable=True, comment='提供的密码')
    password_expires_at = Column(DateTime, nullable=True, comment='密码过期时间')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='申请时间')
    approved_at = Column(DateTime, nullable=True, comment='审批时间')
    
    # 关联关系
    file = relationship("SharedFile", backref="password_requests")
    user = relationship("User", foreign_keys=[user_id], backref="password_requests")
    approver = relationship("User", foreign_keys=[approved_by], backref="approved_requests")
    download_record = relationship("DownloadRecord", backref="password_requests")
    
    def __init__(self, file_id: int, user_id: int = None, **kwargs):
        self.file_id = file_id
        self.user_id = user_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def approve(self, password: str, approved_by: int = None, reason: str = None):
        """批准申请"""
        self.status = 'approved'
        self.password_provided = password
        self.approved_by = approved_by
        self.approval_reason = reason
        self.approved_at = datetime.now()
        
        # 设置密码过期时间（24小时后）
        from datetime import timedelta
        self.password_expires_at = datetime.now() + timedelta(hours=24)
    
    def reject(self, approved_by: int = None, reason: str = None):
        """拒绝申请"""
        self.status = 'rejected'
        self.approved_by = approved_by
        self.approval_reason = reason
        self.approved_at = datetime.now()
    
    def is_password_expired(self) -> bool:
        """检查密码是否过期"""
        if not self.password_expires_at:
            return True
        return datetime.now() > self.password_expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'file_id': self.file_id,
            'user_id': self.user_id,
            'download_record_id': self.download_record_id,
            'request_reason': self.request_reason,
            'status': self.status,
            'approved_by': self.approved_by,
            'approval_reason': self.approval_reason,
            'password_provided': self.password_provided if self.status == 'approved' else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'approved_at': self.approved_at.isoformat() if self.approved_at else None,
            'password_expires_at': self.password_expires_at.isoformat() if self.password_expires_at else None,
            'is_password_expired': self.is_password_expired()
        }

class DownloadStatistics(Base):
    """下载统计模型"""
    
    __tablename__ = 'download_statistics'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    file_id = Column(Integer, ForeignKey('shared_files.id'), nullable=False)
    
    # 统计信息
    total_downloads = Column(Integer, default=0, comment='总下载次数')
    encrypted_downloads = Column(Integer, default=0, comment='加密下载次数')
    password_requests = Column(Integer, default=0, comment='密码申请次数')
    successful_requests = Column(Integer, default=0, comment='成功申请次数')
    
    # 时间统计
    first_download = Column(DateTime, nullable=True, comment='首次下载时间')
    last_download = Column(DateTime, nullable=True, comment='最后下载时间')
    last_password_request = Column(DateTime, nullable=True, comment='最后密码申请时间')
    
    # 更新时间
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关联关系
    file = relationship("SharedFile", backref="download_statistics")
    
    def __init__(self, file_id: int):
        self.file_id = file_id
    
    def increment_download(self, is_encrypted: bool = False):
        """增加下载次数"""
        self.total_downloads += 1
        if is_encrypted:
            self.encrypted_downloads += 1
        
        now = datetime.now()
        if not self.first_download:
            self.first_download = now
        self.last_download = now
    
    def increment_password_request(self, successful: bool = False):
        """增加密码申请次数"""
        self.password_requests += 1
        if successful:
            self.successful_requests += 1
        
        self.last_password_request = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'file_id': self.file_id,
            'total_downloads': self.total_downloads,
            'encrypted_downloads': self.encrypted_downloads,
            'password_requests': self.password_requests,
            'successful_requests': self.successful_requests,
            'first_download': self.first_download.isoformat() if self.first_download else None,
            'last_download': self.last_download.isoformat() if self.last_download else None,
            'last_password_request': self.last_password_request.isoformat() if self.last_password_request else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
