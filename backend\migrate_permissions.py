#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本 - 添加权限字段
"""

import pymysql
import json

def migrate_database():
    """迁移数据库，添加permissions字段"""
    print("🔄 开始数据库迁移...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查permissions字段是否已存在
            cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'file_share_system' 
                AND TABLE_NAME = 'shared_folders' 
                AND COLUMN_NAME = 'permissions'
            """)
            
            if cursor.fetchone():
                print("✅ permissions字段已存在，无需迁移")
                return True
            
            # 添加permissions字段
            print("📝 添加permissions字段...")
            cursor.execute("""
                ALTER TABLE shared_folders 
                ADD COLUMN permissions JSON NULL COMMENT '详细权限设置'
            """)
            
            # 为现有文件夹设置默认权限
            print("🔧 设置默认权限...")
            default_permissions = {
                'network_access': {
                    'internal': True,
                    'external': False
                },
                'file_operations': {
                    'read': True,
                    'write': False,
                    'delete': False,
                    'replace': False,
                    'show_details': True
                },
                'download_limits': {
                    'max_file_size_mb': 100,
                    'max_batch_files': 10
                },
                'encryption': {
                    'encrypt_after_downloads': 5
                }
            }
            
            # 获取所有现有文件夹
            cursor.execute("SELECT id, name FROM shared_folders")
            folders = cursor.fetchall()
            
            for folder_id, folder_name in folders:
                cursor.execute(
                    "UPDATE shared_folders SET permissions = %s WHERE id = %s",
                    (json.dumps(default_permissions), folder_id)
                )
                print(f"   ✓ 设置文件夹 '{folder_name}' 的默认权限")
            
            connection.commit()
            print("✅ 数据库迁移完成！")
            return True
            
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    migrate_database()
