#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
from datetime import datetime, timed<PERSON><PERSON>

def create_test_records():
    """创建测试下载记录"""
    print("📝 创建测试下载记录...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 获取用户fjj的ID
            cursor.execute("SELECT id FROM users WHERE username = 'fjj'")
            user = cursor.fetchone()
            
            if not user:
                print("❌ 找不到用户fjj")
                return False
            
            user_id = user[0]
            print(f"✅ 找到用户fjj，ID: {user_id}")
            
            # 检查现有记录
            cursor.execute("SELECT COUNT(*) FROM download_records WHERE user_id = %s", (user_id,))
            count = cursor.fetchone()[0]
            print(f"📊 现有下载记录数量: {count}")
            
            # 获取一些文件用于测试
            cursor.execute("SELECT id, filename FROM shared_files LIMIT 10")
            files = cursor.fetchall()
            
            if not files:
                print("⚠️ 没有找到共享文件，创建虚拟文件记录...")
                # 创建一些虚拟文件记录用于测试
                test_files = [
                    (1, "test_image_1.jpg"),
                    (2, "test_image_2.png"),
                    (3, "test_document.pdf"),
                    (4, "test_archive.zip"),
                    (5, "test_video.mp4")
                ]
                files = test_files
            
            print(f"📁 找到 {len(files)} 个文件用于测试")
            
            # 清除现有的测试记录
            cursor.execute("DELETE FROM download_records WHERE user_id = %s", (user_id,))
            print("🗑️ 清除了现有的测试记录")
            
            # 创建测试记录
            created_count = 0
            for i, (file_id, filename) in enumerate(files[:5]):
                download_time = datetime.now() - timedelta(days=i, hours=i*2)
                is_encrypted = i % 2 == 0
                
                cursor.execute("""
                    INSERT INTO download_records 
                    (file_id, user_id, download_type, zip_filename, zip_path, file_size, 
                     is_encrypted, password, download_status, created_at, downloaded_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    file_id, user_id, 'single',
                    f"{filename}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                    f"/tmp/downloads/{filename}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                    1024 * 1024 * (i + 1),  # 不同大小
                    is_encrypted,
                    f"pass{i+1}" if is_encrypted else None,
                    'completed',
                    download_time,
                    download_time
                ))
                created_count += 1
            
            connection.commit()
            print(f"✅ 创建了 {created_count} 条测试下载记录")
            
            # 显示创建的记录
            cursor.execute("""
                SELECT id, file_id, zip_filename, download_type, is_encrypted, 
                       file_size, created_at, downloaded_at
                FROM download_records 
                WHERE user_id = %s 
                ORDER BY created_at DESC
            """, (user_id,))
            
            records = cursor.fetchall()
            print(f"\n📋 创建的下载记录:")
            print("ID | 文件ID | 压缩文件名 | 类型 | 加密 | 大小(MB) | 创建时间")
            print("-" * 80)
            
            for record in records:
                file_size_mb = record[5] / (1024 * 1024) if record[5] else 0
                print(f"{record[0]:3d} | {record[1]:6d} | {record[2][:25]:25s} | {record[3]:6s} | {record[4]:5s} | {file_size_mb:8.1f} | {record[6]}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建测试记录失败: {e}")
        return False

if __name__ == "__main__":
    create_test_records()
