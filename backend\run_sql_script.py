#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import os

def run_sql_script():
    """运行SQL脚本创建测试数据"""
    print("📝 运行SQL脚本创建测试下载记录...")
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        # 读取SQL脚本
        sql_file = os.path.join(os.path.dirname(__file__), 'insert_test_records.sql')
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        with connection.cursor() as cursor:
            for sql in sql_statements:
                if sql.startswith('--') or not sql:
                    continue
                    
                print(f"执行SQL: {sql[:50]}...")
                cursor.execute(sql)
                
                # 如果是SELECT语句，显示结果
                if sql.strip().upper().startswith('SELECT'):
                    results = cursor.fetchall()
                    if results:
                        print("查询结果:")
                        for row in results:
                            print(f"  {row}")
            
            connection.commit()
            print("✅ SQL脚本执行成功！")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ SQL脚本执行失败: {e}")
        return False

if __name__ == "__main__":
    run_sql_script()
