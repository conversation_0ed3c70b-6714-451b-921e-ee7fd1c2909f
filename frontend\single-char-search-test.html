<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单字符搜索测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #dbeafe;
            --primary-color-alpha: rgba(37, 99, 235, 0.1);
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e2e8f0;
            --gray-500: #6b7280;
            --gray-800: #1f2937;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --spacing-4: 1rem;
            --spacing-6: 1.5rem;
            --transition: all 0.2s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--gray-800);
            line-height: 1.6;
            padding: 2rem;
        }

        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .test-header h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .test-header p {
            font-size: 1.1rem;
            color: var(--gray-500);
        }

        .search-demo {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .search-demo h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 搜索框样式 */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 28px;
            transition: all 0.2s ease;
            overflow: hidden;
            height: 44px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin-bottom: 1rem;
        }

        .search-container:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .search-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-color-alpha), 0 4px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
        }

        .search-container .fa-search {
            position: absolute;
            left: 18px;
            color: var(--gray-500);
            font-size: 16px;
            z-index: 2;
            transition: color 0.2s ease;
        }

        .search-container:focus-within .fa-search {
            color: var(--primary-color);
        }

        .search-container input {
            width: 100%;
            padding: 12px 56px 12px 48px;
            font-size: 15px;
            font-weight: 400;
            border: none;
            background: transparent;
            color: var(--gray-800);
            outline: none;
            height: 20px;
            line-height: 20px;
        }

        .search-container input::placeholder {
            color: var(--gray-500);
            font-weight: 400;
        }

        .search-type-indicator {
            position: absolute;
            right: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            color: white;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .search-container:focus-within .search-type-indicator {
            transform: scale(1.05);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
        }

        .search-type-indicator .fa-image {
            font-size: 12px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .test-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .test-section h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-buttons {
            display: flex;
            gap: 0.5rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }

        .test-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            background: transparent;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
            min-width: 60px;
        }

        .test-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .test-btn.single-char {
            background: var(--warning-color);
            border-color: var(--warning-color);
            color: white;
        }

        .test-btn.single-char:hover {
            background: #d97706;
            border-color: #d97706;
        }

        .status-panel {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .status-panel h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-value {
            font-weight: 600;
        }

        .status-value.success {
            color: var(--success-color);
        }

        .status-value.warning {
            color: var(--warning-color);
        }

        .status-value.error {
            color: var(--error-color);
        }

        .log-panel {
            background: #1a1a1a;
            color: #00ff00;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 1rem;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .highlight-box h4 {
            color: #92400e;
            margin-bottom: 0.5rem;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .comparison-table th {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--primary-color);
        }

        .comparison-table .before {
            color: var(--error-color);
        }

        .comparison-table .after {
            color: var(--success-color);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-keyboard"></i> 单字符搜索测试</h1>
            <p>测试修复后的单字符搜索功能，确保1个字符也能正常搜索</p>
        </div>

        <div class="search-demo">
            <h2><i class="fas fa-search"></i> 搜索演示</h2>
            <div class="search-container">
                <i class="fas fa-search"></i>
                <input type="text" id="search-input" placeholder="尝试输入单个字符搜索...">
                <div class="search-type-indicator">
                    <i class="fas fa-image"></i>
                </div>
            </div>
            
            <div class="highlight-box">
                <h4>🔧 修复说明</h4>
                <p><strong>问题：</strong>之前搜索1个字符时会一直加载目录，因为最小搜索长度限制为2个字符</p>
                <p><strong>修复：</strong>将最小搜索长度改为1个字符，并优化搜索逻辑避免不必要的目录加载</p>
            </div>
        </div>

        <div class="test-grid">
            <div class="test-section">
                <h3><i class="fas fa-bug"></i> 问题重现测试</h3>
                <p>测试修复前的问题场景</p>
                <div class="test-buttons">
                    <button class="test-btn single-char" onclick="testSingleChar('1')">搜索"1"</button>
                    <button class="test-btn single-char" onclick="testSingleChar('3')">搜索"3"</button>
                    <button class="test-btn single-char" onclick="testSingleChar('a')">搜索"a"</button>
                    <button class="test-btn single-char" onclick="testSingleChar('x')">搜索"x"</button>
                </div>
                <p><small>这些单字符搜索现在应该正常工作，不会一直加载目录</small></p>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-check"></i> 多字符对比测试</h3>
                <p>对比单字符和多字符搜索</p>
                <div class="test-buttons">
                    <button class="test-btn" onclick="testMultiChar('12')">搜索"12"</button>
                    <button class="test-btn" onclick="testMultiChar('abc')">搜索"abc"</button>
                    <button class="test-btn" onclick="testMultiChar('test')">搜索"test"</button>
                    <button class="test-btn" onclick="clearSearch()">清空</button>
                </div>
                <p><small>多字符搜索应该和之前一样正常工作</small></p>
            </div>
        </div>

        <div class="status-panel">
            <h3><i class="fas fa-chart-line"></i> 搜索状态监控</h3>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>测试项目</th>
                        <th>修复前</th>
                        <th>修复后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>最小搜索长度</td>
                        <td class="before">2个字符</td>
                        <td class="after">1个字符</td>
                    </tr>
                    <tr>
                        <td>单字符搜索</td>
                        <td class="before">一直加载目录</td>
                        <td class="after">正常搜索</td>
                    </tr>
                    <tr>
                        <td>空搜索处理</td>
                        <td class="before">恢复目录</td>
                        <td class="after">恢复目录</td>
                    </tr>
                    <tr>
                        <td>多字符搜索</td>
                        <td class="before">正常</td>
                        <td class="after">正常</td>
                    </tr>
                </tbody>
            </table>

            <div class="status-item">
                <span>当前搜索内容:</span>
                <span class="status-value" id="current-query">-</span>
            </div>
            <div class="status-item">
                <span>搜索状态:</span>
                <span class="status-value" id="search-status">待测试</span>
            </div>
            <div class="status-item">
                <span>字符长度:</span>
                <span class="status-value" id="query-length">0</span>
            </div>
            <div class="status-item">
                <span>是否触发搜索:</span>
                <span class="status-value" id="search-triggered">否</span>
            </div>
            
            <div class="log-panel" id="log-panel">
                <div>单字符搜索测试日志:</div>
                <div>等待测试开始...</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟配置
        const MIN_QUERY_LENGTH = 1; // 修复后的最小搜索长度

        function testSingleChar(char) {
            const searchInput = document.getElementById('search-input');
            searchInput.value = char;
            searchInput.focus();
            
            updateStatus(char);
            addLog(`🔍 测试单字符搜索: "${char}"`);
            addLog(`✅ 字符长度: ${char.length} (>= ${MIN_QUERY_LENGTH})`);
            addLog(`✅ 应该触发搜索，不会一直加载目录`);
            
            // 模拟搜索结果
            setTimeout(() => {
                const mockResults = Math.floor(Math.random() * 5);
                addLog(`📊 模拟搜索结果: 找到 ${mockResults} 个图片文件`);
                document.getElementById('search-status').textContent = '搜索完成';
                document.getElementById('search-status').className = 'status-value success';
            }, 500);
        }

        function testMultiChar(query) {
            const searchInput = document.getElementById('search-input');
            searchInput.value = query;
            searchInput.focus();
            
            updateStatus(query);
            addLog(`🔍 测试多字符搜索: "${query}"`);
            addLog(`✅ 字符长度: ${query.length} (>= ${MIN_QUERY_LENGTH})`);
            addLog(`✅ 正常触发搜索`);
            
            // 模拟搜索结果
            setTimeout(() => {
                const mockResults = Math.floor(Math.random() * 10);
                addLog(`📊 模拟搜索结果: 找到 ${mockResults} 个图片文件`);
                document.getElementById('search-status').textContent = '搜索完成';
                document.getElementById('search-status').className = 'status-value success';
            }, 500);
        }

        function clearSearch() {
            const searchInput = document.getElementById('search-input');
            searchInput.value = '';
            searchInput.blur();
            
            updateStatus('');
            addLog(`🔄 清空搜索`);
            addLog(`📁 恢复原始文件列表`);
            
            document.getElementById('search-status').textContent = '已清空';
            document.getElementById('search-status').className = 'status-value';
        }

        function updateStatus(query) {
            document.getElementById('current-query').textContent = query || '-';
            document.getElementById('query-length').textContent = query.length;
            document.getElementById('search-triggered').textContent = query.length >= MIN_QUERY_LENGTH ? '是' : '否';
            document.getElementById('search-triggered').className = query.length >= MIN_QUERY_LENGTH ? 'status-value success' : 'status-value warning';
            
            if (query.length > 0) {
                document.getElementById('search-status').textContent = '搜索中...';
                document.getElementById('search-status').className = 'status-value warning';
            }
        }

        function addLog(message) {
            const logPanel = document.getElementById('log-panel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 搜索框交互监控
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                updateStatus(query);
                
                if (query.length === 1) {
                    addLog(`⚡ 单字符输入: "${query}" - 现在会触发搜索`);
                } else if (query.length === 0) {
                    addLog(`🔄 搜索框已清空 - 恢复原始列表`);
                } else if (query.length > 1) {
                    addLog(`📝 多字符输入: "${query}" - 正常搜索`);
                }
            });
            
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    addLog(`⏎ 回车键搜索: "${this.value}"`);
                }
                if (e.key === 'Escape') {
                    addLog(`⎋ ESC键清空搜索`);
                    clearSearch();
                }
            });
            
            addLog('🚀 单字符搜索测试页面已加载');
            addLog(`⚙️ 最小搜索长度已设置为: ${MIN_QUERY_LENGTH} 个字符`);
            addLog('📋 可以开始测试单字符搜索功能');
        });
    </script>
</body>
</html>
