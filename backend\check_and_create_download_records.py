#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查和创建下载记录
"""

import pymysql
from datetime import datetime, timedelta
import os
import sys

def check_and_create_download_records():
    """检查并创建测试下载记录"""
    try:
        print("🔍 检查下载记录...")
        
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查用户表
            cursor.execute("SELECT id, username FROM users WHERE username = 'fjj'")
            user = cursor.fetchone()
            
            if not user:
                print("❌ 找不到用户fjj")
                return False
            
            user_id = user[0]
            print(f"✅ 找到用户fjj，ID: {user_id}")
            
            # 检查现有下载记录
            cursor.execute("SELECT COUNT(*) FROM download_records WHERE user_id = %s", (user_id,))
            count = cursor.fetchone()[0]
            print(f"📊 用户 {user_id} 现有下载记录数量: {count}")
            
            # 如果没有下载记录，创建一些测试记录
            if count == 0:
                print("📝 创建测试下载记录...")
                
                # 获取一些文件ID用于测试
                cursor.execute("SELECT id, filename FROM shared_files LIMIT 5")
                files = cursor.fetchall()
                
                if not files:
                    print("⚠️ 没有找到共享文件，无法创建测试记录")
                    return False
                
                # 创建测试下载记录
                test_records = []
                for i, (file_id, filename) in enumerate(files):
                    # 创建不同类型的下载记录
                    is_encrypted = i % 2 == 0  # 交替加密
                    download_time = datetime.now() - timedelta(days=i, hours=i)
                    
                    test_record = {
                        'file_id': file_id,
                        'user_id': user_id,
                        'download_type': 'single',
                        'zip_filename': f"{filename}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                        'zip_path': f"/tmp/downloads/{filename}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                        'file_size': 1024 * 1024 * (i + 1),  # 不同大小
                        'is_encrypted': is_encrypted,
                        'password': f"pass{i+1}" if is_encrypted else None,
                        'download_status': 'completed',
                        'created_at': download_time,
                        'downloaded_at': download_time
                    }
                    test_records.append(test_record)
                
                # 插入测试记录
                for record in test_records:
                    cursor.execute("""
                        INSERT INTO download_records 
                        (file_id, user_id, download_type, zip_filename, zip_path, file_size, 
                         is_encrypted, password, download_status, created_at, downloaded_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        record['file_id'], record['user_id'], record['download_type'],
                        record['zip_filename'], record['zip_path'], record['file_size'],
                        record['is_encrypted'], record['password'], record['download_status'],
                        record['created_at'], record['downloaded_at']
                    ))
                
                connection.commit()
                print(f"✅ 创建了 {len(test_records)} 条测试下载记录")
            
            # 显示最新的下载记录
            cursor.execute("""
                SELECT dr.id, dr.file_id, sf.filename, dr.zip_filename, dr.download_type, 
                       dr.is_encrypted, dr.download_status, dr.file_size, dr.created_at, dr.downloaded_at
                FROM download_records dr
                LEFT JOIN shared_files sf ON dr.file_id = sf.id
                WHERE dr.user_id = %s 
                ORDER BY dr.created_at DESC 
                LIMIT 10
            """, (user_id,))
            
            records = cursor.fetchall()
            print(f"\n📋 用户 {user_id} 的下载记录 (最新10条):")
            print("ID | 文件ID | 原文件名 | 压缩文件名 | 类型 | 加密 | 状态 | 大小 | 创建时间")
            print("-" * 120)
            
            for record in records:
                file_size_mb = record[7] / (1024 * 1024) if record[7] else 0
                print(f"{record[0]:3d} | {record[1]:6d} | {record[2][:15]:15s} | {record[3][:20]:20s} | {record[4]:6s} | {record[5]:5s} | {record[6]:9s} | {file_size_mb:6.1f}MB | {record[8]}")
            
            # 检查下载统计表
            cursor.execute("""
                SELECT ds.file_id, sf.filename, ds.total_downloads, ds.encrypted_downloads, 
                       ds.first_download, ds.last_download
                FROM download_statistics ds
                LEFT JOIN shared_files sf ON ds.file_id = sf.id
                ORDER BY ds.last_download DESC
                LIMIT 5
            """)
            
            stats = cursor.fetchall()
            if stats:
                print(f"\n📈 下载统计 (最新5条):")
                print("文件ID | 文件名 | 总下载 | 加密下载 | 首次下载 | 最后下载")
                print("-" * 80)
                for stat in stats:
                    print(f"{stat[0]:6d} | {stat[1][:15]:15s} | {stat[2]:6d} | {stat[3]:8d} | {stat[4]} | {stat[5]}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        return False

if __name__ == "__main__":
    success = check_and_create_download_records()
    if success:
        print("\n✅ 下载记录检查和创建完成")
    else:
        print("\n❌ 下载记录检查和创建失败")
        sys.exit(1)
