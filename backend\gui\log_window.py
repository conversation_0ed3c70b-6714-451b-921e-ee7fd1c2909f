#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统日志窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import threading
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any

class LogWindow:
    """系统日志窗口类"""
    
    def __init__(self, parent, server):
        self.parent = parent
        self.server = server
        self.window = None
        self.auto_refresh = True
        self.refresh_thread = None
        
        # 日志文件路径
        self.log_dir = os.path.join(os.getcwd(), 'logs')
        self.current_log_file = None
        
        self.create_window()
    
    def create_window(self):
        """创建日志窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("系统日志")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建工具栏
        self.create_toolbar(main_frame)
        
        # 创建日志显示区域
        self.create_log_display(main_frame)
        
        # 创建状态栏
        self.create_statusbar(main_frame)
        
        # 加载日志
        self.load_logs()
        
        # 开始自动刷新
        self.start_auto_refresh()
        
        # 窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # 日志文件选择
        ttk.Label(toolbar, text="日志文件:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.log_file_var = tk.StringVar()
        self.log_file_combo = ttk.Combobox(toolbar, textvariable=self.log_file_var, width=30, state="readonly")
        self.log_file_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.log_file_combo.bind('<<ComboboxSelected>>', self.on_log_file_changed)
        
        # 日志级别过滤
        ttk.Label(toolbar, text="级别:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.level_var = tk.StringVar(value="全部")
        level_combo = ttk.Combobox(toolbar, textvariable=self.level_var, width=10, state="readonly")
        level_combo['values'] = ("全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL")
        level_combo.pack(side=tk.LEFT, padx=(0, 10))
        level_combo.bind('<<ComboboxSelected>>', self.filter_logs)
        
        # 时间范围过滤
        ttk.Label(toolbar, text="时间范围:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.time_range_var = tk.StringVar(value="全部")
        time_combo = ttk.Combobox(toolbar, textvariable=self.time_range_var, width=15, state="readonly")
        time_combo['values'] = ("全部", "最近1小时", "最近6小时", "今天", "昨天", "最近7天")
        time_combo.pack(side=tk.LEFT, padx=(0, 10))
        time_combo.bind('<<ComboboxSelected>>', self.filter_logs)
        
        # 搜索框
        ttk.Label(toolbar, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 5))
        search_entry.bind('<KeyRelease>', self.on_search_changed)
        
        # 按钮
        ttk.Button(toolbar, text="刷新", command=self.refresh_logs).pack(side=tk.LEFT, padx=(10, 5))
        ttk.Button(toolbar, text="清空", command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="导出", command=self.export_logs).pack(side=tk.LEFT, padx=(0, 5))
        
        # 自动刷新开关
        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(toolbar, text="自动刷新", variable=self.auto_refresh_var, 
                       command=self.toggle_auto_refresh).pack(side=tk.RIGHT)
    
    def create_log_display(self, parent):
        """创建日志显示区域"""
        # 创建框架
        display_frame = ttk.Frame(parent)
        display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview显示日志
        columns = ("时间", "级别", "模块", "消息")
        self.log_tree = ttk.Treeview(display_frame, columns=columns, show="headings", height=25)
        
        # 设置列标题和宽度
        self.log_tree.heading("时间", text="时间")
        self.log_tree.heading("级别", text="级别")
        self.log_tree.heading("模块", text="模块")
        self.log_tree.heading("消息", text="消息")
        
        self.log_tree.column("时间", width=180)
        self.log_tree.column("级别", width=80)
        self.log_tree.column("模块", width=150)
        self.log_tree.column("消息", width=600)
        
        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(display_frame, orient=tk.VERTICAL, command=self.log_tree.yview)
        h_scrollbar = ttk.Scrollbar(display_frame, orient=tk.HORIZONTAL, command=self.log_tree.xview)
        
        self.log_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.log_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        display_frame.grid_rowconfigure(0, weight=1)
        display_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定双击事件
        self.log_tree.bind('<Double-1>', self.on_log_double_click)
        
        # 设置行颜色
        self.log_tree.tag_configure('ERROR', background='#ffcccc')
        self.log_tree.tag_configure('WARNING', background='#ffffcc')
        self.log_tree.tag_configure('CRITICAL', background='#ff9999')
        self.log_tree.tag_configure('DEBUG', foreground='#666666')
    
    def create_statusbar(self, parent):
        """创建状态栏"""
        statusbar = ttk.Frame(parent)
        statusbar.pack(fill=tk.X, pady=(10, 0))
        
        self.status_label = ttk.Label(statusbar, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        self.log_count_label = ttk.Label(statusbar, text="日志条数: 0")
        self.log_count_label.pack(side=tk.RIGHT)
    
    def load_logs(self):
        """加载日志文件列表"""
        try:
            if not os.path.exists(self.log_dir):
                os.makedirs(self.log_dir)
                return
            
            # 获取所有日志文件
            log_files = []
            for filename in os.listdir(self.log_dir):
                if filename.endswith('.log'):
                    log_files.append(filename)
            
            # 按时间排序
            log_files.sort(reverse=True)
            
            # 更新下拉框
            self.log_file_combo['values'] = log_files
            
            # 选择最新的日志文件
            if log_files:
                self.log_file_var.set(log_files[0])
                self.load_log_content(log_files[0])
            
        except Exception as e:
            messagebox.showerror("错误", f"加载日志文件失败: {e}")
    
    def load_log_content(self, filename):
        """加载日志内容"""
        try:
            self.status_label.config(text=f"正在加载日志: {filename}")
            
            # 清空现有内容
            self.log_tree.delete(*self.log_tree.get_children())
            
            log_file_path = os.path.join(self.log_dir, filename)
            if not os.path.exists(log_file_path):
                return
            
            # 读取日志文件
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 解析日志行
            log_entries = []
            for line in lines:
                entry = self.parse_log_line(line.strip())
                if entry:
                    log_entries.append(entry)
            
            # 显示日志
            self.display_log_entries(log_entries)
            
            self.current_log_file = filename
            self.status_label.config(text=f"已加载日志: {filename}")
            self.log_count_label.config(text=f"日志条数: {len(log_entries)}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载日志内容失败: {e}")
            self.status_label.config(text="加载失败")
    
    def parse_log_line(self, line):
        """解析日志行"""
        try:
            # 日志格式: 2025-06-07 21:39:54 - ModuleName - LEVEL - Message
            if ' - ' not in line:
                return None
            
            parts = line.split(' - ', 3)
            if len(parts) < 4:
                return None
            
            timestamp = parts[0]
            module = parts[1]
            level = parts[2]
            message = parts[3]
            
            return {
                'timestamp': timestamp,
                'level': level,
                'module': module,
                'message': message,
                'raw_line': line
            }
            
        except Exception:
            return None
    
    def display_log_entries(self, entries):
        """显示日志条目"""
        for entry in entries:
            # 确定标签
            tags = []
            level = entry['level']
            if level in ['ERROR', 'WARNING', 'CRITICAL', 'DEBUG']:
                tags.append(level)
            
            # 插入到树形视图
            self.log_tree.insert("", tk.END, values=(
                entry['timestamp'],
                entry['level'],
                entry['module'],
                entry['message']
            ), tags=tags)
        
        # 滚动到底部
        if entries:
            self.log_tree.see(self.log_tree.get_children()[-1])
    
    def filter_logs(self, event=None):
        """过滤日志"""
        if not self.current_log_file:
            return
        
        # 重新加载并过滤
        self.load_log_content(self.current_log_file)
        
        # 应用过滤器
        level_filter = self.level_var.get()
        time_filter = self.time_range_var.get()
        search_text = self.search_var.get().lower()
        
        # 获取所有项目
        all_items = self.log_tree.get_children()
        
        for item in all_items:
            values = self.log_tree.item(item)['values']
            show_item = True
            
            # 级别过滤
            if level_filter != "全部" and values[1] != level_filter:
                show_item = False
            
            # 时间过滤
            if time_filter != "全部":
                timestamp_str = values[0]
                if not self.is_in_time_range(timestamp_str, time_filter):
                    show_item = False
            
            # 搜索过滤
            if search_text:
                item_text = ' '.join(str(v).lower() for v in values)
                if search_text not in item_text:
                    show_item = False
            
            # 隐藏或显示项目
            if not show_item:
                self.log_tree.delete(item)
    
    def is_in_time_range(self, timestamp_str, time_range):
        """检查时间是否在指定范围内"""
        try:
            log_time = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
            now = datetime.now()
            
            if time_range == "最近1小时":
                return now - log_time <= timedelta(hours=1)
            elif time_range == "最近6小时":
                return now - log_time <= timedelta(hours=6)
            elif time_range == "今天":
                return log_time.date() == now.date()
            elif time_range == "昨天":
                yesterday = now.date() - timedelta(days=1)
                return log_time.date() == yesterday
            elif time_range == "最近7天":
                return now - log_time <= timedelta(days=7)
            
            return True
            
        except Exception:
            return True
    
    def on_log_file_changed(self, event):
        """日志文件改变事件"""
        filename = self.log_file_var.get()
        if filename:
            self.load_log_content(filename)
    
    def on_search_changed(self, event):
        """搜索内容改变事件"""
        # 延迟搜索，避免频繁过滤
        if hasattr(self, 'search_timer'):
            self.window.after_cancel(self.search_timer)
        self.search_timer = self.window.after(500, self.filter_logs)
    
    def on_log_double_click(self, event):
        """日志双击事件"""
        selection = self.log_tree.selection()
        if selection:
            item = self.log_tree.item(selection[0])
            values = item['values']
            
            # 显示详细信息
            detail_text = f"时间: {values[0]}\n级别: {values[1]}\n模块: {values[2]}\n消息: {values[3]}"
            messagebox.showinfo("日志详情", detail_text)
    
    def refresh_logs(self):
        """刷新日志"""
        self.load_logs()
        if self.current_log_file:
            self.load_log_content(self.current_log_file)
    
    def clear_logs(self):
        """清空日志"""
        result = messagebox.askyesno("确认", "确定要清空当前日志文件吗？")
        if result and self.current_log_file:
            try:
                log_file_path = os.path.join(self.log_dir, self.current_log_file)
                with open(log_file_path, 'w', encoding='utf-8') as f:
                    f.write("")
                self.load_log_content(self.current_log_file)
                messagebox.showinfo("成功", "日志已清空")
            except Exception as e:
                messagebox.showerror("错误", f"清空日志失败: {e}")
    
    def export_logs(self):
        """导出日志"""
        if not self.current_log_file:
            messagebox.showwarning("警告", "请先选择日志文件")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="导出日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 获取当前显示的日志
                items = self.log_tree.get_children()
                with open(file_path, 'w', encoding='utf-8') as f:
                    for item in items:
                        values = self.log_tree.item(item)['values']
                        line = f"{values[0]} - {values[2]} - {values[1]} - {values[3]}\n"
                        f.write(line)
                
                messagebox.showinfo("成功", f"日志已导出到: {file_path}")
                
            except Exception as e:
                messagebox.showerror("错误", f"导出日志失败: {e}")
    
    def toggle_auto_refresh(self):
        """切换自动刷新"""
        self.auto_refresh = self.auto_refresh_var.get()
        if self.auto_refresh:
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()
    
    def start_auto_refresh(self):
        """开始自动刷新"""
        if not self.auto_refresh:
            return
        
        self.refresh_thread = threading.Thread(target=self.auto_refresh_loop, daemon=True)
        self.refresh_thread.start()
    
    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.auto_refresh = False
    
    def auto_refresh_loop(self):
        """自动刷新循环"""
        while self.auto_refresh and self.window:
            try:
                time.sleep(5)  # 每5秒刷新一次
                if self.auto_refresh and self.current_log_file:
                    self.window.after(0, lambda: self.load_log_content(self.current_log_file))
            except Exception:
                break
    
    def on_closing(self):
        """窗口关闭事件"""
        self.stop_auto_refresh()
        self.window.destroy()
        self.window = None
