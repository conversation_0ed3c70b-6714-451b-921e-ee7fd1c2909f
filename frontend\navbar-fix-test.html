<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏布局修复测试</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        .test-info {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-controls {
            background: white;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-controls button:hover {
            background: #0056b3;
        }
        .width-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            z-index: 9999;
        }
        .layout-grid {
            position: absolute;
            top: 64px;
            left: 0;
            right: 0;
            height: 2px;
            background: repeating-linear-gradient(
                to right,
                transparent,
                transparent 99px,
                #ff0000 99px,
                #ff0000 100px
            );
            opacity: 0.3;
            pointer-events: none;
            z-index: 999;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-cloud"></i>
                <span>文件共享系统</span>
            </div>
            
            <div class="nav-search">
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" placeholder="搜索文件和文件夹...">
                    <div class="search-filters">
                        <button class="filter-btn active" data-type="all">
                            <i class="fas fa-globe"></i> 全部
                        </button>
                        <button class="filter-btn" data-type="image">
                            <i class="fas fa-image"></i> 图片
                        </button>
                        <button class="filter-btn" data-type="document">
                            <i class="fas fa-file-alt"></i> 文档
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="nav-actions">
                <button class="nav-btn" id="upload-btn">
                    <i class="fas fa-upload"></i>
                    <span>上传</span>
                </button>
                <button class="nav-btn" id="notifications-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" id="user-menu-btn">
                        <i class="fas fa-user"></i>
                    </button>
                    <div class="user-dropdown" id="user-dropdown">
                        <div class="user-info">
                            <div class="user-name">测试用户</div>
                            <div class="user-role">管理员</div>
                        </div>
                        <hr>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i> 设置
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-question-circle"></i> 帮助
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> 退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 布局网格线（用于调试） -->
    <div class="layout-grid" id="layout-grid" style="display: none;"></div>

    <!-- 测试信息 -->
    <div class="test-info">
        <h2>🔧 导航栏布局修复测试</h2>
        <p><strong>修复内容：</strong></p>
        <ul>
            <li>✅ 移除了导航容器的最大宽度限制</li>
            <li>✅ 设置了品牌logo和操作按钮的最小宽度</li>
            <li>✅ 添加了flex-shrink: 0防止压缩</li>
            <li>✅ 优化了搜索区域的最大宽度</li>
            <li>✅ 添加了响应式布局支持</li>
        </ul>
        <p><strong>预期效果：</strong>右侧按钮应该紧贴浏览器右边缘，左侧logo在左边缘，中间搜索框居中。</p>
    </div>

    <!-- 测试控制 -->
    <div class="test-controls">
        <h3>测试控制</h3>
        <button onclick="testDesktop()">桌面视图 (1200px)</button>
        <button onclick="testTablet()">平板视图 (768px)</button>
        <button onclick="testMobile()">移动视图 (375px)</button>
        <button onclick="toggleGrid()">切换网格线</button>
        <button onclick="testUserMenu()">测试用户菜单</button>
        <button onclick="testSearchFilters()">测试搜索过滤器</button>
        
        <div style="margin-top: 15px;">
            <h4>布局检查：</h4>
            <div id="layout-check">等待检查...</div>
        </div>
    </div>

    <!-- 宽度指示器 -->
    <div class="width-indicator" id="width-indicator">
        宽度: <span id="current-width">0</span>px
    </div>

    <script>
        // 更新宽度指示器
        function updateWidthIndicator() {
            const width = window.innerWidth;
            document.getElementById('current-width').textContent = width;
        }

        // 检查布局
        function checkLayout() {
            const navbar = document.querySelector('.navbar');
            const navContainer = document.querySelector('.nav-container');
            const navBrand = document.querySelector('.nav-brand');
            const navSearch = document.querySelector('.nav-search');
            const navActions = document.querySelector('.nav-actions');
            
            const containerRect = navContainer.getBoundingClientRect();
            const brandRect = navBrand.getBoundingClientRect();
            const searchRect = navSearch.getBoundingClientRect();
            const actionsRect = navActions.getBoundingClientRect();
            
            const checkResult = document.getElementById('layout-check');
            
            let result = '<div style="font-family: monospace; font-size: 12px;">';
            result += `容器宽度: ${Math.round(containerRect.width)}px<br>`;
            result += `品牌区域: ${Math.round(brandRect.left)}px - ${Math.round(brandRect.right)}px (宽度: ${Math.round(brandRect.width)}px)<br>`;
            result += `搜索区域: ${Math.round(searchRect.left)}px - ${Math.round(searchRect.right)}px (宽度: ${Math.round(searchRect.width)}px)<br>`;
            result += `操作区域: ${Math.round(actionsRect.left)}px - ${Math.round(actionsRect.right)}px (宽度: ${Math.round(actionsRect.width)}px)<br>`;
            
            // 检查右侧是否贴边
            const rightGap = window.innerWidth - actionsRect.right;
            const leftGap = brandRect.left;
            
            result += `<br><strong>边距检查:</strong><br>`;
            result += `左侧边距: ${Math.round(leftGap)}px ${leftGap < 30 ? '✅' : '❌'}<br>`;
            result += `右侧边距: ${Math.round(rightGap)}px ${rightGap < 30 ? '✅' : '❌'}<br>`;
            
            result += '</div>';
            checkResult.innerHTML = result;
        }

        // 测试不同屏幕尺寸
        function testDesktop() {
            document.body.style.width = '1200px';
            document.body.style.margin = '0 auto';
            setTimeout(checkLayout, 100);
        }

        function testTablet() {
            document.body.style.width = '768px';
            document.body.style.margin = '0 auto';
            setTimeout(checkLayout, 100);
        }

        function testMobile() {
            document.body.style.width = '375px';
            document.body.style.margin = '0 auto';
            setTimeout(checkLayout, 100);
        }

        // 切换网格线
        function toggleGrid() {
            const grid = document.getElementById('layout-grid');
            grid.style.display = grid.style.display === 'none' ? 'block' : 'none';
        }

        // 测试用户菜单
        function testUserMenu() {
            const userMenuBtn = document.getElementById('user-menu-btn');
            const userDropdown = document.getElementById('user-dropdown');
            
            userMenuBtn.click();
            
            setTimeout(() => {
                alert('用户菜单测试：' + (userDropdown.classList.contains('show') ? '✅ 正常显示' : '❌ 显示异常'));
            }, 100);
        }

        // 测试搜索过滤器
        function testSearchFilters() {
            const searchInput = document.getElementById('search-input');
            searchInput.focus();
            
            setTimeout(() => {
                const filters = document.querySelector('.search-filters');
                const isVisible = window.getComputedStyle(filters).opacity > 0;
                alert('搜索过滤器测试：' + (isVisible ? '✅ 正常显示' : '❌ 显示异常'));
                searchInput.blur();
            }, 100);
        }

        // 用户菜单交互
        document.getElementById('user-menu-btn').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('user-dropdown').classList.toggle('show');
        });

        document.addEventListener('click', function() {
            document.getElementById('user-dropdown').classList.remove('show');
        });

        // 窗口大小变化时更新
        window.addEventListener('resize', function() {
            updateWidthIndicator();
            setTimeout(checkLayout, 100);
        });

        // 初始化
        window.addEventListener('load', function() {
            updateWidthIndicator();
            setTimeout(checkLayout, 500);
        });

        // 定期检查布局
        setInterval(checkLayout, 2000);
    </script>
</body>
</html>
