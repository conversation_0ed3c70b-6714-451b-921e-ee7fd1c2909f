#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控服务
"""

import psutil
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from collections import defaultdict, deque

from utils.logger import setup_logger

class MonitoringService:
    """系统监控服务"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("MonitoringService")
        
        # 监控数据
        self.system_stats = {}
        self.user_activities = defaultdict(list)
        self.performance_metrics = deque(maxlen=1000)  # 保留最近1000个数据点
        
        # 在线用户
        self.online_users = {}
        
        # 监控线程
        self.monitoring_thread = None
        self.running = False
        
        # 启动监控
        self.start_monitoring()
    
    def start_monitoring(self):
        """启动监控"""
        if self.running:
            return
        
        self.running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        self.logger.info("监控服务已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        self.logger.info("监控服务已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 收集系统信息
                self._collect_system_stats()
                
                # 清理过期数据
                self._cleanup_expired_data()
                
                # 等待下一次收集
                time.sleep(10)  # 每10秒收集一次
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(5)
    
    def _collect_system_stats(self):
        """收集系统统计信息"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 网络统计
            network = psutil.net_io_counters()
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            # 更新系统统计
            self.system_stats = {
                'timestamp': datetime.now(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': psutil.cpu_count()
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'process': {
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'cpu_percent': process.cpu_percent()
                }
            }
            
            # 添加到性能指标历史
            self.performance_metrics.append({
                'timestamp': datetime.now(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': (disk.used / disk.total) * 100,
                'process_memory_mb': process_memory.rss / (1024 * 1024)
            })
            
        except Exception as e:
            self.logger.error(f"收集系统统计失败: {e}")
    
    def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            # 清理过期的用户活动记录（保留24小时）
            cutoff_time = datetime.now() - timedelta(hours=24)
            
            for user_id in list(self.user_activities.keys()):
                activities = self.user_activities[user_id]
                # 过滤掉过期的活动
                self.user_activities[user_id] = [
                    activity for activity in activities
                    if activity.get('timestamp', datetime.min) > cutoff_time
                ]
                
                # 如果没有活动记录，删除用户
                if not self.user_activities[user_id]:
                    del self.user_activities[user_id]
            
            # 清理离线用户
            offline_users = []
            for user_id, user_info in self.online_users.items():
                last_activity = user_info.get('last_activity', datetime.min)
                if datetime.now() - last_activity > timedelta(minutes=30):  # 30分钟无活动视为离线
                    offline_users.append(user_id)
            
            for user_id in offline_users:
                del self.online_users[user_id]
            
        except Exception as e:
            self.logger.error(f"清理过期数据失败: {e}")
    
    def record_user_activity(self, user_id: int, activity_type: str, 
                           details: Dict[str, Any] = None, ip_address: str = None):
        """记录用户活动"""
        try:
            activity = {
                'timestamp': datetime.now(),
                'type': activity_type,
                'details': details or {},
                'ip_address': ip_address
            }
            
            self.user_activities[user_id].append(activity)
            
            # 更新在线用户信息
            if user_id not in self.online_users:
                self.online_users[user_id] = {
                    'login_time': datetime.now(),
                    'ip_address': ip_address,
                    'activity_count': 0
                }
            
            self.online_users[user_id]['last_activity'] = datetime.now()
            self.online_users[user_id]['activity_count'] += 1
            
        except Exception as e:
            self.logger.error(f"记录用户活动失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            return {
                'system_stats': self.system_stats,
                'online_users_count': len(self.online_users),
                'total_activities': sum(len(activities) for activities in self.user_activities.values()),
                'uptime': self._get_system_uptime(),
                'status': 'running' if self.running else 'stopped'
            }
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {}
    
    def get_online_users(self) -> List[Dict[str, Any]]:
        """获取在线用户列表"""
        try:
            users_list = []
            for user_id, user_info in self.online_users.items():
                users_list.append({
                    'user_id': user_id,
                    'login_time': user_info['login_time'].isoformat(),
                    'last_activity': user_info['last_activity'].isoformat(),
                    'ip_address': user_info['ip_address'],
                    'activity_count': user_info['activity_count'],
                    'online_duration': str(datetime.now() - user_info['login_time'])
                })
            
            return users_list
        except Exception as e:
            self.logger.error(f"获取在线用户列表失败: {e}")
            return []
    
    def get_user_activities(self, user_id: int, limit: int = 100) -> List[Dict[str, Any]]:
        """获取用户活动记录"""
        try:
            activities = self.user_activities.get(user_id, [])
            # 按时间倒序排列，返回最新的活动
            sorted_activities = sorted(activities, key=lambda x: x['timestamp'], reverse=True)
            
            # 转换时间戳为字符串
            result = []
            for activity in sorted_activities[:limit]:
                activity_copy = activity.copy()
                activity_copy['timestamp'] = activity['timestamp'].isoformat()
                result.append(activity_copy)
            
            return result
        except Exception as e:
            self.logger.error(f"获取用户活动记录失败: {e}")
            return []
    
    def get_performance_metrics(self, hours: int = 1) -> List[Dict[str, Any]]:
        """获取性能指标"""
        try:
            # 过滤指定时间范围内的数据
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            filtered_metrics = [
                metric for metric in self.performance_metrics
                if metric['timestamp'] > cutoff_time
            ]
            
            # 转换时间戳为字符串
            result = []
            for metric in filtered_metrics:
                metric_copy = metric.copy()
                metric_copy['timestamp'] = metric['timestamp'].isoformat()
                result.append(metric_copy)
            
            return result
        except Exception as e:
            self.logger.error(f"获取性能指标失败: {e}")
            return []
    
    def get_activity_statistics(self) -> Dict[str, Any]:
        """获取活动统计"""
        try:
            # 统计不同类型的活动
            activity_types = defaultdict(int)
            total_activities = 0
            
            for activities in self.user_activities.values():
                for activity in activities:
                    activity_types[activity['type']] += 1
                    total_activities += 1
            
            # 统计最近24小时的活动
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_activities = 0
            
            for activities in self.user_activities.values():
                for activity in activities:
                    if activity['timestamp'] > recent_cutoff:
                        recent_activities += 1
            
            return {
                'total_activities': total_activities,
                'recent_activities_24h': recent_activities,
                'activity_types': dict(activity_types),
                'online_users': len(self.online_users),
                'active_users_24h': len([
                    user_id for user_id, activities in self.user_activities.items()
                    if any(activity['timestamp'] > recent_cutoff for activity in activities)
                ])
            }
        except Exception as e:
            self.logger.error(f"获取活动统计失败: {e}")
            return {}
    
    def _get_system_uptime(self) -> str:
        """获取系统运行时间"""
        try:
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = datetime.now() - boot_time
            return str(uptime)
        except Exception as e:
            return "未知"
    
    def check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        try:
            health_status = {
                'overall': 'healthy',
                'issues': [],
                'warnings': []
            }
            
            # 检查CPU使用率
            if self.system_stats.get('cpu', {}).get('percent', 0) > 90:
                health_status['issues'].append('CPU使用率过高')
                health_status['overall'] = 'critical'
            elif self.system_stats.get('cpu', {}).get('percent', 0) > 70:
                health_status['warnings'].append('CPU使用率较高')
                if health_status['overall'] == 'healthy':
                    health_status['overall'] = 'warning'
            
            # 检查内存使用率
            memory_percent = self.system_stats.get('memory', {}).get('percent', 0)
            if memory_percent > 90:
                health_status['issues'].append('内存使用率过高')
                health_status['overall'] = 'critical'
            elif memory_percent > 80:
                health_status['warnings'].append('内存使用率较高')
                if health_status['overall'] == 'healthy':
                    health_status['overall'] = 'warning'
            
            # 检查磁盘使用率
            disk_percent = self.system_stats.get('disk', {}).get('percent', 0)
            if disk_percent > 95:
                health_status['issues'].append('磁盘空间不足')
                health_status['overall'] = 'critical'
            elif disk_percent > 85:
                health_status['warnings'].append('磁盘空间较少')
                if health_status['overall'] == 'healthy':
                    health_status['overall'] = 'warning'
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"检查系统健康状态失败: {e}")
            return {
                'overall': 'unknown',
                'issues': ['无法获取系统状态'],
                'warnings': []
            }
