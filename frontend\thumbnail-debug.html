<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缩略图调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .thumbnail-test {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .thumbnail-item {
            width: 200px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 8px;
            text-align: center;
        }
        .thumbnail-container {
            width: 150px;
            height: 150px;
            margin: 0 auto 10px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
        }
        .thumbnail-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
        }
        .thumbnail-fallback {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 8px;
        }
        .thumbnail-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .loading-spinner-small {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>缩略图功能调试页面</h1>
        
        <div class="debug-section">
            <h3>1. 系统状态检查</h3>
            <button class="btn" onclick="checkSystemStatus()">检查系统状态</button>
            <div id="system-status"></div>
        </div>
        
        <div class="debug-section">
            <h3>2. 文件列表获取</h3>
            <button class="btn" onclick="loadFiles()">获取文件列表</button>
            <div id="file-list-status"></div>
            <div id="file-list"></div>
        </div>
        
        <div class="debug-section">
            <h3>3. 缩略图测试</h3>
            <button class="btn" onclick="testThumbnails()">测试缩略图</button>
            <div id="thumbnail-status"></div>
            <div id="thumbnail-test" class="thumbnail-test"></div>
        </div>
        
        <div class="debug-section">
            <h3>4. 调试日志</h3>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <div id="debug-log" class="log"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(logEntry);
        }
        
        function clearLog() {
            document.getElementById('debug-log').textContent = '';
        }
        
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function checkSystemStatus() {
            log('检查系统状态...');
            
            try {
                // 检查API服务器
                const response = await fetch('http://localhost:8086/api/system/info', {
                    headers: authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
                });
                
                if (response.status === 401) {
                    log('需要登录，尝试自动登录...');
                    await login();
                    return checkSystemStatus();
                }
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus('system-status', '✅ API服务器正常运行', 'success');
                    log(`API服务器状态: ${JSON.stringify(data)}`);
                } else {
                    showStatus('system-status', `❌ API服务器响应异常: ${response.status}`, 'error');
                    log(`API服务器错误: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                showStatus('system-status', `❌ 无法连接到API服务器: ${error.message}`, 'error');
                log(`连接错误: ${error.message}`, 'error');
            }
        }
        
        async function login() {
            log('尝试登录...');
            
            try {
                const response = await fetch('http://localhost:8086/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: '123456'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    log('✅ 登录成功');
                    return true;
                } else {
                    log(`❌ 登录失败: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 登录异常: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function loadFiles() {
            log('获取文件列表...');
            
            if (!authToken) {
                await login();
            }
            
            try {
                const response = await fetch('http://localhost:8086/api/files', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const files = data.files || [];
                    
                    showStatus('file-list-status', `✅ 获取到 ${files.length} 个文件`, 'success');
                    log(`文件列表: ${files.length} 个文件`);
                    
                    // 显示文件列表
                    const fileListDiv = document.getElementById('file-list');
                    fileListDiv.innerHTML = files.map(file => 
                        `<div>ID: ${file.id}, 名称: ${file.name}, 类型: ${file.type || 'file'}</div>`
                    ).join('');
                    
                    return files;
                } else {
                    showStatus('file-list-status', `❌ 获取文件列表失败: ${response.status}`, 'error');
                    log(`获取文件列表失败: ${response.status}`, 'error');
                    return [];
                }
            } catch (error) {
                showStatus('file-list-status', `❌ 获取文件列表异常: ${error.message}`, 'error');
                log(`获取文件列表异常: ${error.message}`, 'error');
                return [];
            }
        }
        
        async function testThumbnails() {
            log('开始测试缩略图...');
            
            const files = await loadFiles();
            const imageFiles = files.filter(file => {
                if (file.type === 'folder') return false;
                const ext = file.name.toLowerCase().split('.').pop();
                return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'psd', 'tif', 'ai', 'eps'].includes(ext);
            });
            
            if (imageFiles.length === 0) {
                showStatus('thumbnail-status', '⚠️ 没有找到图片文件', 'warning');
                log('没有找到图片文件进行测试', 'warning');
                return;
            }
            
            showStatus('thumbnail-status', `找到 ${imageFiles.length} 个图片文件，开始测试缩略图...`, 'info');
            log(`找到 ${imageFiles.length} 个图片文件`);
            
            const thumbnailTestDiv = document.getElementById('thumbnail-test');
            thumbnailTestDiv.innerHTML = '';
            
            // 测试前几个图片文件
            const testFiles = imageFiles.slice(0, 6);
            
            for (const file of testFiles) {
                await testSingleThumbnail(file, thumbnailTestDiv);
            }
        }
        
        async function testSingleThumbnail(file, container) {
            log(`测试文件: ${file.name} (ID: ${file.id})`);
            
            const thumbnailItem = document.createElement('div');
            thumbnailItem.className = 'thumbnail-item';
            thumbnailItem.innerHTML = `
                <div class="thumbnail-container" id="thumb-${file.id}">
                    <div class="thumbnail-loading">
                        <div class="loading-spinner-small"></div>
                    </div>
                </div>
                <div>${file.name}</div>
                <div id="status-${file.id}">加载中...</div>
            `;
            
            container.appendChild(thumbnailItem);
            
            // 请求缩略图
            const thumbnailUrl = `http://localhost:8086/api/files/${file.id}/thumbnail?size=medium`;
            log(`缩略图URL: ${thumbnailUrl}`);
            
            try {
                const response = await fetch(thumbnailUrl, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const imageUrl = URL.createObjectURL(blob);
                    
                    const img = document.createElement('img');
                    img.className = 'thumbnail-image';
                    img.src = imageUrl;
                    img.onload = () => {
                        document.getElementById(`thumb-${file.id}`).innerHTML = '';
                        document.getElementById(`thumb-${file.id}`).appendChild(img);
                        document.getElementById(`status-${file.id}`).innerHTML = '<span style="color: green;">✅ 成功</span>';
                        log(`✅ ${file.name} 缩略图加载成功`);
                    };
                    img.onerror = () => {
                        document.getElementById(`thumb-${file.id}`).innerHTML = '<div class="thumbnail-fallback">❌</div>';
                        document.getElementById(`status-${file.id}`).innerHTML = '<span style="color: red;">❌ 图片加载失败</span>';
                        log(`❌ ${file.name} 图片加载失败`, 'error');
                    };
                } else {
                    document.getElementById(`thumb-${file.id}`).innerHTML = '<div class="thumbnail-fallback">❌</div>';
                    document.getElementById(`status-${file.id}`).innerHTML = `<span style="color: red;">❌ HTTP ${response.status}</span>`;
                    log(`❌ ${file.name} 缩略图请求失败: ${response.status}`, 'error');
                }
            } catch (error) {
                document.getElementById(`thumb-${file.id}`).innerHTML = '<div class="thumbnail-fallback">❌</div>';
                document.getElementById(`status-${file.id}`).innerHTML = '<span style="color: red;">❌ 请求异常</span>';
                log(`❌ ${file.name} 缩略图请求异常: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查状态
        window.onload = function() {
            log('页面加载完成，开始初始化...');
            checkSystemStatus();
        };
    </script>
</body>
</html>
