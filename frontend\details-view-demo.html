<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详情视图演示 - 文件共享系统</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>详情视图横向布局演示</h1>
        <p>进入文件夹后，详情视图中每个图片文件占据一行，横向显示</p>
        
        <div class="file-list" id="demo-list">
            <table class="file-table">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>大小</th>
                        <th>类型</th>
                        <th>修改时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 图片文件1 -->
                    <tr class="file-item" data-file-id="1" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <div class="thumbnail-container">
                                    <img src="https://picsum.photos/60/60?random=1"
                                         alt="产品图片1.jpg"
                                         class="thumbnail-image">
                                </div>
                            </div>
                            <span class="file-name">产品图片1.jpg</span>
                        </td>
                        <td>2.5 MB</td>
                        <td>JPEG图片</td>
                        <td>2024-01-15 14:30</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" data-action="favorite" title="收藏">
                                    <i class="fas fa-star"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 图片文件2 -->
                    <tr class="file-item" data-file-id="2" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <div class="thumbnail-container">
                                    <img src="https://picsum.photos/60/60?random=2" 
                                         alt="设计稿.psd" 
                                         class="thumbnail-image">
                                </div>
                            </div>
                            <span class="file-name">设计稿.psd</span>
                        </td>
                        <td>15.8 MB</td>
                        <td>Photoshop文档</td>
                        <td>2024-01-15 16:45</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" data-action="favorite" title="收藏">
                                    <i class="fas fa-star"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 图片文件3 -->
                    <tr class="file-item" data-file-id="3" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <div class="thumbnail-container">
                                    <img src="https://picsum.photos/60/60?random=3" 
                                         alt="banner.png" 
                                         class="thumbnail-image">
                                </div>
                            </div>
                            <span class="file-name">banner.png</span>
                        </td>
                        <td>1.2 MB</td>
                        <td>PNG图片</td>
                        <td>2024-01-16 09:20</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" data-action="favorite" title="收藏">
                                    <i class="fas fa-star"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 图片文件4 -->
                    <tr class="file-item" data-file-id="4" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <div class="thumbnail-container">
                                    <img src="https://picsum.photos/60/60?random=4" 
                                         alt="logo.ai" 
                                         class="thumbnail-image">
                                </div>
                            </div>
                            <span class="file-name">logo.ai</span>
                        </td>
                        <td>3.7 MB</td>
                        <td>Illustrator文档</td>
                        <td>2024-01-16 11:15</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" data-action="favorite" title="收藏">
                                    <i class="fas fa-star"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 图片文件5 -->
                    <tr class="file-item" data-file-id="5" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <div class="thumbnail-container">
                                    <img src="https://picsum.photos/60/60?random=5" 
                                         alt="宣传海报.tif" 
                                         class="thumbnail-image">
                                </div>
                            </div>
                            <span class="file-name">宣传海报.tif</span>
                        </td>
                        <td>28.4 MB</td>
                        <td>TIFF图片</td>
                        <td>2024-01-16 14:30</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="action-btn" data-action="favorite" title="收藏">
                                    <i class="fas fa-star"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="description">
            <h3>详情视图特点：</h3>
            <ul>
                <li>每个图片文件占据一行，横向布局</li>
                <li>左侧显示60x60px的缩略图预览</li>
                <li>文件名、大小、类型、时间信息清晰排列</li>
                <li>悬停效果：整行高亮，操作按钮显示</li>
                <li>操作按钮：下载、预览、收藏</li>
                <li>响应式设计，适配不同屏幕尺寸</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const actionBtns = document.querySelectorAll('.action-btn');
            
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.dataset.action;
                    const fileName = this.closest('tr').querySelector('.file-name').textContent;
                    alert(`${action}: ${fileName}`);
                });
            });
        });
    </script>
</body>
</html>
