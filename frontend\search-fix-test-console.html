<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索修复测试控制台</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .test-panel {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .test-panel h3 {
            margin-top: 0;
            color: #4a5568;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .test-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .test-btn.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .test-btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .test-btn.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .console-panel {
            background: #1a202c;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: #e2e8f0;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #2d3748;
        }
        
        .console-line {
            margin-bottom: 8px;
            padding: 4px 0;
        }
        
        .console-line.info {
            color: #63b3ed;
        }
        
        .console-line.success {
            color: #68d391;
        }
        
        .console-line.warning {
            color: #fbb6ce;
        }
        
        .console-line.error {
            color: #fc8181;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .status-card {
            background: #f7fafc;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #4299e1;
        }
        
        .status-card h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
            font-size: 14px;
            font-weight: 600;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #4299e1;
        }
        
        .instructions {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .instructions h4 {
            margin-top: 0;
            font-size: 18px;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .clear-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
        }
        
        .clear-btn:hover {
            background: #c53030;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 搜索修复测试控制台</h1>
            <p>测试搜索结果页面视图切换修复效果</p>
        </div>
        
        <div class="instructions">
            <h4>📋 使用说明</h4>
            <ol>
                <li>确保主页面 (index.html) 已在另一个标签页中打开</li>
                <li>在主页面中进行搜索操作</li>
                <li>回到此页面，点击"检查当前状态"查看搜索状态</li>
                <li>点击"运行完整测试"验证修复效果</li>
                <li>观察控制台输出了解详细测试结果</li>
            </ol>
        </div>
        
        <div class="test-panel">
            <h3>🎮 测试控制</h3>
            <div class="button-grid">
                <button class="test-btn primary" onclick="runFullTest()">运行完整测试</button>
                <button class="test-btn info" onclick="checkStatus()">检查当前状态</button>
                <button class="test-btn success" onclick="simulateSearch()">模拟搜索</button>
                <button class="test-btn warning" onclick="clearTestState()">清除测试状态</button>
            </div>
        </div>
        
        <div class="test-panel">
            <h3>📊 系统状态</h3>
            <div class="status-grid">
                <div class="status-card">
                    <h4>文件管理器搜索模式</h4>
                    <div class="status-value" id="fm-search-mode">未知</div>
                </div>
                <div class="status-card">
                    <h4>搜索管理器搜索模式</h4>
                    <div class="status-value" id="sm-search-mode">未知</div>
                </div>
                <div class="status-card">
                    <h4>当前文件数量</h4>
                    <div class="status-value" id="current-files">未知</div>
                </div>
                <div class="status-card">
                    <h4>搜索结果数量</h4>
                    <div class="status-value" id="search-results">未知</div>
                </div>
                <div class="status-card">
                    <h4>当前视图模式</h4>
                    <div class="status-value" id="view-mode">未知</div>
                </div>
                <div class="status-card">
                    <h4>测试状态</h4>
                    <div class="status-value" id="test-status">待测试</div>
                </div>
            </div>
        </div>
        
        <div class="test-panel">
            <h3>💻 控制台输出</h3>
            <div class="console-panel" id="console-output">
                <div class="console-line info">[INFO] 测试控制台已加载</div>
            </div>
            <button class="clear-btn" onclick="clearConsole()">清空控制台</button>
        </div>
    </div>

    <script>
        let consoleLineCount = 0;
        
        function log(level, message) {
            const console = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const line = document.createElement('div');
            line.className = `console-line ${level}`;
            line.textContent = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
            
            consoleLineCount++;
            if (consoleLineCount > 100) {
                const firstLine = console.firstElementChild;
                if (firstLine) firstLine.remove();
                consoleLineCount--;
            }
        }
        
        function updateStatus(id, value, color = '#4299e1') {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                element.style.color = color;
            }
        }
        
        function getMainPageObjects() {
            // 尝试从不同的窗口获取对象
            let fileManager = null;
            let searchManager = null;
            
            // 尝试从当前窗口
            if (window.fileManager) {
                fileManager = window.fileManager;
            }
            if (window.searchManager) {
                searchManager = window.searchManager;
            }
            
            // 尝试从父窗口
            if (!fileManager && window.parent && window.parent.fileManager) {
                fileManager = window.parent.fileManager;
            }
            if (!searchManager && window.parent && window.parent.searchManager) {
                searchManager = window.parent.searchManager;
            }
            
            // 尝试从其他窗口（通过 window.open 打开的）
            if (!fileManager || !searchManager) {
                try {
                    // 这里可能需要用户手动设置引用
                    log('warning', '无法自动获取主页面对象，请确保主页面已打开');
                } catch (e) {
                    log('error', '跨窗口访问受限');
                }
            }
            
            return { fileManager, searchManager };
        }
        
        function checkStatus() {
            log('info', '检查系统状态...');
            
            const { fileManager, searchManager } = getMainPageObjects();
            
            if (fileManager) {
                updateStatus('fm-search-mode', fileManager.isInSearchMode ? '是' : '否', 
                    fileManager.isInSearchMode ? '#68d391' : '#fc8181');
                updateStatus('current-files', fileManager.files?.length || 0);
                updateStatus('view-mode', fileManager.viewMode || '未知');
                
                log('success', `文件管理器状态: 搜索模式=${fileManager.isInSearchMode}, 文件数=${fileManager.files?.length || 0}`);
            } else {
                updateStatus('fm-search-mode', '无法访问', '#fc8181');
                updateStatus('current-files', '无法访问', '#fc8181');
                updateStatus('view-mode', '无法访问', '#fc8181');
                log('error', '无法访问文件管理器对象');
            }
            
            if (searchManager) {
                updateStatus('sm-search-mode', searchManager.isInSearchMode ? '是' : '否',
                    searchManager.isInSearchMode ? '#68d391' : '#fc8181');
                updateStatus('search-results', searchManager.searchResults?.length || 0);
                
                log('success', `搜索管理器状态: 搜索模式=${searchManager.isInSearchMode}, 结果数=${searchManager.searchResults?.length || 0}`);
            } else {
                updateStatus('sm-search-mode', '无法访问', '#fc8181');
                updateStatus('search-results', '无法访问', '#fc8181');
                log('error', '无法访问搜索管理器对象');
            }
        }
        
        function runFullTest() {
            log('info', '开始运行完整测试...');
            updateStatus('test-status', '测试中...', '#fbb6ce');
            
            const { fileManager, searchManager } = getMainPageObjects();
            
            if (!fileManager || !searchManager) {
                log('error', '无法获取必要对象，测试失败');
                updateStatus('test-status', '测试失败', '#fc8181');
                return;
            }
            
            // 模拟搜索状态
            const mockResults = [
                { id: 'test1', name: 'test_1.jpg', type: 'file', size: 1024 },
                { id: 'test2', name: 'test_2.png', type: 'file', size: 2048 },
                { id: 'test3', name: 'test_3.psd', type: 'file', size: 4096 }
            ];
            
            fileManager.isInSearchMode = true;
            fileManager.searchResults = mockResults;
            fileManager.files = mockResults;
            
            log('info', '设置模拟搜索状态完成');
            
            // 测试视图切换
            const viewModes = ['large-icons', 'medium-icons', 'small-icons', 'extra-large-icons'];
            let passed = 0;
            
            viewModes.forEach((mode, index) => {
                log('info', `测试视图切换: ${mode}`);
                
                const beforeFiles = fileManager.files.length;
                const beforeSearchMode = fileManager.isInSearchMode;
                
                fileManager.setViewMode(mode);
                
                const afterFiles = fileManager.files.length;
                const afterSearchMode = fileManager.isInSearchMode;
                
                if (beforeFiles === afterFiles && beforeSearchMode === afterSearchMode) {
                    log('success', `${mode} 测试通过`);
                    passed++;
                } else {
                    log('error', `${mode} 测试失败: 文件数 ${beforeFiles}->${afterFiles}, 搜索模式 ${beforeSearchMode}->${afterSearchMode}`);
                }
            });
            
            const success = passed === viewModes.length;
            updateStatus('test-status', success ? '测试通过' : '测试失败', 
                success ? '#68d391' : '#fc8181');
            
            log(success ? 'success' : 'error', 
                `测试完成: ${passed}/${viewModes.length} 通过`);
            
            checkStatus();
        }
        
        function simulateSearch() {
            log('info', '模拟搜索操作...');
            
            const { fileManager, searchManager } = getMainPageObjects();
            
            if (!fileManager || !searchManager) {
                log('error', '无法获取必要对象');
                return;
            }
            
            const mockResults = [
                { id: 'sim1', name: 'simulated_1.jpg', type: 'file', size: 1024 },
                { id: 'sim2', name: 'simulated_2.png', type: 'file', size: 2048 }
            ];
            
            searchManager.currentQuery = 'simulated';
            searchManager.searchResults = mockResults;
            searchManager.isInSearchMode = true;
            
            fileManager.isInSearchMode = true;
            fileManager.searchResults = mockResults;
            fileManager.files = mockResults;
            
            log('success', `模拟搜索完成，找到 ${mockResults.length} 个结果`);
            checkStatus();
        }
        
        function clearTestState() {
            log('info', '清除测试状态...');
            
            const { fileManager, searchManager } = getMainPageObjects();
            
            if (fileManager) {
                fileManager.isInSearchMode = false;
                fileManager.searchResults = [];
            }
            
            if (searchManager) {
                searchManager.isInSearchMode = false;
                searchManager.searchResults = [];
                searchManager.currentQuery = '';
            }
            
            updateStatus('test-status', '已清除', '#68d391');
            log('success', '测试状态已清除');
            checkStatus();
        }
        
        function clearConsole() {
            const console = document.getElementById('console-output');
            console.innerHTML = '<div class="console-line info">[INFO] 控制台已清空</div>';
            consoleLineCount = 1;
        }
        
        // 定期更新状态
        setInterval(checkStatus, 3000);
        
        // 初始检查
        setTimeout(checkStatus, 1000);
        
        log('success', '测试控制台已就绪');
    </script>
</body>
</html>
