#!/usr/bin/env python3
"""
简化的收藏功能服务 - 直接使用SQL操作
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import pymysql


class SimpleFavoriteService:
    """简化的收藏功能服务"""
    
    def __init__(self, db_config=None):
        self.logger = logging.getLogger(__name__)
        self.db_config = db_config or {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'database': 'file_share_system',
            'charset': 'utf8mb4'
        }
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            return pymysql.connect(**self.db_config)
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def add_favorite(self, user_id: int, file_id: int, notes: str = None) -> Dict[str, Any]:
        """添加收藏"""
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    # 检查文件是否存在
                    cursor.execute("SELECT id, filename FROM shared_files WHERE id = %s", (file_id,))
                    file_record = cursor.fetchone()
                    if not file_record:
                        return {"success": False, "error": "文件不存在"}
                    
                    # 检查是否已经收藏
                    cursor.execute("""
                        SELECT id FROM user_favorites 
                        WHERE user_id = %s AND file_id = %s AND is_active = TRUE
                    """, (user_id, file_id))
                    
                    if cursor.fetchone():
                        return {"success": False, "error": "文件已收藏"}
                    
                    # 创建收藏记录
                    now = datetime.now()
                    cursor.execute("""
                        INSERT INTO user_favorites (user_id, file_id, favorited_at, notes, is_active, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, TRUE, %s, %s)
                    """, (user_id, file_id, now, notes, now, now))
                    
                    favorite_id = cursor.lastrowid
                    connection.commit()
                    
                    self.logger.info(f"用户 {user_id} 收藏文件 {file_id}")
                    
                    return {
                        "success": True,
                        "data": {
                            "id": favorite_id,
                            "user_id": user_id,
                            "file_id": file_id,
                            "favorited_at": now.isoformat(),
                            "notes": notes
                        }
                    }
                    
        except Exception as e:
            self.logger.error(f"添加收藏失败: {e}")
            return {"success": False, "error": str(e)}
    
    def remove_favorite(self, user_id: int, file_id: int) -> Dict[str, Any]:
        """取消收藏"""
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    # 软删除：标记为无效
                    cursor.execute("""
                        UPDATE user_favorites 
                        SET is_active = FALSE, updated_at = %s
                        WHERE user_id = %s AND file_id = %s AND is_active = TRUE
                    """, (datetime.now(), user_id, file_id))
                    
                    if cursor.rowcount == 0:
                        return {"success": False, "error": "收藏记录不存在"}
                    
                    connection.commit()
                    
                    self.logger.info(f"用户 {user_id} 取消收藏文件 {file_id}")
                    
                    return {"success": True}
                    
        except Exception as e:
            self.logger.error(f"取消收藏失败: {e}")
            return {"success": False, "error": str(e)}
    
    def toggle_favorite(self, user_id: int, file_id: int, notes: str = None) -> Dict[str, Any]:
        """切换收藏状态"""
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    # 检查当前收藏状态
                    cursor.execute("""
                        SELECT id FROM user_favorites 
                        WHERE user_id = %s AND file_id = %s AND is_active = TRUE
                    """, (user_id, file_id))
                    
                    favorite = cursor.fetchone()
                    
                    if favorite:
                        # 已收藏，取消收藏
                        cursor.execute("""
                            UPDATE user_favorites 
                            SET is_active = FALSE, updated_at = %s
                            WHERE user_id = %s AND file_id = %s AND is_active = TRUE
                        """, (datetime.now(), user_id, file_id))
                        
                        connection.commit()
                        
                        self.logger.info(f"用户 {user_id} 取消收藏文件 {file_id}")
                        return {
                            "success": True,
                            "action": "removed",
                            "is_favorited": False
                        }
                    else:
                        # 未收藏，添加收藏
                        # 检查文件是否存在
                        cursor.execute("SELECT id FROM shared_files WHERE id = %s", (file_id,))
                        if not cursor.fetchone():
                            return {"success": False, "error": "文件不存在"}
                        
                        # 检查是否有历史收藏记录（可能被软删除）
                        cursor.execute("""
                            SELECT id FROM user_favorites 
                            WHERE user_id = %s AND file_id = %s
                        """, (user_id, file_id))
                        
                        old_favorite = cursor.fetchone()
                        now = datetime.now()
                        
                        if old_favorite:
                            # 恢复历史记录
                            cursor.execute("""
                                UPDATE user_favorites 
                                SET is_active = TRUE, favorited_at = %s, updated_at = %s, notes = %s
                                WHERE user_id = %s AND file_id = %s
                            """, (now, now, notes, user_id, file_id))
                        else:
                            # 创建新记录
                            cursor.execute("""
                                INSERT INTO user_favorites (user_id, file_id, favorited_at, notes, is_active, created_at, updated_at)
                                VALUES (%s, %s, %s, %s, TRUE, %s, %s)
                            """, (user_id, file_id, now, notes, now, now))
                        
                        connection.commit()
                        
                        self.logger.info(f"用户 {user_id} 收藏文件 {file_id}")
                        return {
                            "success": True,
                            "action": "added",
                            "is_favorited": True,
                            "data": {
                                "user_id": user_id,
                                "file_id": file_id,
                                "favorited_at": now.isoformat(),
                                "notes": notes
                            }
                        }
                        
        except Exception as e:
            self.logger.error(f"切换收藏状态失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_user_favorites(self, user_id: int, page: int = 1, page_size: int = 50, 
                          folder_id: int = None) -> Dict[str, Any]:
        """获取用户收藏列表"""
        try:
            with self.get_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 构建查询 - 移除is_image过滤，显示所有收藏
                    where_clause = "uf.user_id = %s AND uf.is_active = TRUE"
                    params = [user_id]
                    
                    if folder_id:
                        where_clause += " AND sf.folder_id = %s"
                        params.append(folder_id)
                    
                    # 计算总数
                    count_sql = f"""
                        SELECT COUNT(*) as total
                        FROM user_favorites uf
                        JOIN shared_files sf ON uf.file_id = sf.id
                        WHERE {where_clause}
                    """
                    cursor.execute(count_sql, params)
                    total_count = cursor.fetchone()['total']
                    
                    # 分页查询
                    offset = (page - 1) * page_size
                    query_sql = f"""
                        SELECT 
                            uf.id, uf.user_id, uf.file_id, uf.favorited_at, uf.notes,
                            sf.filename, sf.relative_path, sf.file_size, sf.mime_type,
                            sf.is_image, sf.has_thumbnail, sf.thumbnail_path, sf.folder_id,
                            sfo.name as folder_name
                        FROM user_favorites uf
                        JOIN shared_files sf ON uf.file_id = sf.id
                        LEFT JOIN shared_folders sfo ON sf.folder_id = sfo.id
                        WHERE {where_clause}
                        ORDER BY uf.favorited_at DESC
                        LIMIT %s OFFSET %s
                    """
                    
                    cursor.execute(query_sql, params + [page_size, offset])
                    favorites = cursor.fetchall()
                    
                    # 转换数据格式
                    favorite_list = []
                    for fav in favorites:
                        favorite_list.append({
                            "id": fav["id"],
                            "user_id": fav["user_id"],
                            "file_id": fav["file_id"],
                            "favorited_at": fav["favorited_at"].isoformat() if fav["favorited_at"] else None,
                            "notes": fav["notes"],
                            "file": {
                                "id": fav["file_id"],
                                "filename": fav["filename"],
                                "relative_path": fav["relative_path"],
                                "file_size": fav["file_size"],
                                "mime_type": fav["mime_type"],
                                "is_image": fav["is_image"],
                                "has_thumbnail": fav["has_thumbnail"],
                                "thumbnail_path": fav["thumbnail_path"],
                                "folder_id": fav["folder_id"],
                                "folder_name": fav["folder_name"]
                            }
                        })
                    
                    return {
                        "success": True,
                        "data": {
                            "favorites": favorite_list,
                            "total_count": total_count,
                            "page": page,
                            "page_size": page_size,
                            "total_pages": (total_count + page_size - 1) // page_size
                        }
                    }
                    
        except Exception as e:
            self.logger.error(f"获取用户收藏列表失败: {e}")
            return {"success": False, "error": str(e)}
    
    def check_favorite_status(self, user_id: int, file_ids: List[int]) -> Dict[str, Any]:
        """批量检查文件收藏状态"""
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    if not file_ids:
                        return {"success": True, "data": {}}
                    
                    # 构建IN查询
                    placeholders = ','.join(['%s'] * len(file_ids))
                    query = f"""
                        SELECT file_id FROM user_favorites 
                        WHERE user_id = %s AND file_id IN ({placeholders}) AND is_active = TRUE
                    """
                    
                    cursor.execute(query, [user_id] + file_ids)
                    favorited_files = {row[0] for row in cursor.fetchall()}
                    
                    # 构建状态字典
                    status_dict = {}
                    for file_id in file_ids:
                        status_dict[str(file_id)] = file_id in favorited_files
                    
                    return {
                        "success": True,
                        "data": status_dict
                    }
                    
        except Exception as e:
            self.logger.error(f"检查收藏状态失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_favorite_statistics(self, user_id: int) -> Dict[str, Any]:
        """获取收藏统计信息"""
        try:
            with self.get_connection() as connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    # 总收藏数
                    cursor.execute("""
                        SELECT COUNT(*) as total FROM user_favorites 
                        WHERE user_id = %s AND is_active = TRUE
                    """, (user_id,))
                    total_count = cursor.fetchone()['total']
                    
                    # 按文件夹分组统计
                    cursor.execute("""
                        SELECT sf.folder_id, COUNT(uf.id) as count
                        FROM user_favorites uf
                        JOIN shared_files sf ON uf.file_id = sf.id
                        WHERE uf.user_id = %s AND uf.is_active = TRUE
                        GROUP BY sf.folder_id
                    """, (user_id,))
                    folder_stats = cursor.fetchall()
                    
                    # 最近收藏（最近7天）
                    cursor.execute("""
                        SELECT COUNT(*) as recent FROM user_favorites 
                        WHERE user_id = %s AND is_active = TRUE 
                        AND favorited_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                    """, (user_id,))
                    recent_count = cursor.fetchone()['recent']
                    
                    return {
                        "success": True,
                        "data": {
                            "total_count": total_count,
                            "recent_count": recent_count,
                            "folder_stats": [
                                {"folder_id": stat["folder_id"], "count": stat["count"]}
                                for stat in folder_stats
                            ]
                        }
                    }
                    
        except Exception as e:
            self.logger.error(f"获取收藏统计失败: {e}")
            return {"success": False, "error": str(e)}
