#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
活动日志模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, ForeignKey
from sqlalchemy.sql import func
from config.database import Base
from datetime import datetime
from typing import Dict, Any, Optional

class ActivityLog(Base):
    """活动日志模型"""
    
    __tablename__ = 'activity_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=True, comment='用户ID')
    
    # 活动信息
    action = Column(String(100), nullable=False, comment='操作类型')
    details = Column(JSON, nullable=True, comment='详细信息')
    
    # 网络信息
    ip_address = Column(String(45), nullable=True, comment='IP地址')
    user_agent = Column(Text, nullable=True, comment='用户代理')
    
    # 结果信息
    success = Column(String(10), default='success', comment='操作结果')
    error_message = Column(Text, nullable=True, comment='错误信息')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    
    def __init__(self, user_id: Optional[int], action: str, **kwargs):
        self.user_id = user_id
        self.action = action
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'action': self.action,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'success': self.success,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def create_log(cls, user_id: Optional[int], action: str, 
                   details: Dict[str, Any] = None, **kwargs):
        """创建日志记录"""
        return cls(
            user_id=user_id,
            action=action,
            details=details or {},
            **kwargs
        )
    
    def __repr__(self):
        return f"<ActivityLog(id={self.id}, action='{self.action}', user_id={self.user_id})>"
