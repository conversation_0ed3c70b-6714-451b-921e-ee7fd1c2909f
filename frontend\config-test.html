<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .config-section {
            margin-bottom: 30px;
        }
        .config-section h3 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .config-label {
            font-weight: bold;
            color: #555;
        }
        .config-value {
            color: #007bff;
            font-family: monospace;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API配置测试页面</h1>
        <p>此页面用于测试API配置功能的实现</p>
        
        <div class="controls">
            <button class="btn" onclick="loadPublicConfig()">获取公开配置</button>
            <button class="btn" onclick="loadSystemConfig()">获取系统配置（需管理员权限）</button>
            <button class="btn btn-success" onclick="testConfigUpdate()">测试配置更新</button>
        </div>
        
        <div id="status"></div>
    </div>

    <div class="container" id="config-display" style="display: none;">
        <h2>📋 当前配置</h2>
        <div id="config-content"></div>
    </div>

    <div class="container" id="raw-response" style="display: none;">
        <h2>📄 原始响应</h2>
        <pre id="raw-content"></pre>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    
    <script>
        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 显示配置信息
        function displayConfig(config, title = '配置信息') {
            const configDisplay = document.getElementById('config-display');
            const configContent = document.getElementById('config-content');
            
            let html = `<h3>${title}</h3>`;
            
            // 递归显示配置
            function renderConfig(obj, prefix = '') {
                let result = '';
                for (const [key, value] of Object.entries(obj)) {
                    const fullKey = prefix ? `${prefix}.${key}` : key;
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        result += `<div class="config-section">
                            <h4>${key}</h4>
                            ${renderConfig(value, fullKey)}
                        </div>`;
                    } else {
                        result += `<div class="config-item">
                            <span class="config-label">${key}:</span>
                            <span class="config-value">${JSON.stringify(value)}</span>
                        </div>`;
                    }
                }
                return result;
            }
            
            html += renderConfig(config);
            configContent.innerHTML = html;
            configDisplay.style.display = 'block';
        }

        // 显示原始响应
        function displayRawResponse(response) {
            const rawResponse = document.getElementById('raw-response');
            const rawContent = document.getElementById('raw-content');
            
            rawContent.textContent = JSON.stringify(response, null, 2);
            rawResponse.style.display = 'block';
        }

        // 获取公开配置
        async function loadPublicConfig() {
            try {
                showStatus('正在获取公开配置...', 'info');
                const response = await ConfigAPI.getPublicConfig();
                
                showStatus('公开配置获取成功！', 'success');
                displayConfig(response, '公开配置');
                displayRawResponse(response);
                
            } catch (error) {
                showStatus(`获取公开配置失败: ${error.message}`, 'error');
                console.error('获取公开配置失败:', error);
            }
        }

        // 获取系统配置
        async function loadSystemConfig() {
            try {
                showStatus('正在获取系统配置...', 'info');
                const response = await ConfigAPI.getSystemConfig();
                
                showStatus('系统配置获取成功！', 'success');
                displayConfig(response, '系统配置（管理员）');
                displayRawResponse(response);
                
            } catch (error) {
                showStatus(`获取系统配置失败: ${error.message}`, 'error');
                console.error('获取系统配置失败:', error);
            }
        }

        // 测试配置更新
        async function testConfigUpdate() {
            try {
                showStatus('正在测试配置更新...', 'info');
                
                // 测试配置数据
                const testConfig = {
                    'search.max_search_results': 500,
                    'download.max_batch_files': 150,
                    'notifications.notification_duration': 3000
                };
                
                const response = await ConfigAPI.updateSystemConfig(testConfig);
                
                showStatus('配置更新测试成功！', 'success');
                displayRawResponse(response);
                
                if (response.restart_required) {
                    showStatus('注意：此配置变更需要重启服务器才能生效', 'info');
                }
                
            } catch (error) {
                showStatus(`配置更新测试失败: ${error.message}`, 'error');
                console.error('配置更新测试失败:', error);
            }
        }

        // 页面加载完成后自动获取公开配置
        document.addEventListener('DOMContentLoaded', () => {
            showStatus('页面加载完成，可以开始测试配置功能', 'info');
        });
    </script>
</body>
</html>
