#!/usr/bin/env python3
"""
收藏功能数据库迁移脚本
将现有的localStorage收藏数据迁移到数据库
"""

import sys
import os
import json
import pymysql
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def migrate_favorites_to_database():
    """迁移收藏数据到数据库"""
    print("收藏功能数据库迁移工具")
    print("=" * 50)
    
    # 数据库连接配置
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '123456',
        'database': 'file_share_system',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        print("连接数据库...")
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 检查收藏表是否存在
        cursor.execute("SHOW TABLES LIKE 'user_favorites'")
        if not cursor.fetchone():
            print("❌ 收藏表不存在，请先运行数据库初始化脚本")
            return False
        
        # 检查是否已有收藏数据
        cursor.execute("SELECT COUNT(*) FROM user_favorites")
        existing_count = cursor.fetchone()[0]
        
        if existing_count > 0:
            print(f"⚠️ 数据库中已有 {existing_count} 条收藏记录")
            choice = input("是否继续迁移？这可能会创建重复数据 (y/N): ")
            if choice.lower() != 'y':
                print("迁移已取消")
                return False
        
        # 模拟从localStorage读取的收藏数据
        # 在实际使用中，这些数据应该从前端导出
        sample_favorites = [
            {
                "id": 1,
                "name": "sample1.jpg",
                "type": "file",
                "size": 1024000,
                "modified_at": "2024-01-01T10:00:00Z",
                "favorited_at": "2024-01-01T12:00:00Z",
                "folder_id": 1,
                "folder_name": "示例文件夹"
            },
            {
                "id": 2,
                "name": "sample2.png",
                "type": "file",
                "size": 2048000,
                "modified_at": "2024-01-02T10:00:00Z",
                "favorited_at": "2024-01-02T12:00:00Z",
                "folder_id": 1,
                "folder_name": "示例文件夹"
            }
        ]
        
        print("\n📋 示例收藏数据:")
        for fav in sample_favorites:
            print(f"  - {fav['name']} (ID: {fav['id']})")
        
        print(f"\n准备迁移 {len(sample_favorites)} 条收藏记录...")
        
        # 获取默认用户ID（假设为admin用户）
        cursor.execute("SELECT id FROM users WHERE username = 'admin' LIMIT 1")
        user_result = cursor.fetchone()
        
        if not user_result:
            print("❌ 未找到默认用户，请先创建用户")
            return False
        
        user_id = user_result[0]
        print(f"使用用户ID: {user_id}")
        
        # 开始迁移
        migrated_count = 0
        skipped_count = 0
        
        for favorite in sample_favorites:
            try:
                file_id = favorite['id']
                
                # 检查文件是否存在
                cursor.execute("SELECT id FROM shared_files WHERE id = %s", (file_id,))
                if not cursor.fetchone():
                    print(f"⚠️ 跳过文件 {favorite['name']} (ID: {file_id}) - 文件不存在")
                    skipped_count += 1
                    continue
                
                # 检查是否已经收藏
                cursor.execute(
                    "SELECT id FROM user_favorites WHERE user_id = %s AND file_id = %s",
                    (user_id, file_id)
                )
                
                if cursor.fetchone():
                    print(f"⚠️ 跳过文件 {favorite['name']} (ID: {file_id}) - 已收藏")
                    skipped_count += 1
                    continue
                
                # 插入收藏记录
                favorited_at = datetime.fromisoformat(favorite['favorited_at'].replace('Z', '+00:00'))
                
                cursor.execute("""
                    INSERT INTO user_favorites (user_id, file_id, favorited_at, notes, is_active)
                    VALUES (%s, %s, %s, %s, %s)
                """, (
                    user_id,
                    file_id,
                    favorited_at,
                    f"从localStorage迁移 - 原文件夹: {favorite.get('folder_name', '未知')}",
                    True
                ))
                
                migrated_count += 1
                print(f"✅ 迁移成功: {favorite['name']} (ID: {file_id})")
                
            except Exception as e:
                print(f"❌ 迁移失败: {favorite['name']} - {e}")
                skipped_count += 1
        
        # 提交事务
        connection.commit()
        
        print(f"\n📊 迁移完成:")
        print(f"  - 成功迁移: {migrated_count} 条")
        print(f"  - 跳过: {skipped_count} 条")
        print(f"  - 总计: {migrated_count + skipped_count} 条")
        
        # 验证迁移结果
        cursor.execute("SELECT COUNT(*) FROM user_favorites WHERE user_id = %s", (user_id,))
        total_favorites = cursor.fetchone()[0]
        print(f"  - 用户总收藏数: {total_favorites} 条")
        
        connection.close()
        
        if migrated_count > 0:
            print("\n✅ 收藏数据迁移成功！")
            print("\n💡 迁移后建议:")
            print("1. 清除前端localStorage中的收藏数据")
            print("2. 重启前端应用以使用新的数据库收藏功能")
            print("3. 测试收藏功能是否正常工作")
        else:
            print("\n⚠️ 没有新数据被迁移")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

def create_sample_migration_data():
    """创建示例迁移数据文件"""
    sample_data = {
        "favorites": [
            {
                "id": 1,
                "name": "design.psd",
                "type": "file",
                "size": 10485760,
                "modified_at": "2024-01-01T10:00:00Z",
                "favorited_at": "2024-01-01T12:00:00Z",
                "folder_id": 1,
                "folder_name": "设计文件"
            },
            {
                "id": 2,
                "name": "photo.jpg",
                "type": "file",
                "size": 2048000,
                "modified_at": "2024-01-02T10:00:00Z",
                "favorited_at": "2024-01-02T12:00:00Z",
                "folder_id": 2,
                "folder_name": "照片"
            }
        ]
    }
    
    with open('favorites_export.json', 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("✅ 示例迁移数据文件已创建: favorites_export.json")
    print("💡 您可以修改此文件，然后使用它进行迁移")

def main():
    """主函数"""
    print("收藏功能数据库迁移工具")
    print("=" * 50)
    
    choice = input("选择操作:\n1. 执行迁移\n2. 创建示例数据文件\n请输入选择 (1/2): ")
    
    if choice == '1':
        if migrate_favorites_to_database():
            return 0
        else:
            return 1
    elif choice == '2':
        create_sample_migration_data()
        return 0
    else:
        print("无效选择")
        return 1

if __name__ == "__main__":
    sys.exit(main())
