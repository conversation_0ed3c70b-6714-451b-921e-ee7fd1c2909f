<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索框布局测试</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        .test-container {
            padding: 2rem;
            background: #f5f5f5;
            min-height: 100vh;
        }
        .test-section {
            background: white;
            padding: 1rem;
            margin-bottom: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        .layout-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 4px;
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <!-- 导航栏测试 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-cloud"></i>
                <span>文件共享系统</span>
            </div>
            
            <div class="nav-search">
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" placeholder="搜索图片文件...">
                    <div class="search-type-indicator">
                        <i class="fas fa-image"></i>
                    </div>
                </div>
            </div>
            
            <div class="nav-actions">
                <button class="nav-btn" id="upload-btn">
                    <i class="fas fa-upload"></i>
                    <span>上传</span>
                </button>
                <button class="nav-btn" id="notifications-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                <div class="user-menu">
                    <button class="user-avatar" id="user-menu-btn">
                        <i class="fas fa-user"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="test-container">
        <div class="test-section">
            <div class="test-title">🔍 简洁图片搜索框</div>
            <p>新的搜索框设计特点：</p>
            <ul>
                <li>✅ 专注于图片搜索，去除多余的过滤器</li>
                <li>✅ 圆角设计，现代化外观</li>
                <li>✅ 左侧搜索图标，右侧图片类型指示器</li>
                <li>✅ 悬停和焦点状态的优雅过渡效果</li>
                <li>✅ 简洁的占位符文字</li>
            </ul>

            <div class="layout-info">
                <strong>设计亮点：</strong><br>
                • 圆角边框：24px 圆角，现代感十足<br>
                • 图片指示器：右侧蓝色圆形图标，明确搜索类型<br>
                • 焦点效果：蓝色边框 + 阴影，视觉反馈清晰<br>
                • 最大宽度：400px，避免过宽影响布局
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📱 响应式测试</div>
            <p>请调整浏览器窗口大小测试响应式布局：</p>
            <ul>
                <li>大屏幕：所有元素正常显示</li>
                <li>中等屏幕：搜索框适当缩小</li>
                <li>小屏幕：隐藏部分文字，保持核心功能</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 交互测试</div>
            <p>测试搜索框的交互功能：</p>
            <ol>
                <li>悬停搜索框 - 边框颜色变化，轻微阴影</li>
                <li>点击搜索框 - 获得焦点，蓝色边框和阴影</li>
                <li>输入文字 - 文字正常显示，占位符消失</li>
                <li>右侧图片图标 - 始终显示，表明搜索类型</li>
            </ol>
        </div>
    </div>

    <script>
        // 简洁搜索框交互测试
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            const searchContainer = document.querySelector('.search-container');

            // 搜索框焦点测试
            searchInput.addEventListener('focus', function() {
                console.log('图片搜索框获得焦点');
                searchContainer.style.transform = 'scale(1.02)';
            });

            searchInput.addEventListener('blur', function() {
                console.log('图片搜索框失去焦点');
                searchContainer.style.transform = 'scale(1)';
            });

            // 搜索输入测试
            searchInput.addEventListener('input', function() {
                console.log('搜索内容:', this.value);
                if (this.value.length > 0) {
                    console.log('正在搜索图片文件...');
                }
            });

            // 回车搜索
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('执行图片搜索:', this.value);
                    // 这里可以添加实际的搜索逻辑
                }
            });
        });
    </script>
</body>
</html>
