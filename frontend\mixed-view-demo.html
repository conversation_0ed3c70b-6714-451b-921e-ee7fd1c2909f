<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>混合视图演示 - 文件共享系统</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .demo-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .demo-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        .switch-btn {
            margin: 1rem 0;
            padding: 0.5rem 1rem;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>混合视图演示</h1>
        <p>文件夹显示表格视图，进入文件夹后文件显示卡片视图</p>
        
        <button class="switch-btn" onclick="toggleView()">切换视图（文件夹 ↔ 文件夹内文件）</button>
        
        <!-- 文件夹视图 -->
        <div class="demo-section" id="folder-view">
            <div class="demo-title">📁 文件夹视图（横向卡片布局）</div>
            <div class="file-list">
                <div class="folder-horizontal-cards">
                    <div class="folder-card" data-file-id="1" data-file-type="folder">
                        <div class="folder-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="folder-name">设计文件</div>
                    </div>

                    <div class="folder-card" data-file-id="2" data-file-type="folder">
                        <div class="folder-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="folder-name">产品图片</div>
                    </div>

                    <div class="folder-card" data-file-id="3" data-file-type="folder">
                        <div class="folder-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="folder-name">营销素材</div>
                    </div>

                    <div class="folder-card" data-file-id="4" data-file-type="folder">
                        <div class="folder-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="folder-name">品牌资源</div>
                    </div>

                    <div class="folder-card" data-file-id="5" data-file-type="folder">
                        <div class="folder-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="folder-name">项目文档</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 文件夹内文件视图 -->
        <div class="demo-section" id="files-view" style="display: none;">
            <div class="demo-title">🖼️ 文件夹内文件视图（卡片布局）</div>
            <div class="file-list">
                <div class="file-details-cards">
                    <div class="file-detail-card" data-file-id="1" data-file-type="file">
                        <div class="file-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">3.jpg</div>
                            <div class="file-meta">
                                <div class="file-meta-item">
                                    <div class="file-meta-label">修改时间</div>
                                    <div class="file-meta-value">2025-05-28 23:49</div>
                                </div>
                                <div class="file-meta-item">
                                    <div class="file-meta-label">类型</div>
                                    <div class="file-meta-value">JPG 图片文件</div>
                                </div>
                                <div class="file-meta-item">
                                    <div class="file-meta-label">大小</div>
                                    <div class="file-meta-value">32 KB</div>
                                </div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" data-action="download" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="action-btn" data-action="preview" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn" data-action="favorite" title="收藏">
                                <i class="fas fa-star"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="file-detail-card" data-file-id="2" data-file-type="file">
                        <div class="file-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="file-info">
                            <div class="file-name">wechat_2025-06-08_19:10:23.png</div>
                            <div class="file-meta">
                                <div class="file-meta-item">
                                    <div class="file-meta-label">修改时间</div>
                                    <div class="file-meta-value">2025-06-08 19:10</div>
                                </div>
                                <div class="file-meta-item">
                                    <div class="file-meta-label">类型</div>
                                    <div class="file-meta-value">PNG 图片文件</div>
                                </div>
                                <div class="file-meta-item">
                                    <div class="file-meta-label">大小</div>
                                    <div class="file-meta-value">83 KB</div>
                                </div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" data-action="download" title="下载">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="action-btn" data-action="preview" title="预览">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="action-btn" data-action="favorite" title="收藏">
                                <i class="fas fa-star"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="description">
            <h3>横向文件夹卡片布局特点：</h3>
            <ul>
                <li>📁 <strong>文件夹展示</strong>：使用横向卡片布局，美观直观</li>
                <li>🖼️ <strong>文件夹内文件</strong>：使用详情卡片布局，信息丰富</li>
                <li>🔄 <strong>智能切换</strong>：根据内容类型自动选择最适合的布局</li>
                <li>📱 <strong>用户友好</strong>：文件夹浏览美观，文件查看详细</li>
                <li>🎨 <strong>视觉效果</strong>：悬停动画，选中状态，交互流畅</li>
            </ul>
        </div>
    </div>
    
    <script>
        let currentView = 'folders';
        
        function toggleView() {
            const folderView = document.getElementById('folder-view');
            const filesView = document.getElementById('files-view');
            const btn = document.querySelector('.switch-btn');
            
            if (currentView === 'folders') {
                folderView.style.display = 'none';
                filesView.style.display = 'block';
                btn.textContent = '切换视图（文件夹内文件 ↔ 文件夹）';
                currentView = 'files';
            } else {
                folderView.style.display = 'block';
                filesView.style.display = 'none';
                btn.textContent = '切换视图（文件夹 ↔ 文件夹内文件）';
                currentView = 'folders';
            }
        }
        
        // 交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const actionBtns = document.querySelectorAll('.action-btn');
            const fileCards = document.querySelectorAll('.file-detail-card');
            const folderCards = document.querySelectorAll('.folder-card');

            actionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const action = this.dataset.action;
                    const fileName = this.closest('tr, .file-detail-card').querySelector('.file-name').textContent;
                    alert(`${action}: ${fileName}`);
                });
            });

            fileCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    fileCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            folderCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    folderCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    const folderName = this.querySelector('.folder-name').textContent;
                    alert(`打开文件夹: ${folderName}`);
                });
            });
        });
    </script>
</body>
</html>
