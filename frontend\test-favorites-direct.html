<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接测试收藏API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 直接测试收藏API</h1>
        
        <div>
            <button class="btn" onclick="testFavoritesAPI()">📋 测试收藏API</button>
            <button class="btn" onclick="testWithDifferentParams()">🔧 测试不同参数</button>
        </div>
        
        <div id="status" class="status">等待测试...</div>
        <div id="result" class="result">点击按钮开始测试...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        
        function updateStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function updateResult(content) {
            document.getElementById('result').textContent = content;
        }
        
        function log(message) {
            const current = document.getElementById('result').textContent;
            const timestamp = new Date().toLocaleTimeString();
            document.getElementById('result').textContent = current + `\n[${timestamp}] ${message}`;
        }
        
        async function getAuthToken() {
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                throw new Error('未找到认证信息，请先登录');
            }
            
            const auth = JSON.parse(authData);
            if (!auth.token) {
                throw new Error('认证token不存在');
            }
            
            return auth.token;
        }
        
        async function apiCall(endpoint, options = {}) {
            try {
                const token = await getAuthToken();
                
                const url = `${API_BASE}${endpoint}`;
                const config = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        ...options.headers
                    },
                    ...options
                };
                
                log(`请求: ${config.method} ${url}`);
                if (config.body) {
                    log(`请求体: ${config.body}`);
                }
                
                const response = await fetch(url, config);
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.error || response.statusText}`);
                }
                
                return data;
                
            } catch (error) {
                log(`❌ 错误: ${error.message}`);
                throw error;
            }
        }
        
        async function testFavoritesAPI() {
            updateResult('');
            log('=== 开始测试收藏API ===');
            
            try {
                // 测试基本收藏API
                log('\n1. 测试基本收藏API...');
                const result1 = await apiCall('/favorites');
                
                if (result1.favorites) {
                    log(`✅ 成功获取收藏列表: ${result1.favorites.length} 个收藏`);
                    log(`总数: ${result1.total_count || '未知'}`);
                    log(`当前页: ${result1.page || '未知'}`);
                    log(`总页数: ${result1.total_pages || '未知'}`);
                    
                    if (result1.favorites.length > 0) {
                        log('\n收藏详情:');
                        result1.favorites.forEach((fav, index) => {
                            log(`  ${index + 1}. ID: ${fav.id}, 文件ID: ${fav.file_id}`);
                            if (fav.file) {
                                log(`     文件名: ${fav.file.filename}`);
                                log(`     文件大小: ${fav.file.file_size}`);
                                log(`     文件夹: ${fav.file.folder_name}`);
                            } else {
                                log(`     ⚠️ 缺少文件信息`);
                            }
                        });
                    }
                    
                    updateStatus(`✅ 成功获取 ${result1.favorites.length} 个收藏`, 'success');
                } else {
                    log(`⚠️ 响应格式异常，没有favorites字段`);
                    updateStatus('⚠️ 响应格式异常', 'error');
                }
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                updateStatus(`❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testWithDifferentParams() {
            updateResult('');
            log('=== 测试不同参数的收藏API ===');
            
            const testCases = [
                { desc: '默认参数', url: '/favorites' },
                { desc: '第1页，10个', url: '/favorites?page=1&page_size=10' },
                { desc: '第1页，50个', url: '/favorites?page=1&page_size=50' },
                { desc: '第1页，1000个', url: '/favorites?page=1&page_size=1000' },
            ];
            
            for (const testCase of testCases) {
                try {
                    log(`\n--- 测试: ${testCase.desc} ---`);
                    const result = await apiCall(testCase.url);
                    
                    if (result.favorites) {
                        log(`✅ ${testCase.desc}: ${result.favorites.length} 个收藏`);
                        log(`   总数: ${result.total_count || '未知'}`);
                        log(`   页码: ${result.page || '未知'}/${result.total_pages || '未知'}`);
                    } else {
                        log(`❌ ${testCase.desc}: 响应格式异常`);
                    }
                    
                } catch (error) {
                    log(`❌ ${testCase.desc}: ${error.message}`);
                }
            }
            
            updateStatus('测试完成，请查看详细日志', 'success');
        }
    </script>
</body>
</html>
