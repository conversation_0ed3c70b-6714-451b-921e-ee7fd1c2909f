#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单添加权限字段
"""

import pymysql

def add_permissions_field():
    """添加权限字段"""
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 添加permissions字段
            cursor.execute("""
                ALTER TABLE shared_folders 
                ADD COLUMN permissions JSON NULL COMMENT '详细权限设置'
            """)
            connection.commit()
            print("✅ permissions字段添加成功")
            
    except pymysql.err.OperationalError as e:
        if "Duplicate column name" in str(e):
            print("✅ permissions字段已存在")
        else:
            print(f"❌ 添加字段失败: {e}")
    except Exception as e:
        print(f"❌ 操作失败: {e}")
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    add_permissions_field()
