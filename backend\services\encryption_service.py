#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密下载服务
"""

import os
import zipfile
import secrets
import string
from pathlib import Path
from typing import List, Dict, Any, Optional
import tempfile
import shutil
from datetime import datetime, timedelta

from utils.logger import setup_logger

# 可选导入加密库
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    import base64
    HAS_CRYPTOGRAPHY = True
except ImportError:
    HAS_CRYPTOGRAPHY = False

class EncryptionService:
    """加密下载服务"""
    
    def __init__(self, temp_dir: str = "./temp", db_manager=None):
        self.temp_dir = Path(temp_dir)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.db_manager = db_manager
        self.logger = setup_logger("EncryptionService")
        
        # 下载计数器（用户ID -> 文件路径 -> 下载次数）
        self.download_counters = {}
        
        # 密码请求记录（用户ID -> 请求次数）
        self.password_requests = {}
        
        # 默认配置
        self.encryption_threshold = 3  # N次下载后加密
        self.password_request_limit = 5  # 密码请求次数限制
        self.password_length = 12  # 密码长度
        
        # 清理临时文件
        self._cleanup_temp_files()
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            # 删除超过24小时的临时文件
            cutoff_time = datetime.now() - timedelta(hours=24)
            
            for temp_file in self.temp_dir.glob("*"):
                if temp_file.is_file():
                    file_time = datetime.fromtimestamp(temp_file.stat().st_mtime)
                    if file_time < cutoff_time:
                        temp_file.unlink()
                        self.logger.debug(f"清理临时文件: {temp_file}")
                        
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")
    
    def should_encrypt_download(self, user_id: int, file_path: str) -> bool:
        """检查是否应该加密下载"""
        try:
            if user_id not in self.download_counters:
                self.download_counters[user_id] = {}
            
            user_downloads = self.download_counters[user_id]
            current_count = user_downloads.get(file_path, 0)
            
            return current_count >= self.encryption_threshold
            
        except Exception as e:
            self.logger.error(f"检查加密下载失败: {e}")
            return False
    
    def record_download(self, user_id: int, file_path: str):
        """记录下载次数"""
        try:
            if user_id not in self.download_counters:
                self.download_counters[user_id] = {}
            
            user_downloads = self.download_counters[user_id]
            user_downloads[file_path] = user_downloads.get(file_path, 0) + 1
            
            self.logger.debug(f"记录下载: 用户 {user_id}, 文件 {file_path}, 次数 {user_downloads[file_path]}")
            
        except Exception as e:
            self.logger.error(f"记录下载失败: {e}")
    
    def create_encrypted_package(self, file_paths: List[str], package_name: str = None) -> Dict[str, Any]:
        """创建加密压缩包"""
        try:
            if not file_paths:
                return {"success": False, "error": "文件列表为空"}
            
            # 生成包名
            if not package_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                package_name = f"download_{timestamp}"
            
            # 生成密码
            password = self._generate_password()
            
            # 创建临时压缩包
            temp_zip_path = self.temp_dir / f"{package_name}.zip"
            
            # 创建压缩包
            with zipfile.ZipFile(temp_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in file_paths:
                    if os.path.exists(file_path):
                        # 获取文件在压缩包中的名称
                        arcname = os.path.basename(file_path)
                        
                        # 如果有重名文件，添加序号
                        counter = 1
                        original_arcname = arcname
                        while arcname in [info.filename for info in zipf.infolist()]:
                            name, ext = os.path.splitext(original_arcname)
                            arcname = f"{name}_{counter}{ext}"
                            counter += 1
                        
                        zipf.write(file_path, arcname)
                        self.logger.debug(f"添加文件到压缩包: {file_path} -> {arcname}")
            
            # 加密压缩包
            if HAS_CRYPTOGRAPHY:
                encrypted_path = self._encrypt_file_with_password(temp_zip_path, password)
                if encrypted_path:
                    # 删除原始压缩包
                    temp_zip_path.unlink()
                    
                    return {
                        "success": True,
                        "package_path": encrypted_path,
                        "password": password,
                        "file_count": len(file_paths)
                    }
            
            # 如果加密失败或不支持加密，使用密码保护的ZIP
            protected_zip_path = self._create_password_protected_zip(file_paths, package_name, password)
            if protected_zip_path:
                # 删除临时文件
                if temp_zip_path.exists():
                    temp_zip_path.unlink()
                
                return {
                    "success": True,
                    "package_path": protected_zip_path,
                    "password": password,
                    "file_count": len(file_paths)
                }
            
            return {"success": False, "error": "创建加密包失败"}
            
        except Exception as e:
            self.logger.error(f"创建加密包失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _encrypt_file_with_password(self, file_path: Path, password: str) -> Optional[Path]:
        """使用密码加密文件"""
        try:
            if not HAS_CRYPTOGRAPHY:
                return None
            
            # 生成密钥
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            fernet = Fernet(key)
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # 加密数据
            encrypted_data = fernet.encrypt(file_data)
            
            # 保存加密文件
            encrypted_path = file_path.with_suffix('.encrypted')
            with open(encrypted_path, 'wb') as f:
                f.write(salt)  # 保存盐值
                f.write(encrypted_data)
            
            return encrypted_path
            
        except Exception as e:
            self.logger.error(f"文件加密失败: {e}")
            return None
    
    def _create_password_protected_zip(self, file_paths: List[str], package_name: str, password: str) -> Optional[Path]:
        """创建密码保护的ZIP文件"""
        try:
            # 注意：标准zipfile库不支持密码保护
            # 这里创建普通ZIP，实际部署时可以使用pyminizip等库
            
            protected_zip_path = self.temp_dir / f"{package_name}_protected.zip"
            
            with zipfile.ZipFile(protected_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加密码信息文件
                password_info = f"解压密码: {password}\n创建时间: {datetime.now().isoformat()}"
                zipf.writestr("密码.txt", password_info.encode('utf-8'))
                
                for file_path in file_paths:
                    if os.path.exists(file_path):
                        arcname = os.path.basename(file_path)
                        zipf.write(file_path, arcname)
            
            return protected_zip_path
            
        except Exception as e:
            self.logger.error(f"创建密码保护ZIP失败: {e}")
            return None
    
    def _generate_password(self) -> str:
        """生成随机密码"""
        try:
            # 生成包含字母、数字的密码
            characters = string.ascii_letters + string.digits
            password = ''.join(secrets.choice(characters) for _ in range(self.password_length))
            
            # 确保密码包含至少一个大写字母、小写字母和数字
            if not any(c.isupper() for c in password):
                password = password[:-1] + secrets.choice(string.ascii_uppercase)
            if not any(c.islower() for c in password):
                password = password[:-1] + secrets.choice(string.ascii_lowercase)
            if not any(c.isdigit() for c in password):
                password = password[:-1] + secrets.choice(string.digits)
            
            return password
            
        except Exception as e:
            self.logger.error(f"生成密码失败: {e}")
            return "DefaultPass123"
    
    def request_password(self, user_id: int, package_id: str) -> Dict[str, Any]:
        """请求解压密码"""
        try:
            # 检查请求次数限制
            if user_id not in self.password_requests:
                self.password_requests[user_id] = {}
            
            user_requests = self.password_requests[user_id]
            current_requests = user_requests.get(package_id, 0)
            
            if current_requests >= self.password_request_limit:
                return {
                    "success": False,
                    "error": f"密码请求次数已达上限 ({self.password_request_limit})"
                }
            
            # 记录请求
            user_requests[package_id] = current_requests + 1
            
            # 这里应该从数据库或缓存中获取密码
            # 暂时返回模拟密码
            password = "TempPassword123"
            
            self.logger.info(f"用户 {user_id} 请求密码: {package_id}, 请求次数: {user_requests[package_id]}")
            
            return {
                "success": True,
                "password": password,
                "remaining_requests": self.password_request_limit - user_requests[package_id]
            }
            
        except Exception as e:
            self.logger.error(f"请求密码失败: {e}")
            return {"success": False, "error": "密码请求失败"}
    
    def create_single_file_package(self, file_path: str, user_id: int) -> Dict[str, Any]:
        """创建单文件下载包"""
        try:
            if not os.path.exists(file_path):
                return {"success": False, "error": "文件不存在"}
            
            # 检查是否需要加密
            if self.should_encrypt_download(user_id, file_path):
                # 创建加密包
                result = self.create_encrypted_package([file_path])
                if result["success"]:
                    self.logger.info(f"创建加密单文件包: {file_path}")
                return result
            else:
                # 记录下载并返回原文件
                self.record_download(user_id, file_path)
                return {
                    "success": True,
                    "package_path": file_path,
                    "encrypted": False,
                    "download_count": self.download_counters.get(user_id, {}).get(file_path, 0)
                }
                
        except Exception as e:
            self.logger.error(f"创建单文件包失败: {e}")
            return {"success": False, "error": str(e)}
    
    def create_batch_package(self, file_paths: List[str], user_id: int) -> Dict[str, Any]:
        """创建批量下载包"""
        try:
            if not file_paths:
                return {"success": False, "error": "文件列表为空"}
            
            # 过滤存在的文件
            existing_files = [f for f in file_paths if os.path.exists(f)]
            if not existing_files:
                return {"success": False, "error": "没有有效的文件"}
            
            # 检查是否有文件需要加密
            needs_encryption = any(
                self.should_encrypt_download(user_id, f) for f in existing_files
            )
            
            if needs_encryption:
                # 创建加密包
                result = self.create_encrypted_package(existing_files)
                if result["success"]:
                    # 记录所有文件的下载
                    for file_path in existing_files:
                        self.record_download(user_id, file_path)
                    self.logger.info(f"创建加密批量包: {len(existing_files)} 个文件")
                return result
            else:
                # 创建普通压缩包
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                package_name = f"batch_download_{timestamp}"
                zip_path = self.temp_dir / f"{package_name}.zip"
                
                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for file_path in existing_files:
                        arcname = os.path.basename(file_path)
                        zipf.write(file_path, arcname)
                        self.record_download(user_id, file_path)
                
                return {
                    "success": True,
                    "package_path": str(zip_path),
                    "encrypted": False,
                    "file_count": len(existing_files)
                }
                
        except Exception as e:
            self.logger.error(f"创建批量包失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_download_statistics(self, user_id: int = None) -> Dict[str, Any]:
        """获取下载统计"""
        try:
            if user_id:
                # 获取特定用户的统计
                user_downloads = self.download_counters.get(user_id, {})
                return {
                    "user_id": user_id,
                    "total_downloads": sum(user_downloads.values()),
                    "unique_files": len(user_downloads),
                    "files": user_downloads
                }
            else:
                # 获取全局统计
                total_downloads = 0
                total_users = len(self.download_counters)
                total_files = set()
                
                for user_downloads in self.download_counters.values():
                    total_downloads += sum(user_downloads.values())
                    total_files.update(user_downloads.keys())
                
                return {
                    "total_downloads": total_downloads,
                    "total_users": total_users,
                    "unique_files": len(total_files)
                }
                
        except Exception as e:
            self.logger.error(f"获取下载统计失败: {e}")
            return {}
    
    def cleanup_expired_packages(self) -> int:
        """清理过期的下载包"""
        try:
            cleaned_count = 0
            cutoff_time = datetime.now() - timedelta(hours=6)  # 6小时后清理
            
            for package_file in self.temp_dir.glob("*.zip"):
                file_time = datetime.fromtimestamp(package_file.stat().st_mtime)
                if file_time < cutoff_time:
                    package_file.unlink()
                    cleaned_count += 1
                    self.logger.debug(f"清理过期包: {package_file}")
            
            for encrypted_file in self.temp_dir.glob("*.encrypted"):
                file_time = datetime.fromtimestamp(encrypted_file.stat().st_mtime)
                if file_time < cutoff_time:
                    encrypted_file.unlink()
                    cleaned_count += 1
                    self.logger.debug(f"清理过期加密文件: {encrypted_file}")
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理过期包失败: {e}")
            return 0
