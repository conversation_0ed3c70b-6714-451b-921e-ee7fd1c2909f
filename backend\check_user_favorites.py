#!/usr/bin/env python3
"""
检查特定用户的收藏数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import Database
import json

def check_user_favorites():
    """检查用户收藏数据"""
    db = Database()
    
    try:
        # 连接数据库
        db.connect()
        
        print("=== 检查用户收藏数据 ===")
        
        # 1. 检查所有用户
        print("\n1. 所有用户列表:")
        users_query = "SELECT id, username, full_name FROM users ORDER BY id"
        users = db.fetch_all(users_query)
        
        for user in users:
            print(f"  用户ID: {user['id']}, 用户名: {user['username']}, 姓名: {user['full_name']}")
        
        # 2. 查找test1用户
        print("\n2. 查找test1用户:")
        test1_query = "SELECT id, username, full_name FROM users WHERE username = 'test1'"
        test1_user = db.fetch_one(test1_query)
        
        if not test1_user:
            print("❌ 未找到test1用户")
            return
        
        user_id = test1_user['id']
        print(f"✅ 找到test1用户: ID={user_id}, 姓名={test1_user['full_name']}")
        
        # 3. 检查该用户的收藏数据
        print(f"\n3. 检查用户ID {user_id} 的收藏数据:")
        favorites_query = """
        SELECT 
            f.id as favorite_id,
            f.file_id,
            f.notes,
            f.favorited_at,
            fi.filename,
            fi.file_size,
            fi.is_image,
            fo.name as folder_name
        FROM favorites f
        LEFT JOIN files fi ON f.file_id = fi.id
        LEFT JOIN folders fo ON fi.folder_id = fo.id
        WHERE f.user_id = %s
        ORDER BY f.favorited_at DESC
        """
        
        favorites = db.fetch_all(favorites_query, (user_id,))
        
        print(f"总收藏数: {len(favorites)}")
        
        if favorites:
            print("\n收藏详情:")
            for i, fav in enumerate(favorites, 1):
                print(f"  {i}. 收藏ID: {fav['favorite_id']}")
                print(f"     文件ID: {fav['file_id']}")
                print(f"     文件名: {fav['filename']}")
                print(f"     文件大小: {fav['file_size']}")
                print(f"     是否图片: {fav['is_image']}")
                print(f"     文件夹: {fav['folder_name']}")
                print(f"     收藏时间: {fav['favorited_at']}")
                print(f"     备注: {fav['notes']}")
                print()
        else:
            print("❌ 该用户没有收藏任何文件")
        
        # 4. 检查文件表中的数据
        print(f"\n4. 检查文件表中的数据:")
        if favorites:
            file_ids = [str(fav['file_id']) for fav in favorites]
            files_query = f"""
            SELECT 
                id,
                filename,
                file_size,
                is_image,
                folder_id,
                created_at
            FROM files 
            WHERE id IN ({','.join(['%s'] * len(file_ids))})
            """
            
            files = db.fetch_all(files_query, file_ids)
            print(f"文件表中找到 {len(files)} 个文件:")
            
            for file in files:
                print(f"  文件ID: {file['id']}, 文件名: {file['filename']}, 大小: {file['file_size']}")
        
        # 5. 检查是否有孤立的收藏记录（文件已删除）
        print(f"\n5. 检查孤立的收藏记录:")
        orphan_query = """
        SELECT f.id, f.file_id, f.favorited_at
        FROM favorites f
        LEFT JOIN files fi ON f.file_id = fi.id
        WHERE f.user_id = %s AND fi.id IS NULL
        """
        
        orphans = db.fetch_all(orphan_query, (user_id,))
        if orphans:
            print(f"发现 {len(orphans)} 个孤立的收藏记录（对应的文件已删除）:")
            for orphan in orphans:
                print(f"  收藏ID: {orphan['id']}, 文件ID: {orphan['file_id']}, 收藏时间: {orphan['favorited_at']}")
        else:
            print("✅ 没有孤立的收藏记录")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    check_user_favorites()
