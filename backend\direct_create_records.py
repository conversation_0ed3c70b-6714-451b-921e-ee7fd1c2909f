#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.download_record import DownloadRecord
from models.user import User
from utils.database import DatabaseManager
from datetime import datetime, timedelta

def create_test_records():
    """直接通过ORM创建测试下载记录"""
    print("📝 通过ORM创建测试下载记录...")
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        
        with db_manager.get_session() as session:
            # 获取用户fjj
            user = session.query(User).filter_by(username='fjj').first()
            if not user:
                print("❌ 找不到用户fjj")
                return False
            
            user_id = user.id
            print(f"✅ 找到用户fjj，ID: {user_id}")
            
            # 清除现有的测试记录
            existing_count = session.query(DownloadRecord).filter_by(user_id=user_id).count()
            print(f"📊 现有下载记录数量: {existing_count}")
            
            if existing_count > 0:
                session.query(DownloadRecord).filter_by(user_id=user_id).delete()
                print("🗑️ 清除了现有的测试记录")
            
            # 创建测试记录
            test_records = []
            for i in range(5):
                download_time = datetime.now() - timedelta(days=i, hours=i*2)
                is_encrypted = i % 2 == 0
                
                record = DownloadRecord(
                    download_type='single',
                    zip_filename=f"test_file_{i+1}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                    zip_path=f"/tmp/downloads/test_file_{i+1}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                    file_size=1024 * 1024 * (i + 1),  # 不同大小
                    file_id=i + 1,
                    user_id=user_id,
                    is_encrypted=is_encrypted,
                    password=f"pass{i+1}" if is_encrypted else None,
                    download_status='completed',
                    downloaded_at=download_time
                )
                
                # 手动设置创建时间
                record.created_at = download_time
                
                session.add(record)
                test_records.append(record)
            
            # 提交事务
            session.commit()
            print(f"✅ 创建了 {len(test_records)} 条测试下载记录")
            
            # 查询并显示创建的记录
            records = session.query(DownloadRecord).filter_by(user_id=user_id).order_by(DownloadRecord.created_at.desc()).all()
            
            print(f"\n📋 创建的下载记录:")
            print("ID | 文件ID | 压缩文件名 | 类型 | 加密 | 大小(MB) | 创建时间")
            print("-" * 80)
            
            for record in records:
                file_size_mb = record.file_size / (1024 * 1024) if record.file_size else 0
                print(f"{record.id:3d} | {record.file_id:6d} | {record.zip_filename[:25]:25s} | {record.download_type:6s} | {record.is_encrypted:5s} | {file_size_mb:8.1f} | {record.created_at}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试记录失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    create_test_records()
