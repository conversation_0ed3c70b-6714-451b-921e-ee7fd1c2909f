#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime
import psutil
from typing import Dict, Any, Optional

class MonitoringWindow:
    """监控窗口类"""
    
    def __init__(self, parent, server):
        self.parent = parent
        self.server = server
        self.window = None
        self.monitoring_active = False
        self.update_thread = None
        
        # 数据存储
        self.system_stats = {}
        self.user_activities = []
        self.server_stats = {}
        
        self.create_window()
    
    def create_window(self):
        """创建监控窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("系统监控")
        self.window.geometry("1000x700")
        self.window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标签页
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 系统监控标签页
        self.create_system_tab()
        
        # 用户活动标签页
        self.create_activity_tab()
        
        # 服务器状态标签页
        self.create_server_tab()
        
        # 控制按钮
        self.create_control_buttons(main_frame)
        
        # 开始监控
        self.start_monitoring()
        
        # 窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_system_tab(self):
        """创建系统监控标签页"""
        system_frame = ttk.Frame(self.notebook)
        self.notebook.add(system_frame, text="系统监控")
        
        # 系统信息框架
        info_frame = ttk.LabelFrame(system_frame, text="系统信息", padding=10)
        info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 系统信息标签
        self.cpu_label = ttk.Label(info_frame, text="CPU使用率: --")
        self.cpu_label.pack(anchor=tk.W)
        
        self.memory_label = ttk.Label(info_frame, text="内存使用率: --")
        self.memory_label.pack(anchor=tk.W)
        
        self.disk_label = ttk.Label(info_frame, text="磁盘使用率: --")
        self.disk_label.pack(anchor=tk.W)
        
        self.network_label = ttk.Label(info_frame, text="网络状态: --")
        self.network_label.pack(anchor=tk.W)
        
        # 进程列表框架
        process_frame = ttk.LabelFrame(system_frame, text="系统进程", padding=10)
        process_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 进程列表
        columns = ("PID", "进程名", "CPU%", "内存%", "状态")
        self.process_tree = ttk.Treeview(process_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.process_tree.heading(col, text=col)
            self.process_tree.column(col, width=100)
        
        # 滚动条
        process_scrollbar = ttk.Scrollbar(process_frame, orient=tk.VERTICAL, command=self.process_tree.yview)
        self.process_tree.configure(yscrollcommand=process_scrollbar.set)
        
        self.process_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        process_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_activity_tab(self):
        """创建用户活动标签页"""
        activity_frame = ttk.Frame(self.notebook)
        self.notebook.add(activity_frame, text="用户活动")
        
        # 在线用户框架
        online_frame = ttk.LabelFrame(activity_frame, text="在线用户", padding=10)
        online_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 在线用户列表
        columns = ("用户ID", "用户名", "IP地址", "登录时间", "最后活动")
        self.users_tree = ttk.Treeview(online_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=120)
        
        # 滚动条
        users_scrollbar = ttk.Scrollbar(online_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)
        
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        users_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 活动日志框架
        log_frame = ttk.LabelFrame(activity_frame, text="活动日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 活动日志列表
        columns = ("时间", "用户", "操作", "详情", "IP地址")
        self.activity_tree = ttk.Treeview(log_frame, columns=columns, show="headings", height=12)
        
        for col in columns:
            self.activity_tree.heading(col, text=col)
            self.activity_tree.column(col, width=150)
        
        # 滚动条
        activity_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)
        
        self.activity_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        activity_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_server_tab(self):
        """创建服务器状态标签页"""
        server_frame = ttk.Frame(self.notebook)
        self.notebook.add(server_frame, text="服务器状态")
        
        # 服务状态框架
        status_frame = ttk.LabelFrame(server_frame, text="服务状态", padding=10)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 服务状态标签
        self.api_status_label = ttk.Label(status_frame, text="API服务器: --")
        self.api_status_label.pack(anchor=tk.W)
        
        self.db_status_label = ttk.Label(status_frame, text="数据库连接: --")
        self.db_status_label.pack(anchor=tk.W)
        
        self.file_service_label = ttk.Label(status_frame, text="文件服务: --")
        self.file_service_label.pack(anchor=tk.W)
        
        self.search_service_label = ttk.Label(status_frame, text="搜索服务: --")
        self.search_service_label.pack(anchor=tk.W)
        
        # 统计信息框架
        stats_frame = ttk.LabelFrame(server_frame, text="统计信息", padding=10)
        stats_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 统计信息标签
        self.total_users_label = ttk.Label(stats_frame, text="总用户数: --")
        self.total_users_label.pack(anchor=tk.W)
        
        self.total_files_label = ttk.Label(stats_frame, text="总文件数: --")
        self.total_files_label.pack(anchor=tk.W)
        
        self.total_downloads_label = ttk.Label(stats_frame, text="总下载次数: --")
        self.total_downloads_label.pack(anchor=tk.W)
        
        self.uptime_label = ttk.Label(stats_frame, text="运行时间: --")
        self.uptime_label.pack(anchor=tk.W)
        
        # 性能图表框架（简化版）
        chart_frame = ttk.LabelFrame(server_frame, text="性能趋势", padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 性能数据显示
        self.performance_text = tk.Text(chart_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
        perf_scrollbar = ttk.Scrollbar(chart_frame, orient=tk.VERTICAL, command=self.performance_text.yview)
        self.performance_text.configure(yscrollcommand=perf_scrollbar.set)
        
        self.performance_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        perf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_control_buttons(self, parent):
        """创建控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="刷新数据", command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出报告", command=self.export_report).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清理日志", command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态标签
        self.status_label = ttk.Label(button_frame, text="监控状态: 运行中")
        self.status_label.pack(side=tk.RIGHT)
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring_active = True
        self.update_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.update_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.update_thread:
            self.update_thread.join(timeout=1)
    
    def monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                self.update_system_info()
                self.update_user_activity()
                self.update_server_status()
                time.sleep(2)  # 每2秒更新一次
            except Exception as e:
                print(f"监控更新错误: {e}")
                time.sleep(5)
    
    def update_system_info(self):
        """更新系统信息"""
        try:
            # 获取系统信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 更新标签
            self.window.after(0, lambda: self.cpu_label.config(text=f"CPU使用率: {cpu_percent:.1f}%"))
            self.window.after(0, lambda: self.memory_label.config(text=f"内存使用率: {memory.percent:.1f}%"))
            self.window.after(0, lambda: self.disk_label.config(text=f"磁盘使用率: {disk.percent:.1f}%"))
            
            # 更新进程列表
            self.update_process_list()
            
        except Exception as e:
            print(f"更新系统信息失败: {e}")
    
    def update_process_list(self):
        """更新进程列表"""
        try:
            # 清空现有数据
            self.window.after(0, lambda: self.process_tree.delete(*self.process_tree.get_children()))
            
            # 获取进程信息
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # 按CPU使用率排序
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            
            # 只显示前20个进程
            for proc in processes[:20]:
                self.window.after(0, lambda p=proc: self.process_tree.insert("", tk.END, values=(
                    p.get('pid', 'N/A'),
                    p.get('name', 'N/A'),
                    f"{p.get('cpu_percent', 0):.1f}%",
                    f"{p.get('memory_percent', 0):.1f}%",
                    p.get('status', 'N/A')
                )))
                
        except Exception as e:
            print(f"更新进程列表失败: {e}")
    
    def update_user_activity(self):
        """更新用户活动"""
        try:
            # 这里应该从服务器获取用户活动数据
            # 暂时使用模拟数据
            pass
        except Exception as e:
            print(f"更新用户活动失败: {e}")
    
    def update_server_status(self):
        """更新服务器状态"""
        try:
            if self.server:
                # 更新服务状态
                api_status = "运行中" if self.server.running else "已停止"
                self.window.after(0, lambda: self.api_status_label.config(text=f"API服务器: {api_status}"))
                
                # 更新数据库状态
                db_status = "连接正常" if self.server.db_manager else "连接失败"
                self.window.after(0, lambda: self.db_status_label.config(text=f"数据库连接: {db_status}"))
                
                # 更新服务数量
                service_count = len(self.server.services) if hasattr(self.server, 'services') else 0
                self.window.after(0, lambda: self.file_service_label.config(text=f"已启动服务: {service_count} 个"))
                
                # 更新运行时间
                if hasattr(self.server, 'start_time'):
                    uptime = time.time() - self.server.start_time
                    uptime_str = f"{int(uptime // 3600)}小时 {int((uptime % 3600) // 60)}分钟"
                    self.window.after(0, lambda: self.uptime_label.config(text=f"运行时间: {uptime_str}"))
                
        except Exception as e:
            print(f"更新服务器状态失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            self.update_system_info()
            self.update_user_activity()
            self.update_server_status()
            messagebox.showinfo("成功", "数据已刷新")
        except Exception as e:
            messagebox.showerror("错误", f"刷新数据失败: {e}")
    
    def export_report(self):
        """导出监控报告"""
        messagebox.showinfo("导出报告", "监控报告导出功能正在开发中...")
    
    def clear_logs(self):
        """清理日志"""
        result = messagebox.askyesno("确认", "确定要清理活动日志吗？")
        if result:
            # 清理日志逻辑
            messagebox.showinfo("成功", "日志已清理")
    
    def on_closing(self):
        """窗口关闭事件"""
        self.stop_monitoring()
        self.window.destroy()
        self.window = None
