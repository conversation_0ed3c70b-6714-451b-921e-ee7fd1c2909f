/* 动画效果 */

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 淡出动画 */
@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* 滑入动画 */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 缩放动画 */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* 弹跳动画 */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -30px, 0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

/* 摇摆动画 */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 旋转动画 */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 闪烁动画 */
@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* 波纹动画 */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* 进度条动画 */
@keyframes progressBar {
    0% {
        width: 0%;
    }
    100% {
        width: var(--progress-width, 100%);
    }
}

/* 骨架屏动画 */
@keyframes skeleton {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* 动画类 */
.animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
}

.animate-fadeOut {
    animation: fadeOut 0.3s ease-out forwards;
}

.animate-slideInRight {
    animation: slideInRight 0.3s ease-out forwards;
}

.animate-slideInLeft {
    animation: slideInLeft 0.3s ease-out forwards;
}

.animate-slideInUp {
    animation: slideInUp 0.3s ease-out forwards;
}

.animate-slideInDown {
    animation: slideInDown 0.3s ease-out forwards;
}

.animate-scaleIn {
    animation: scaleIn 0.3s ease-out forwards;
}

.animate-scaleOut {
    animation: scaleOut 0.3s ease-out forwards;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-spin {
    animation: rotate 1s linear infinite;
}

.animate-blink {
    animation: blink 1s ease-in-out infinite;
}

/* 悬停动画 */
.hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.2s ease-in-out;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow 0.2s ease-in-out;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* 加载状态动画 */
.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* 骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: skeleton 1.5s infinite;
}

.skeleton-text {
    height: 1em;
    margin-bottom: 0.5em;
    border-radius: 4px;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-button {
    height: 40px;
    border-radius: 8px;
}

/* 页面过渡动画 */
.page-transition-enter {
    opacity: 0;
    transform: translateX(20px);
}

.page-transition-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.page-transition-exit {
    opacity: 1;
    transform: translateX(0);
}

.page-transition-exit-active {
    opacity: 0;
    transform: translateX(-20px);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

/* 文件上传动画 */
.upload-bounce {
    animation: uploadBounce 0.6s ease-in-out;
}

@keyframes uploadBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* 成功动画 */
.success-checkmark {
    animation: successCheckmark 0.6s ease-in-out;
}

@keyframes successCheckmark {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 错误动画 */
.error-shake {
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* 通知滑入动画 */
.notification-slide-in {
    animation: notificationSlideIn 0.3s ease-out forwards;
}

@keyframes notificationSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-slide-out {
    animation: notificationSlideOut 0.3s ease-out forwards;
}

@keyframes notificationSlideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 搜索结果高亮动画 */
.search-highlight {
    animation: searchHighlight 1s ease-in-out;
}

@keyframes searchHighlight {
    0%, 100% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(255, 235, 59, 0.3);
    }
}

/* 文件拖拽动画 */
.drag-over {
    animation: dragOver 0.3s ease-in-out;
    border-color: var(--primary-color) !important;
    background-color: var(--primary-light) !important;
}

@keyframes dragOver {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 性能优化 */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.will-change-auto {
    will-change: auto;
}

/* GPU 加速 */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 延迟动画 */
.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}

.delay-500 {
    animation-delay: 0.5s;
}

.delay-700 {
    animation-delay: 0.7s;
}

.delay-1000 {
    animation-delay: 1s;
}
