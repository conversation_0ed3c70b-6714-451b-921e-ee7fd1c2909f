<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件夹加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn.primary {
            background: #007bff;
            color: white;
        }
        .test-btn.success {
            background: #28a745;
            color: white;
        }
        .test-btn.warning {
            background: #ffc107;
            color: black;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 5px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 文件夹加载测试</h1>
        <p>测试前端是否能正确加载共享文件夹</p>

        <div class="test-section">
            <h3>📁 API测试</h3>
            <button class="test-btn primary" onclick="testFoldersAPI()">测试 /api/files/folders</button>
            <button class="test-btn primary" onclick="testFilesAPI()">测试 /api/files</button>
            <button class="test-btn warning" onclick="testAuth()">测试认证状态</button>
            <div id="api-result" class="result-box" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 前端模块测试</h3>
            <button class="test-btn success" onclick="testFileManager()">测试 FileManager</button>
            <button class="test-btn success" onclick="testFolderAPI()">测试 FolderAPI</button>
            <button class="test-btn success" onclick="simulateLoad()">模拟加载过程</button>
            <div id="frontend-result" class="result-box" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 状态显示</h3>
            <div id="status-display">
                <div class="status info">等待测试...</div>
            </div>
        </div>
    </div>

    <!-- 引入必要的前端脚本 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>

    <script>
        let testResults = {};

        function showResult(containerId, content) {
            const container = document.getElementById(containerId);
            container.style.display = 'block';
            container.textContent = content;
        }

        function updateStatus(message, type = 'info') {
            const statusDisplay = document.getElementById('status-display');
            statusDisplay.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testFoldersAPI() {
            updateStatus('测试 /api/files/folders API...', 'info');
            
            try {
                const response = await fetch(`${CONFIG.API.BASE_URL}/files/folders`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                showResult('api-result', `
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}
                `);

                if (response.ok) {
                    testResults.foldersAPI = { success: true, data };
                    updateStatus(`✅ /api/files/folders 测试成功，返回 ${Array.isArray(data) ? data.length : '未知'} 个文件夹`, 'success');
                } else {
                    testResults.foldersAPI = { success: false, error: data };
                    updateStatus(`❌ /api/files/folders 测试失败: ${data.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `请求失败: ${error.message}`);
                testResults.foldersAPI = { success: false, error: error.message };
                updateStatus(`❌ /api/files/folders 请求失败: ${error.message}`, 'error');
            }
        }

        async function testFilesAPI() {
            updateStatus('测试 /api/files API...', 'info');
            
            try {
                // 获取认证token
                const authData = localStorage.getItem('fileShareAuth');
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (authData) {
                    const auth = JSON.parse(authData);
                    headers['Authorization'] = `Bearer ${auth.token}`;
                }

                const response = await fetch(`${CONFIG.API.BASE_URL}/files`, {
                    method: 'GET',
                    headers
                });

                const data = await response.json();
                
                showResult('api-result', `
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}
                `);

                if (response.ok) {
                    testResults.filesAPI = { success: true, data };
                    const fileCount = data.files ? data.files.length : 0;
                    updateStatus(`✅ /api/files 测试成功，返回 ${fileCount} 个项目`, 'success');
                } else {
                    testResults.filesAPI = { success: false, error: data };
                    updateStatus(`❌ /api/files 测试失败: ${data.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `请求失败: ${error.message}`);
                testResults.filesAPI = { success: false, error: error.message };
                updateStatus(`❌ /api/files 请求失败: ${error.message}`, 'error');
            }
        }

        async function testAuth() {
            updateStatus('检查认证状态...', 'info');
            
            const authData = localStorage.getItem('fileShareAuth');
            
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    showResult('api-result', `
认证数据存在:
${JSON.stringify(auth, null, 2)}
                    `);
                    updateStatus('✅ 找到认证数据', 'success');
                } catch (error) {
                    showResult('api-result', `认证数据解析失败: ${error.message}`);
                    updateStatus('❌ 认证数据格式错误', 'error');
                }
            } else {
                showResult('api-result', '未找到认证数据，需要先登录');
                updateStatus('⚠️ 未找到认证数据，请先登录', 'warning');
            }
        }

        async function testFileManager() {
            updateStatus('测试 FileManager 模块...', 'info');
            
            try {
                if (typeof FileManager === 'undefined') {
                    throw new Error('FileManager 类未定义');
                }

                // 创建临时实例进行测试
                const tempManager = new FileManager();
                
                showResult('frontend-result', `
FileManager 实例创建成功
属性检查:
- currentFolder: ${tempManager.currentFolder}
- files: ${tempManager.files ? tempManager.files.length : 0} 项
- viewMode: ${tempManager.viewMode}
- isInFolder: ${tempManager.isInFolder}
                `);

                updateStatus('✅ FileManager 模块测试成功', 'success');
            } catch (error) {
                showResult('frontend-result', `FileManager 测试失败: ${error.message}`);
                updateStatus(`❌ FileManager 测试失败: ${error.message}`, 'error');
            }
        }

        async function testFolderAPI() {
            updateStatus('测试 FolderAPI 模块...', 'info');
            
            try {
                if (typeof FolderAPI === 'undefined') {
                    throw new Error('FolderAPI 未定义');
                }

                if (typeof FolderAPI.getSharedFolders !== 'function') {
                    throw new Error('FolderAPI.getSharedFolders 方法不存在');
                }

                const folders = await FolderAPI.getSharedFolders();
                
                showResult('frontend-result', `
FolderAPI.getSharedFolders() 调用成功
返回数据:
${JSON.stringify(folders, null, 2)}
                `);

                if (Array.isArray(folders)) {
                    updateStatus(`✅ FolderAPI 测试成功，返回 ${folders.length} 个文件夹`, 'success');
                } else {
                    updateStatus('⚠️ FolderAPI 返回数据格式异常', 'warning');
                }
            } catch (error) {
                showResult('frontend-result', `FolderAPI 测试失败: ${error.message}`);
                updateStatus(`❌ FolderAPI 测试失败: ${error.message}`, 'error');
            }
        }

        async function simulateLoad() {
            updateStatus('模拟完整加载过程...', 'info');
            
            try {
                let log = '开始模拟加载过程...\n\n';
                
                // 1. 检查认证
                log += '1. 检查认证状态\n';
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    log += '   ❌ 未找到认证数据\n';
                    throw new Error('需要先登录');
                }
                log += '   ✅ 认证数据存在\n\n';

                // 2. 测试 FolderAPI
                log += '2. 调用 FolderAPI.getSharedFolders()\n';
                const folders = await FolderAPI.getSharedFolders();
                log += `   ✅ 返回 ${Array.isArray(folders) ? folders.length : '未知'} 个文件夹\n\n`;

                // 3. 测试 FileAPI
                log += '3. 调用 FileAPI.getFiles(null)\n';
                const files = await FileAPI.getFiles(null);
                log += `   ✅ 返回数据: ${JSON.stringify(files, null, 2)}\n\n`;

                // 4. 模拟渲染
                log += '4. 模拟渲染过程\n';
                if (files && files.files) {
                    const folderCount = files.files.filter(f => f.type === 'folder').length;
                    const fileCount = files.files.filter(f => f.type !== 'folder').length;
                    log += `   📁 文件夹: ${folderCount} 个\n`;
                    log += `   📄 文件: ${fileCount} 个\n`;
                }

                showResult('frontend-result', log);
                updateStatus('✅ 模拟加载过程完成', 'success');

            } catch (error) {
                showResult('frontend-result', `模拟加载失败: ${error.message}`);
                updateStatus(`❌ 模拟加载失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动检查基本状态
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，可以开始测试', 'info');
            
            // 检查必要的对象是否存在
            const checks = [
                { name: 'CONFIG', obj: typeof CONFIG !== 'undefined' },
                { name: 'Utils', obj: typeof Utils !== 'undefined' },
                { name: 'FolderAPI', obj: typeof FolderAPI !== 'undefined' },
                { name: 'FileAPI', obj: typeof FileAPI !== 'undefined' },
                { name: 'FileManager', obj: typeof FileManager !== 'undefined' }
            ];

            let allGood = true;
            checks.forEach(check => {
                if (!check.obj) {
                    console.error(`❌ ${check.name} 未定义`);
                    allGood = false;
                } else {
                    console.log(`✅ ${check.name} 已加载`);
                }
            });

            if (allGood) {
                updateStatus('✅ 所有必要模块已加载，可以开始测试', 'success');
            } else {
                updateStatus('⚠️ 部分模块未加载，可能影响测试结果', 'warning');
            }
        });
    </script>
</body>
</html>
