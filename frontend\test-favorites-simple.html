<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 收藏功能测试</h1>
        
        <div>
            <button class="btn" onclick="testFavorites()">📥 测试收藏API</button>
            <button class="btn" onclick="testWithDifferentPageSizes()">📊 测试不同页面大小</button>
        </div>
        
        <div id="result" class="result info">点击按钮开始测试...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        let authToken = localStorage.getItem('auth_token');

        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.textContent += `[${timestamp}] ${message}\n`;
            resultDiv.className = `result ${type}`;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('result').textContent = '';
        }

        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });

                const data = await response.json();
                
                if (response.ok) {
                    return { success: true, data };
                } else {
                    return { success: false, error: data.error || '未知错误' };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testFavorites() {
            clearLog();
            log('开始测试收藏功能...');

            if (!authToken) {
                log('错误: 未找到认证令牌，请先登录系统', 'error');
                return;
            }

            // 测试默认参数
            log('测试1: 使用默认参数获取收藏');
            const result1 = await apiRequest('/favorites');
            if (result1.success) {
                const data = result1.data;
                log(`成功: 获取到 ${data.favorites?.length || 0} 个收藏`, 'success');
                log(`分页信息: 第 ${data.page || 1} 页，共 ${data.total_pages || 1} 页，总数 ${data.total_count || 0}`);
            } else {
                log(`失败: ${result1.error}`, 'error');
                return;
            }

            // 测试大页面大小
            log('\n测试2: 使用大页面大小 (1000)');
            const result2 = await apiRequest('/favorites?page=1&page_size=1000');
            if (result2.success) {
                const data = result2.data;
                log(`成功: 获取到 ${data.favorites?.length || 0} 个收藏`, 'success');
                log(`分页信息: 第 ${data.page || 1} 页，共 ${data.total_pages || 1} 页，总数 ${data.total_count || 0}`);
                
                if (data.total_count > 50) {
                    log(`✅ 修复成功: 能够获取超过50个收藏 (${data.total_count}个)`, 'success');
                } else {
                    log(`ℹ️ 当前收藏数量: ${data.total_count}个`, 'info');
                }
            } else {
                log(`失败: ${result2.error}`, 'error');
            }
        }

        async function testWithDifferentPageSizes() {
            clearLog();
            log('测试不同页面大小...');

            if (!authToken) {
                log('错误: 未找到认证令牌，请先登录系统', 'error');
                return;
            }

            const pageSizes = [10, 50, 100, 500, 1000];
            
            for (const pageSize of pageSizes) {
                log(`\n测试页面大小: ${pageSize}`);
                const result = await apiRequest(`/favorites?page=1&page_size=${pageSize}`);
                
                if (result.success) {
                    const data = result.data;
                    log(`✅ 成功: 获取到 ${data.favorites?.length || 0}/${data.total_count || 0} 个收藏`);
                } else {
                    log(`❌ 失败: ${result.error}`, 'error');
                }
            }
        }

        // 页面加载时检查认证状态
        window.addEventListener('DOMContentLoaded', function() {
            if (!authToken) {
                log('⚠️ 未找到认证令牌，请先登录系统', 'error');
                log('请在主页面登录后再运行此测试');
            } else {
                log('✅ 找到认证令牌，可以开始测试');
            }
        });
    </script>
</body>
</html>
