2025-06-07 20:42:47 - FileService - ERROR - 获取共享文件夹列表失败: 'NoneType' object is not callable
2025-06-07 20:42:47 - FileService - ERROR - 获取共享文件夹列表失败: 'NoneType' object is not callable
2025-06-07 20:42:57 - FileService - ERROR - 获取共享文件夹列表失败: 'NoneType' object is not callable
2025-06-07 20:42:57 - FileService - ERROR - 获取共享文件夹列表失败: 'NoneType' object is not callable
2025-06-07 21:03:00 - FileService - ERROR - 获取共享文件夹列表失败: (pymysql.err.OperationalError) (1054, "Unknown column 'shared_folders.permissions' in 'field list'")
[SQL: SELECT shared_folders.id AS shared_folders_id, shared_folders.name AS shared_folders_name, shared_folders.path AS shared_folders_path, shared_folders.description AS shared_folders_description, shared_folders.is_active AS shared_folders_is_active, shared_folders.allow_read AS shared_folders_allow_read, shared_folders.allow_write AS shared_folders_allow_write, shared_folders.allow_delete AS shared_folders_allow_delete, shared_folders.allow_upload AS shared_folders_allow_upload, shared_folders.allow_download AS shared_folders_allow_download, shared_folders.allow_internal AS shared_folders_allow_internal, shared_folders.allow_external AS shared_folders_allow_external, shared_folders.show_details AS shared_folders_show_details, shared_folders.enable_thumbnail AS shared_folders_enable_thumbnail, shared_folders.max_file_size AS shared_folders_max_file_size, shared_folders.allowed_extensions AS shared_folders_allowed_extensions, shared_folders.permissions AS shared_folders_permissions, shared_folders.file_count AS shared_folders_file_count, shared_folders.total_size AS shared_folders_total_size, shared_folders.access_count AS shared_folders_access_count, shared_folders.download_count AS shared_folders_download_count, shared_folders.created_at AS shared_folders_created_at, shared_folders.updated_at AS shared_folders_updated_at, shared_folders.last_scanned AS shared_folders_last_scanned 
FROM shared_folders]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
