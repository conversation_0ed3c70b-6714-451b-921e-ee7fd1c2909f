<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缩略图直接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .thumbnail-test {
            width: 200px;
            height: 200px;
            border: 1px solid #ccc;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }
        .thumbnail-test img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>缩略图直接测试</h1>
        
        <div class="test-item">
            <h3>1. 登录测试</h3>
            <button class="btn" onclick="testLogin()">测试登录</button>
            <div id="login-status"></div>
        </div>
        
        <div class="test-item">
            <h3>2. 获取文件列表</h3>
            <button class="btn" onclick="testGetFiles()">获取文件</button>
            <div id="files-status"></div>
            <div id="files-list"></div>
        </div>
        
        <div class="test-item">
            <h3>3. 直接测试缩略图URL</h3>
            <input type="number" id="file-id" placeholder="文件ID" value="1">
            <button class="btn" onclick="testThumbnailDirect()">测试缩略图</button>
            <div id="thumbnail-status"></div>
            <div class="thumbnail-test" id="thumbnail-container">
                <span>缩略图将显示在这里</span>
            </div>
        </div>
        
        <div class="test-item">
            <h3>4. 测试日志</h3>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <div id="test-log" class="log"></div>
        </div>
    </div>

    <script>
        let authToken = null;
        
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }
        
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function testLogin() {
            log('开始登录测试...');
            
            try {
                const response = await fetch('http://localhost:8086/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'test',
                        password: '123456'
                    })
                });
                
                log(`登录响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    showStatus('login-status', '✅ 登录成功', 'success');
                    log(`登录成功，获取token: ${authToken.substring(0, 20)}...`);
                } else {
                    const errorText = await response.text();
                    showStatus('login-status', `❌ 登录失败: ${response.status}`, 'error');
                    log(`登录失败: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                showStatus('login-status', `❌ 登录异常: ${error.message}`, 'error');
                log(`登录异常: ${error.message}`);
            }
        }
        
        async function testGetFiles() {
            log('开始获取文件列表...');
            
            if (!authToken) {
                showStatus('files-status', '⚠️ 请先登录', 'warning');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8086/api/files', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                log(`文件列表响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    const files = data.files || [];
                    
                    showStatus('files-status', `✅ 获取到 ${files.length} 个文件`, 'success');
                    log(`文件列表获取成功，共 ${files.length} 个文件`);
                    
                    // 显示文件列表
                    const filesList = document.getElementById('files-list');
                    filesList.innerHTML = '<h4>文件列表:</h4>' + files.map(file => 
                        `<div>ID: ${file.id}, 名称: ${file.name}, 类型: ${file.type || 'file'}</div>`
                    ).join('');
                    
                    // 如果有图片文件，自动设置第一个图片文件的ID
                    const imageFile = files.find(file => {
                        if (file.type === 'folder') return false;
                        const ext = file.name.toLowerCase().split('.').pop();
                        return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext);
                    });
                    
                    if (imageFile) {
                        document.getElementById('file-id').value = imageFile.id;
                        log(`找到图片文件: ${imageFile.name} (ID: ${imageFile.id})`);
                    }
                } else {
                    const errorText = await response.text();
                    showStatus('files-status', `❌ 获取文件列表失败: ${response.status}`, 'error');
                    log(`获取文件列表失败: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                showStatus('files-status', `❌ 获取文件列表异常: ${error.message}`, 'error');
                log(`获取文件列表异常: ${error.message}`);
            }
        }
        
        async function testThumbnailDirect() {
            const fileId = document.getElementById('file-id').value;
            log(`开始测试缩略图，文件ID: ${fileId}`);
            
            if (!authToken) {
                showStatus('thumbnail-status', '⚠️ 请先登录', 'warning');
                return;
            }
            
            if (!fileId) {
                showStatus('thumbnail-status', '⚠️ 请输入文件ID', 'warning');
                return;
            }
            
            const thumbnailUrl = `http://localhost:8086/api/files/${fileId}/thumbnail?size=medium`;
            log(`缩略图URL: ${thumbnailUrl}`);
            
            try {
                // 清空容器
                const container = document.getElementById('thumbnail-container');
                container.innerHTML = '<span>加载中...</span>';
                
                const response = await fetch(thumbnailUrl, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                log(`缩略图响应状态: ${response.status}`);
                log(`响应头 Content-Type: ${response.headers.get('Content-Type')}`);
                log(`响应头 Content-Length: ${response.headers.get('Content-Length')}`);
                
                if (response.ok) {
                    const contentType = response.headers.get('Content-Type');
                    
                    if (contentType && contentType.startsWith('image/')) {
                        // 是图片，创建图片元素
                        const blob = await response.blob();
                        const imageUrl = URL.createObjectURL(blob);
                        
                        const img = document.createElement('img');
                        img.src = imageUrl;
                        img.onload = () => {
                            container.innerHTML = '';
                            container.appendChild(img);
                            showStatus('thumbnail-status', '✅ 缩略图加载成功', 'success');
                            log(`缩略图加载成功，图片尺寸: ${img.naturalWidth}x${img.naturalHeight}`);
                        };
                        img.onerror = () => {
                            container.innerHTML = '<span style="color: red;">图片加载失败</span>';
                            showStatus('thumbnail-status', '❌ 图片加载失败', 'error');
                            log('图片加载失败');
                        };
                    } else {
                        // 不是图片，显示响应内容
                        const text = await response.text();
                        container.innerHTML = `<span style="color: orange;">非图片响应: ${text.substring(0, 100)}</span>`;
                        showStatus('thumbnail-status', '⚠️ 响应不是图片格式', 'warning');
                        log(`响应内容: ${text}`);
                    }
                } else {
                    const errorText = await response.text();
                    container.innerHTML = `<span style="color: red;">HTTP ${response.status}</span>`;
                    showStatus('thumbnail-status', `❌ 缩略图请求失败: ${response.status}`, 'error');
                    log(`缩略图请求失败: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                container.innerHTML = `<span style="color: red;">请求异常</span>`;
                showStatus('thumbnail-status', `❌ 缩略图请求异常: ${error.message}`, 'error');
                log(`缩略图请求异常: ${error.message}`);
            }
        }
        
        // 页面加载时自动登录
        window.onload = function() {
            log('页面加载完成');
            testLogin();
        };
    </script>
</body>
</html>
