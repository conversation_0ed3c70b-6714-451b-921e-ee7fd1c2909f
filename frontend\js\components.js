/**
 * UI组件库
 * 提供可复用的UI组件
 */

const Components = {
    /**
     * Toast通知组件
     */
    Toast: {
        container: null,
        recentMessages: new Map(), // 存储最近显示的消息

        init() {
            this.container = Utils.dom.$('#toast-container');
            if (!this.container) {
                this.container = Utils.dom.create('div', {
                    id: 'toast-container',
                    className: 'toast-container'
                });
                document.body.appendChild(this.container);
            }
        },

        show(message, type = 'info', duration = CONFIG.UI.NOTIFICATION.DURATION) {
            if (!this.container) this.init();

            // 防重复显示：检查相同消息是否在短时间内已显示
            const messageKey = `${type}:${message}`;
            const now = Date.now();
            const lastShown = this.recentMessages.get(messageKey);

            if (lastShown && (now - lastShown) < 3000) { // 3秒内不重复显示相同消息
                console.log(`Toast重复消息被阻止: ${message}`);
                return null;
            }

            this.recentMessages.set(messageKey, now);

            // 清理过期的消息记录
            this.cleanupRecentMessages();
            
            const toast = Utils.dom.create('div', {
                className: `toast ${type}`,
                innerHTML: `
                    <div class="toast-header">
                        <span class="toast-title">${this.getTypeTitle(type)}</span>
                        <button class="toast-close">&times;</button>
                    </div>
                    <div class="toast-message">${message}</div>
                `
            });
            
            // 添加关闭事件
            const closeBtn = toast.querySelector('.toast-close');
            Utils.event.on(closeBtn, 'click', () => this.hide(toast));
            
            // 添加到容器
            this.container.appendChild(toast);
            
            // 显示动画
            setTimeout(() => Utils.dom.addClass(toast, 'show'), 10);
            
            // 自动隐藏
            if (duration > 0) {
                setTimeout(() => this.hide(toast), duration);
            }
            
            return toast;
        },
        
        hide(toast) {
            Utils.dom.removeClass(toast, 'show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        },

        cleanupRecentMessages() {
            const now = Date.now();
            const expireTime = 5000; // 5秒后清理

            for (const [key, timestamp] of this.recentMessages.entries()) {
                if (now - timestamp > expireTime) {
                    this.recentMessages.delete(key);
                }
            }
        },

        getTypeTitle(type) {
            const titles = {
                success: '成功',
                error: '错误',
                warning: '警告',
                info: '信息'
            };
            return titles[type] || '通知';
        },
        
        success(message, duration) {
            return this.show(message, 'success', duration);
        },
        
        error(message, duration) {
            return this.show(message, 'error', duration);
        },
        
        warning(message, duration) {
            return this.show(message, 'warning', duration);
        },
        
        info(message, duration) {
            return this.show(message, 'info', duration);
        }
    },
    
    /**
     * 模态框组件
     */
    Modal: {
        show(modalId) {
            const modal = Utils.dom.$(`#${modalId}`);
            if (modal) {
                Utils.dom.addClass(modal, 'show');
                Utils.dom.addClass(document.body, 'modal-open');
                
                // 添加ESC键关闭
                const escHandler = (e) => {
                    if (e.key === 'Escape') {
                        this.hide(modalId);
                        Utils.event.off(document, 'keydown', escHandler);
                    }
                };
                Utils.event.on(document, 'keydown', escHandler);
                
                // 添加背景点击关闭
                const clickHandler = (e) => {
                    if (e.target === modal) {
                        this.hide(modalId);
                    }
                };
                Utils.event.on(modal, 'click', clickHandler);
            }
        },
        
        hide(modalId) {
            const modal = Utils.dom.$(`#${modalId}`);
            if (modal) {
                Utils.dom.removeClass(modal, 'show');
                Utils.dom.removeClass(document.body, 'modal-open');
            }
        },
        
        toggle(modalId) {
            const modal = Utils.dom.$(`#${modalId}`);
            if (modal && Utils.dom.hasClass(modal, 'show')) {
                this.hide(modalId);
            } else {
                this.show(modalId);
            }
        }
    },
    
    /**
     * 加载动画组件
     */
    Loading: {
        show(message = '正在加载...') {
            let overlay = Utils.dom.$('#loading-overlay');
            if (!overlay) {
                overlay = Utils.dom.create('div', {
                    id: 'loading-overlay',
                    className: 'loading-overlay',
                    innerHTML: `
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                            <p>${message}</p>
                        </div>
                    `
                });
                document.body.appendChild(overlay);
            } else {
                const text = overlay.querySelector('p');
                if (text) text.textContent = message;
            }
            
            Utils.dom.removeClass(overlay, 'hidden');
            return overlay;
        },
        
        hide() {
            const overlay = Utils.dom.$('#loading-overlay');
            if (overlay) {
                Utils.dom.addClass(overlay, 'hidden');
            }
        }
    },
    
    /**
     * 确认对话框组件
     */
    Confirm: {
        show(message, title = '确认', onConfirm = null, onCancel = null) {
            return new Promise((resolve) => {
                const modal = Utils.dom.create('div', {
                    className: 'modal show',
                    innerHTML: `
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>${title}</h3>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                            </div>
                            <div class="modal-footer">
                                <button class="btn btn-secondary cancel-btn">取消</button>
                                <button class="btn btn-primary confirm-btn">确认</button>
                            </div>
                        </div>
                    `
                });
                
                const confirmBtn = modal.querySelector('.confirm-btn');
                const cancelBtn = modal.querySelector('.cancel-btn');
                
                const cleanup = () => {
                    document.body.removeChild(modal);
                    Utils.dom.removeClass(document.body, 'modal-open');
                };
                
                Utils.event.on(confirmBtn, 'click', () => {
                    cleanup();
                    if (onConfirm) onConfirm();
                    resolve(true);
                });
                
                Utils.event.on(cancelBtn, 'click', () => {
                    cleanup();
                    if (onCancel) onCancel();
                    resolve(false);
                });
                
                document.body.appendChild(modal);
                Utils.dom.addClass(document.body, 'modal-open');
            });
        }
    },
    
    /**
     * 右键菜单组件
     */
    ContextMenu: {
        current: null,
        
        show(x, y, items) {
            this.hide();
            
            const menu = Utils.dom.create('div', {
                className: 'context-menu show',
                style: `left: ${x}px; top: ${y}px;`,
                innerHTML: `
                    <ul>
                        ${items.map(item => {
                            if (item.divider) {
                                return '<li class="divider"></li>';
                            }
                            return `
                                <li data-action="${item.action}">
                                    <i class="${item.icon}"></i>
                                    ${item.text}
                                </li>
                            `;
                        }).join('')}
                    </ul>
                `
            });
            
            // 添加点击事件
            Utils.event.on(menu, 'click', (e) => {
                const item = e.target.closest('li');
                if (item && item.dataset.action) {
                    const action = item.dataset.action;
                    const selectedItem = items.find(i => i.action === action);
                    if (selectedItem && selectedItem.handler) {
                        selectedItem.handler();
                    }
                    this.hide();
                }
            });
            
            document.body.appendChild(menu);
            this.current = menu;
            
            // 调整位置防止超出屏幕
            this.adjustPosition(menu);
            
            // 点击其他地方关闭
            setTimeout(() => {
                Utils.event.on(document, 'click', this.hideHandler.bind(this));
            }, 10);
        },
        
        hide() {
            if (this.current) {
                Utils.event.off(document, 'click', this.hideHandler);
                document.body.removeChild(this.current);
                this.current = null;
            }
        },
        
        hideHandler(e) {
            if (!this.current || !this.current.contains(e.target)) {
                this.hide();
            }
        },
        
        adjustPosition(menu) {
            const rect = menu.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            
            if (rect.right > viewportWidth) {
                menu.style.left = (viewportWidth - rect.width - 10) + 'px';
            }
            
            if (rect.bottom > viewportHeight) {
                menu.style.top = (viewportHeight - rect.height - 10) + 'px';
            }
        }
    },
    
    /**
     * 进度条组件
     */
    ProgressBar: {
        create(container, options = {}) {
            const {
                value = 0,
                max = 100,
                showText = true,
                className = ''
            } = options;
            
            const progressBar = Utils.dom.create('div', {
                className: `progress-bar ${className}`,
                innerHTML: `
                    <div class="progress-fill" style="width: ${(value / max) * 100}%"></div>
                    ${showText ? `<span class="progress-text">${value}%</span>` : ''}
                `
            });
            
            if (container) {
                container.appendChild(progressBar);
            }
            
            return {
                element: progressBar,
                setValue(newValue) {
                    const percentage = Math.min(100, Math.max(0, (newValue / max) * 100));
                    const fill = progressBar.querySelector('.progress-fill');
                    const text = progressBar.querySelector('.progress-text');
                    
                    if (fill) {
                        fill.style.width = percentage + '%';
                    }
                    
                    if (text) {
                        text.textContent = Math.round(newValue) + '%';
                    }
                }
            };
        }
    },
    
    /**
     * 分页组件
     */
    Pagination: {
        create(container, options = {}) {
            const {
                currentPage = 1,
                totalPages = 1,
                onPageChange = null,
                showInfo = true
            } = options;
            
            const pagination = Utils.dom.create('div', {
                className: 'pagination',
                innerHTML: this.generateHTML(currentPage, totalPages, showInfo)
            });
            
            // 添加点击事件
            Utils.event.on(pagination, 'click', (e) => {
                const button = e.target.closest('.page-btn');
                if (button && !Utils.dom.hasClass(button, 'disabled')) {
                    const page = parseInt(button.dataset.page);
                    if (page && onPageChange) {
                        onPageChange(page);
                    }
                }
            });
            
            if (container) {
                container.appendChild(pagination);
            }
            
            return {
                element: pagination,
                update(newCurrentPage, newTotalPages) {
                    pagination.innerHTML = this.generateHTML(newCurrentPage, newTotalPages, showInfo);
                }
            };
        },
        
        generateHTML(currentPage, totalPages, showInfo) {
            let html = '<div class="pagination-buttons">';
            
            // 上一页
            html += `
                <button class="page-btn ${currentPage <= 1 ? 'disabled' : ''}" data-page="${currentPage - 1}">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
            
            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <button class="page-btn ${i === currentPage ? 'active' : ''}" data-page="${i}">
                        ${i}
                    </button>
                `;
            }
            
            // 下一页
            html += `
                <button class="page-btn ${currentPage >= totalPages ? 'disabled' : ''}" data-page="${currentPage + 1}">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
            
            html += '</div>';
            
            // 页面信息
            if (showInfo) {
                html += `
                    <div class="pagination-info">
                        第 ${currentPage} 页，共 ${totalPages} 页
                    </div>
                `;
            }
            
            return html;
        }
    },

    /**
     * 图片预览浮窗组件
     */
    ImagePreviewFloat: {
        isVisible: false,
        isDragging: false,
        isMaximized: false,
        isMinimized: false,
        currentZoom: 1,
        currentRotation: 0,
        dragOffset: { x: 0, y: 0 },

        init() {
            console.log('ImagePreviewFloat: 开始初始化');

            this.overlay = Utils.dom.$('#image-preview-overlay');
            this.floatWindow = Utils.dom.$('#image-preview-float');
            this.header = Utils.dom.$('#float-header');
            this.title = Utils.dom.$('#float-title');
            this.image = Utils.dom.$('#float-image');
            this.content = Utils.dom.$('#float-content');
            this.zoomInfo = Utils.dom.$('#zoom-info');

            console.log('ImagePreviewFloat: DOM元素查找结果:', {
                overlay: !!this.overlay,
                floatWindow: !!this.floatWindow,
                header: !!this.header,
                title: !!this.title,
                image: !!this.image,
                content: !!this.content,
                zoomInfo: !!this.zoomInfo
            });

            if (!this.overlay || !this.floatWindow) {
                console.error('ImagePreviewFloat: 关键DOM元素未找到，初始化失败');
                return false;
            }

            this.bindEvents();
            this.initialized = true;
            console.log('ImagePreviewFloat: 初始化完成');
            return true;
        },

        ensureInitialized() {
            if (!this.initialized) {
                console.log('ImagePreviewFloat: 延迟初始化');
                return this.init();
            }
            return true;
        },

        bindEvents() {
            // 检查所有必需的元素是否存在
            if (!this.overlay || !this.floatWindow || !this.header || !this.image || !this.content) {
                console.error('ImagePreviewFloat: 必需的DOM元素未找到');
                return;
            }

            // 关闭按钮
            const closeBtn = Utils.dom.$('#float-close');
            if (closeBtn) {
                Utils.event.on(closeBtn, 'click', () => this.hide());
            }

            // 最小化按钮
            const minimizeBtn = Utils.dom.$('#float-minimize');
            if (minimizeBtn) {
                Utils.event.on(minimizeBtn, 'click', () => this.minimize());
            }

            // 最大化按钮
            const maximizeBtn = Utils.dom.$('#float-maximize');
            if (maximizeBtn) {
                Utils.event.on(maximizeBtn, 'click', () => this.maximize());
            }

            // 缩放控制
            const zoomInBtn = Utils.dom.$('#zoom-in');
            if (zoomInBtn) {
                Utils.event.on(zoomInBtn, 'click', () => this.zoomIn());
            }

            const zoomOutBtn = Utils.dom.$('#zoom-out');
            if (zoomOutBtn) {
                Utils.event.on(zoomOutBtn, 'click', () => this.zoomOut());
            }

            const zoomFitBtn = Utils.dom.$('#zoom-fit');
            if (zoomFitBtn) {
                Utils.event.on(zoomFitBtn, 'click', () => this.zoomFit());
            }

            const zoomActualBtn = Utils.dom.$('#zoom-actual');
            if (zoomActualBtn) {
                Utils.event.on(zoomActualBtn, 'click', () => this.zoomActual());
            }

            // 旋转控制
            const rotateLeftBtn = Utils.dom.$('#rotate-left');
            if (rotateLeftBtn) {
                Utils.event.on(rotateLeftBtn, 'click', () => this.rotateLeft());
            }

            const rotateRightBtn = Utils.dom.$('#rotate-right');
            if (rotateRightBtn) {
                Utils.event.on(rotateRightBtn, 'click', () => this.rotateRight());
            }

            // 收藏和下载功能
            const favoriteBtn = Utils.dom.$('#float-favorite');
            if (favoriteBtn) {
                Utils.event.on(favoriteBtn, 'click', () => this.toggleFavorite());
            }

            const downloadBtn = Utils.dom.$('#float-download');
            if (downloadBtn) {
                Utils.event.on(downloadBtn, 'click', () => this.downloadFile());
            }

            // 拖拽功能
            Utils.event.on(this.header, 'mousedown', (e) => this.startDrag(e));
            Utils.event.on(document, 'mousemove', (e) => this.drag(e));
            Utils.event.on(document, 'mouseup', () => this.endDrag());

            // 点击遮罩层关闭
            Utils.event.on(this.overlay, 'click', () => this.hide());

            // 键盘快捷键
            Utils.event.on(document, 'keydown', (e) => this.handleKeyboard(e));

            // 图片拖拽
            Utils.event.on(this.image, 'mousedown', (e) => this.startImageDrag(e));

            // 鼠标滚轮缩放
            Utils.event.on(this.content, 'wheel', (e) => this.handleWheel(e));
        },

        show(imageUrl, title = '图片预览', fileId = null) {
            console.log('ImagePreviewFloat.show 被调用:', { imageUrl, title, fileId });

            // 确保已初始化
            if (!this.ensureInitialized()) {
                console.error('ImagePreviewFloat: 初始化失败，无法显示浮窗');
                return;
            }

            this.title.textContent = title;
            this.image.src = imageUrl;
            this.currentZoom = 1;
            this.currentRotation = 0;
            this.currentFileId = fileId; // 保存文件ID用于收藏功能
            this.updateImageTransform();
            this.updateZoomInfo();

            this.overlay.classList.add('show');
            this.floatWindow.classList.add('show');
            this.isVisible = true;

            // 重置位置
            this.resetPosition();

            console.log('ImagePreviewFloat: 浮窗已显示');
        },

        hide() {
            this.overlay.classList.remove('show');
            this.floatWindow.classList.remove('show');
            this.isVisible = false;
            this.isMaximized = false;
            this.isMinimized = false;
        },

        minimize() {
            if (this.isMinimized) {
                this.restore();
            } else {
                this.floatWindow.style.height = '40px';
                this.content.style.display = 'none';
                Utils.dom.$('.float-toolbar').style.display = 'none';
                this.isMinimized = true;
            }
        },

        maximize() {
            if (this.isMaximized) {
                this.restore();
            } else {
                this.floatWindow.style.top = '0';
                this.floatWindow.style.left = '0';
                this.floatWindow.style.transform = 'none';
                this.floatWindow.style.width = '100vw';
                this.floatWindow.style.height = '100vh';
                this.floatWindow.style.maxWidth = 'none';
                this.floatWindow.style.maxHeight = 'none';
                this.isMaximized = true;
            }
        },

        restore() {
            this.resetPosition();
            this.content.style.display = 'block';
            Utils.dom.$('.float-toolbar').style.display = 'flex';
            this.isMinimized = false;
            this.isMaximized = false;
        },

        resetPosition() {
            this.floatWindow.style.top = '50%';
            this.floatWindow.style.left = '50%';
            this.floatWindow.style.transform = 'translate(-50%, -50%)';
            this.floatWindow.style.width = 'auto';
            this.floatWindow.style.height = 'auto';
            this.floatWindow.style.maxWidth = '90vw';
            this.floatWindow.style.maxHeight = '90vh';
        },

        zoomIn() {
            this.currentZoom = Math.min(this.currentZoom * 1.2, 5);
            this.updateImageTransform();
            this.updateZoomInfo();
        },

        zoomOut() {
            this.currentZoom = Math.max(this.currentZoom / 1.2, 0.1);
            this.updateImageTransform();
            this.updateZoomInfo();
        },

        zoomFit() {
            this.currentZoom = 1;
            this.updateImageTransform();
            this.updateZoomInfo();
        },

        zoomActual() {
            this.currentZoom = 1;
            this.updateImageTransform();
            this.updateZoomInfo();
        },

        rotateLeft() {
            this.currentRotation -= 90;
            this.updateImageTransform();
        },

        rotateRight() {
            this.currentRotation += 90;
            this.updateImageTransform();
        },

        updateImageTransform() {
            this.image.style.transform = `scale(${this.currentZoom}) rotate(${this.currentRotation}deg)`;
        },

        updateZoomInfo() {
            this.zoomInfo.textContent = `${Math.round(this.currentZoom * 100)}%`;
        },

        startDrag(e) {
            if (this.isMaximized) return;

            this.isDragging = true;
            const rect = this.floatWindow.getBoundingClientRect();
            this.dragOffset.x = e.clientX - rect.left;
            this.dragOffset.y = e.clientY - rect.top;

            this.header.style.cursor = 'grabbing';
            e.preventDefault();
        },

        drag(e) {
            if (!this.isDragging) return;

            const x = e.clientX - this.dragOffset.x;
            const y = e.clientY - this.dragOffset.y;

            this.floatWindow.style.left = x + 'px';
            this.floatWindow.style.top = y + 'px';
            this.floatWindow.style.transform = 'none';
        },

        endDrag() {
            this.isDragging = false;
            this.header.style.cursor = 'move';
        },

        startImageDrag(e) {
            // 图片拖拽功能（可选实现）
            e.preventDefault();
        },

        handleWheel(e) {
            e.preventDefault();

            if (e.deltaY < 0) {
                this.zoomIn();
            } else {
                this.zoomOut();
            }
        },

        handleKeyboard(e) {
            if (!this.isVisible) return;

            switch (e.key) {
                case 'Escape':
                    this.hide();
                    break;
                case '+':
                case '=':
                    this.zoomIn();
                    break;
                case '-':
                    this.zoomOut();
                    break;
                case '0':
                    this.zoomActual();
                    break;
                case 'r':
                case 'R':
                    this.rotateRight();
                    break;
                case 'f':
                case 'F':
                    this.toggleFavorite();
                    break;
                case 'd':
                case 'D':
                    this.downloadFile();
                    break;
            }
        },

        /**
         * 切换收藏状态
         */
        toggleFavorite() {
            if (!this.currentFileId) {
                Components.Toast.warning('无法收藏：文件ID不可用');
                return;
            }

            // 调用文件管理器的收藏功能
            if (window.fileManager && typeof window.fileManager.favoriteFile === 'function') {
                window.fileManager.favoriteFile(this.currentFileId);

                // 更新浮窗中的收藏按钮状态
                setTimeout(() => {
                    this.updateFavoriteButton();
                }, 500);
            } else {
                Components.Toast.error('收藏功能不可用');
            }
        },

        /**
         * 下载文件
         */
        downloadFile() {
            if (!this.currentFileId) {
                Components.Toast.warning('无法下载：文件ID不可用');
                return;
            }

            // 调用文件管理器的下载功能
            if (window.fileManager && typeof window.fileManager.downloadFile === 'function') {
                window.fileManager.downloadFile(this.currentFileId);
            } else {
                Components.Toast.error('下载功能不可用');
            }
        },

        /**
         * 更新收藏按钮状态
         */
        updateFavoriteButton() {
            if (!this.currentFileId) return;

            const favoriteBtn = Utils.dom.$('#float-favorite');
            if (!favoriteBtn) return;

            // 检查文件是否已收藏
            const isFavorited = window.fileManager && window.fileManager.isFileFavorited
                ? window.fileManager.isFileFavorited(this.currentFileId)
                : false;

            const icon = favoriteBtn.querySelector('i');
            if (icon) {
                icon.className = isFavorited ? 'fas fa-star' : 'far fa-star';
                favoriteBtn.title = isFavorited ? '取消收藏' : '收藏';

                if (isFavorited) {
                    favoriteBtn.classList.add('favorited');
                } else {
                    favoriteBtn.classList.remove('favorited');
                }
            }
        }
    }
};

// 初始化组件
document.addEventListener('DOMContentLoaded', () => {
    console.log('Components: DOMContentLoaded 事件触发');

    // 初始化Toast容器
    Components.Toast.init();
    console.log('Components: Toast 初始化完成');

    // 延迟初始化图片预览浮窗，确保DOM完全加载
    setTimeout(() => {
        try {
            const result = Components.ImagePreviewFloat.init();
            console.log('Components: ImagePreviewFloat 初始化结果:', result);
        } catch (error) {
            console.error('Components: ImagePreviewFloat 初始化失败:', error);
        }
    }, 100);

    // 初始化模态框关闭按钮
    Utils.dom.$$('.modal-close').forEach(btn => {
        Utils.event.on(btn, 'click', (e) => {
            const modal = e.target.closest('.modal');
            if (modal) {
                Components.Modal.hide(modal.id);
            }
        });
    });

    console.log('Components: 所有组件初始化完成');
});

// 全局可用
window.Components = Components;

// 调试函数
window.debugImagePreview = function() {
    console.log('=== 图片预览调试信息 ===');
    console.log('Components:', typeof Components);
    console.log('Components.ImagePreviewFloat:', Components ? Components.ImagePreviewFloat : 'undefined');
    console.log('浮窗元素:', {
        overlay: !!document.getElementById('image-preview-overlay'),
        floatWindow: !!document.getElementById('image-preview-float'),
        header: !!document.getElementById('float-header'),
        title: !!document.getElementById('float-title'),
        image: !!document.getElementById('float-image'),
        content: !!document.getElementById('float-content'),
        zoomInfo: !!document.getElementById('zoom-info')
    });
    console.log('初始化状态:', Components && Components.ImagePreviewFloat ? Components.ImagePreviewFloat.initialized : 'N/A');
    console.log('=== 调试信息结束 ===');
};
