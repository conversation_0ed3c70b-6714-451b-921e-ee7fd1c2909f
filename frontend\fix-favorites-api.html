<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复收藏数据</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复收藏数据</h1>
        
        <div>
            <button class="btn" onclick="checkFavorites()">📋 检查收藏状态</button>
            <button class="btn" onclick="testDirectSQL()">🔍 直接SQL查询</button>
            <button class="btn danger" onclick="fixFavorites()">🛠️ 修复收藏数据</button>
        </div>
        
        <div id="status" class="status">等待操作...</div>
        <div id="result" class="result">点击按钮开始检查...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        
        function updateStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function updateResult(content) {
            document.getElementById('result').textContent = content;
        }
        
        function log(message) {
            const current = document.getElementById('result').textContent;
            const timestamp = new Date().toLocaleTimeString();
            document.getElementById('result').textContent = current + `\n[${timestamp}] ${message}`;
        }
        
        async function getAuthToken() {
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                throw new Error('未找到认证信息，请先登录');
            }
            
            const auth = JSON.parse(authData);
            if (!auth.token) {
                throw new Error('认证token不存在');
            }
            
            return auth.token;
        }
        
        async function apiCall(endpoint, options = {}) {
            try {
                const token = await getAuthToken();
                
                const url = `${API_BASE}${endpoint}`;
                const config = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        ...options.headers
                    },
                    ...options
                };
                
                log(`请求: ${config.method} ${url}`);
                
                const response = await fetch(url, config);
                const data = await response.json();
                
                log(`响应状态: ${response.status}`);
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.error || response.statusText}`);
                }
                
                return data;
                
            } catch (error) {
                log(`❌ 错误: ${error.message}`);
                throw error;
            }
        }
        
        async function checkFavorites() {
            updateResult('');
            log('=== 检查收藏状态 ===');
            
            try {
                // 测试不同的分页参数
                const testCases = [
                    { desc: '默认参数', params: '' },
                    { desc: '大页面', params: '?page=1&page_size=100' },
                    { desc: '超大页面', params: '?page=1&page_size=1000' },
                ];
                
                for (const testCase of testCases) {
                    log(`\n--- ${testCase.desc} ---`);
                    const result = await apiCall(`/favorites${testCase.params}`);
                    
                    if (result.favorites) {
                        log(`✅ ${testCase.desc}: ${result.favorites.length} 个收藏`);
                        log(`   总数: ${result.total_count || '未知'}`);
                        log(`   页码: ${result.page || '未知'}/${result.total_pages || '未知'}`);
                        
                        if (result.favorites.length > 0) {
                            log('   收藏列表:');
                            result.favorites.forEach((fav, index) => {
                                log(`     ${index + 1}. ID: ${fav.id}, 文件ID: ${fav.file_id}`);
                                if (fav.file) {
                                    log(`        文件: ${fav.file.filename}`);
                                }
                            });
                        }
                    } else {
                        log(`❌ ${testCase.desc}: 响应格式异常`);
                    }
                }
                
                updateStatus('检查完成', 'success');
                
            } catch (error) {
                updateStatus(`检查失败: ${error.message}`, 'error');
            }
        }
        
        async function testDirectSQL() {
            updateResult('');
            log('=== 直接SQL查询测试 ===');
            
            try {
                // 这里我们通过API来模拟SQL查询
                log('通过API检查数据库状态...');
                
                // 获取收藏统计
                const stats = await apiCall('/favorites/stats');
                log(`收藏统计: ${JSON.stringify(stats, null, 2)}`);
                
                updateStatus('SQL查询完成', 'success');
                
            } catch (error) {
                updateStatus(`SQL查询失败: ${error.message}`, 'error');
            }
        }
        
        async function fixFavorites() {
            updateResult('');
            log('=== 修复收藏数据 ===');
            
            try {
                log('注意：这是一个模拟修复过程');
                log('实际修复需要在后端进行数据库操作');
                
                // 先检查当前状态
                const current = await apiCall('/favorites?page=1&page_size=1000');
                log(`当前收藏数: ${current.favorites ? current.favorites.length : 0}`);
                log(`总数: ${current.total_count || '未知'}`);
                
                if (current.favorites && current.favorites.length > 0) {
                    log('\n当前收藏列表:');
                    current.favorites.forEach((fav, index) => {
                        log(`  ${index + 1}. 收藏ID: ${fav.id}, 文件ID: ${fav.file_id}`);
                        if (fav.file) {
                            log(`     文件名: ${fav.file.filename}`);
                            log(`     是否图片: ${fav.file.is_image}`);
                        } else {
                            log(`     ⚠️ 文件信息缺失`);
                        }
                    });
                }
                
                log('\n✅ 检查完成');
                log('如果收藏数量不正确，请：');
                log('1. 重启后端服务器');
                log('2. 检查数据库中的 user_favorites 表');
                log('3. 确认 is_active 字段为 1');
                log('4. 确认对应的文件在 shared_files 表中存在');
                
                updateStatus('修复检查完成', 'success');
                
            } catch (error) {
                updateStatus(`修复失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
