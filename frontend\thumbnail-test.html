<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缩略图测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .thumbnail-test {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .thumbnail-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }
        .thumbnail-item img {
            max-width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
        }
        .thumbnail-item .error {
            color: red;
            font-size: 12px;
        }
        .thumbnail-item .success {
            color: green;
            font-size: 12px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 缩略图功能测试</h1>
        <p>此页面用于测试缩略图生成和显示功能</p>
        
        <div class="controls">
            <button class="btn" onclick="testThumbnailAPI()">测试缩略图API</button>
            <button class="btn" onclick="testDirectThumbnailURL()">直接测试缩略图URL</button>
            <button class="btn" onclick="loadFileList()">加载文件列表</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="status"></div>
    </div>

    <div class="container" id="results-container">
        <h2>📋 测试结果</h2>
        <div id="thumbnail-test" class="thumbnail-test"></div>
        <pre id="logs"></pre>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    
    <script>
        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            logs.push(logEntry);
            updateLogsDisplay();
            console.log(logEntry);
        }

        function updateLogsDisplay() {
            const logsElement = document.getElementById('logs');
            logsElement.textContent = logs.join('\n');
            logsElement.scrollTop = logsElement.scrollHeight;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            statusDiv.innerHTML = `<div class="${className}">${message}</div>`;
        }

        function clearResults() {
            logs = [];
            updateLogsDisplay();
            document.getElementById('thumbnail-test').innerHTML = '';
            showStatus('结果已清空', 'info');
        }

        // 测试缩略图API
        async function testThumbnailAPI() {
            log('开始测试缩略图API...');

            try {
                // 首先获取文件列表
                const files = await loadFileList();
                if (!files || files.length === 0) {
                    log('没有找到文件，无法测试缩略图', 'error');
                    return;
                }

                // 找到图片文件
                const imageFiles = files.filter(file => {
                    if (file.type === 'folder') return false;
                    const ext = file.name.toLowerCase().split('.').pop();
                    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext);
                });

                if (imageFiles.length === 0) {
                    log('没有找到图片文件，无法测试缩略图', 'error');
                    return;
                }

                log(`找到 ${imageFiles.length} 个图片文件，开始测试缩略图...`);

                const container = document.getElementById('thumbnail-test');
                container.innerHTML = '';

                // 测试每个图片文件的缩略图
                for (const file of imageFiles.slice(0, 6)) { // 只测试前6个文件
                    await testSingleThumbnail(file, container);
                }

                showStatus('缩略图测试完成', 'success');

            } catch (error) {
                log(`缩略图API测试失败: ${error.message}`, 'error');
                showStatus(`缩略图API测试失败: ${error.message}`, 'error');
            }
        }

        // 直接测试缩略图URL
        async function testDirectThumbnailURL() {
            log('开始直接测试缩略图URL...');

            try {
                // 测试已知的文件ID
                const testFileIds = [3, 4]; // 从日志中看到的文件ID

                for (const fileId of testFileIds) {
                    const thumbnailUrl = `http://localhost:8086/api/files/${fileId}/thumbnail?size=medium`;
                    log(`测试缩略图URL: ${thumbnailUrl}`);

                    // 使用fetch测试API
                    const token = localStorage.getItem('token');
                    const response = await fetch(thumbnailUrl, {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    log(`文件ID ${fileId} - 响应状态: ${response.status} ${response.statusText}`);

                    if (response.ok) {
                        log(`✅ 文件ID ${fileId} 缩略图API响应成功`, 'success');

                        // 创建图片元素测试显示
                        const container = document.getElementById('thumbnail-test');
                        const item = document.createElement('div');
                        item.className = 'thumbnail-item';
                        item.innerHTML = `
                            <h4>文件ID: ${fileId}</h4>
                            <img src="${thumbnailUrl}" style="max-width: 200px; height: 150px; object-fit: cover;"
                                 onload="console.log('图片加载成功: ${fileId}')"
                                 onerror="console.log('图片加载失败: ${fileId}')">
                            <div>状态: API响应成功</div>
                        `;
                        container.appendChild(item);

                    } else {
                        log(`❌ 文件ID ${fileId} 缩略图API响应失败: ${response.status}`, 'error');

                        const errorText = await response.text();
                        log(`错误详情: ${errorText}`, 'error');
                    }
                }

            } catch (error) {
                log(`直接测试缩略图URL失败: ${error.message}`, 'error');
            }
        }

        // 测试单个文件的缩略图
        async function testSingleThumbnail(file, container) {
            const item = document.createElement('div');
            item.className = 'thumbnail-item';
            item.innerHTML = `
                <h4>${file.name}</h4>
                <div id="thumb-${file.id}">正在加载...</div>
                <div id="status-${file.id}"></div>
            `;
            container.appendChild(item);

            try {
                // 生成缩略图URL
                const thumbnailUrl = FileAPI.getThumbnailURL(file.id, 'medium');
                log(`测试文件: ${file.name}, 缩略图URL: ${thumbnailUrl}`);

                // 创建图片元素
                const img = document.createElement('img');
                img.src = thumbnailUrl;
                
                img.onload = () => {
                    document.getElementById(`thumb-${file.id}`).innerHTML = '';
                    document.getElementById(`thumb-${file.id}`).appendChild(img);
                    document.getElementById(`status-${file.id}`).innerHTML = '<span class="success">✅ 缩略图加载成功</span>';
                    log(`✅ ${file.name} 缩略图加载成功`, 'success');
                };

                img.onerror = () => {
                    document.getElementById(`thumb-${file.id}`).innerHTML = '❌ 加载失败';
                    document.getElementById(`status-${file.id}`).innerHTML = '<span class="error">❌ 缩略图加载失败</span>';
                    log(`❌ ${file.name} 缩略图加载失败`, 'error');
                };

            } catch (error) {
                document.getElementById(`thumb-${file.id}`).innerHTML = '❌ 错误';
                document.getElementById(`status-${file.id}`).innerHTML = `<span class="error">❌ ${error.message}</span>`;
                log(`❌ ${file.name} 缩略图测试出错: ${error.message}`, 'error');
            }
        }

        // 加载文件列表
        async function loadFileList() {
            log('开始加载文件列表...');
            
            try {
                if (typeof FileAPI === 'undefined') {
                    throw new Error('FileAPI未加载');
                }

                const response = await FileAPI.getFiles();
                log(`API响应: ${JSON.stringify(response)}`);

                let files = [];
                if (response && response.files) {
                    files = response.files;
                } else if (Array.isArray(response)) {
                    files = response;
                } else if (response && response.data && Array.isArray(response.data)) {
                    files = response.data;
                }

                log(`获取到 ${files.length} 个文件`);
                showStatus(`成功加载 ${files.length} 个文件`, 'success');
                
                return files;

            } catch (error) {
                log(`加载文件列表失败: ${error.message}`, 'error');
                showStatus(`加载文件列表失败: ${error.message}`, 'error');
                return [];
            }
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，准备测试缩略图功能...');
            showStatus('页面加载完成，可以开始测试', 'info');
        });

        // 监听错误
        window.addEventListener('error', (e) => {
            log(`❌ JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });

        window.addEventListener('unhandledrejection', (e) => {
            log(`❌ 未处理的Promise拒绝: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
