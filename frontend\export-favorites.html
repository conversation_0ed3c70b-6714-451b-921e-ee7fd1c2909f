<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏数据导出工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .favorites-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
        }
        
        .favorite-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .favorite-item:last-child {
            border-bottom: none;
        }
        
        .favorite-name {
            font-weight: 500;
        }
        
        .favorite-meta {
            font-size: 12px;
            color: #666;
        }
        
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            resize: vertical;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 收藏数据导出工具</h1>
        
        <div class="info">
            <strong>说明：</strong>此工具用于将浏览器localStorage中的收藏数据导出，以便迁移到新的数据库收藏系统。
        </div>
        
        <!-- 当前收藏状态 -->
        <div class="section">
            <h3>📋 当前收藏状态</h3>
            <div id="favorites-status">
                <p>正在检查收藏数据...</p>
            </div>
            <button class="btn" onclick="checkFavorites()">🔄 刷新状态</button>
        </div>
        
        <!-- 收藏列表 -->
        <div class="section">
            <h3>📁 收藏列表</h3>
            <div id="favorites-display">
                <p>暂无收藏数据</p>
            </div>
        </div>
        
        <!-- 导出功能 -->
        <div class="section">
            <h3>📤 导出收藏数据</h3>
            <div class="warning">
                <strong>注意：</strong>导出的数据将包含所有收藏信息，请妥善保管。
            </div>
            <button class="btn btn-success" onclick="exportFavorites()">📥 导出为JSON文件</button>
            <button class="btn" onclick="showExportData()">👁️ 查看导出数据</button>
        </div>
        
        <!-- 导出数据显示 -->
        <div class="section hidden" id="export-data-section">
            <h3>📄 导出数据预览</h3>
            <textarea id="export-data-textarea" readonly placeholder="导出的JSON数据将显示在这里..."></textarea>
            <button class="btn" onclick="copyExportData()">📋 复制到剪贴板</button>
        </div>
        
        <!-- 清理功能 -->
        <div class="section">
            <h3>🗑️ 清理本地数据</h3>
            <div class="warning">
                <strong>警告：</strong>清理后将无法恢复本地收藏数据，请确保已完成数据库迁移。
            </div>
            <button class="btn btn-warning" onclick="clearFavorites()">🧹 清理localStorage收藏数据</button>
        </div>
        
        <!-- 测试功能 -->
        <div class="section">
            <h3>🧪 测试功能</h3>
            <button class="btn" onclick="addTestFavorites()">➕ 添加测试收藏数据</button>
            <button class="btn btn-danger" onclick="clearAllData()">🗑️ 清除所有测试数据</button>
        </div>
    </div>

    <script>
        // 收藏数据管理
        const STORAGE_KEY = 'fileShareFavorites';
        
        // 页面加载时检查收藏数据
        document.addEventListener('DOMContentLoaded', function() {
            checkFavorites();
        });
        
        // 检查收藏数据
        function checkFavorites() {
            try {
                const favoritesData = localStorage.getItem(STORAGE_KEY);
                const favorites = favoritesData ? JSON.parse(favoritesData) : [];
                
                const statusDiv = document.getElementById('favorites-status');
                const displayDiv = document.getElementById('favorites-display');
                
                if (favorites.length === 0) {
                    statusDiv.innerHTML = '<div class="info">📭 暂无收藏数据</div>';
                    displayDiv.innerHTML = '<p>暂无收藏数据</p>';
                } else {
                    statusDiv.innerHTML = `
                        <div class="success">
                            📊 找到 <strong>${favorites.length}</strong> 条收藏记录
                            <br>存储大小: <strong>${(JSON.stringify(favorites).length / 1024).toFixed(2)} KB</strong>
                        </div>
                    `;
                    
                    // 显示收藏列表
                    let listHtml = '<div class="favorites-list">';
                    favorites.forEach((fav, index) => {
                        listHtml += `
                            <div class="favorite-item">
                                <div>
                                    <div class="favorite-name">${fav.name || '未知文件'}</div>
                                    <div class="favorite-meta">
                                        ID: ${fav.id} | 
                                        大小: ${formatFileSize(fav.size || 0)} | 
                                        收藏时间: ${formatDate(fav.favorited_at)}
                                    </div>
                                </div>
                                <div class="favorite-meta">
                                    文件夹: ${fav.folder_name || '未知'}
                                </div>
                            </div>
                        `;
                    });
                    listHtml += '</div>';
                    displayDiv.innerHTML = listHtml;
                }
                
            } catch (error) {
                console.error('检查收藏数据失败:', error);
                document.getElementById('favorites-status').innerHTML = 
                    '<div class="error">❌ 检查收藏数据失败: ' + error.message + '</div>';
            }
        }
        
        // 导出收藏数据
        function exportFavorites() {
            try {
                const favoritesData = localStorage.getItem(STORAGE_KEY);
                const favorites = favoritesData ? JSON.parse(favoritesData) : [];
                
                if (favorites.length === 0) {
                    alert('没有收藏数据可导出');
                    return;
                }
                
                const exportData = {
                    export_time: new Date().toISOString(),
                    export_version: '1.0',
                    total_count: favorites.length,
                    favorites: favorites
                };
                
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `favorites_export_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                alert(`✅ 成功导出 ${favorites.length} 条收藏记录`);
                
            } catch (error) {
                console.error('导出失败:', error);
                alert('❌ 导出失败: ' + error.message);
            }
        }
        
        // 显示导出数据
        function showExportData() {
            try {
                const favoritesData = localStorage.getItem(STORAGE_KEY);
                const favorites = favoritesData ? JSON.parse(favoritesData) : [];
                
                const exportData = {
                    export_time: new Date().toISOString(),
                    export_version: '1.0',
                    total_count: favorites.length,
                    favorites: favorites
                };
                
                const dataStr = JSON.stringify(exportData, null, 2);
                document.getElementById('export-data-textarea').value = dataStr;
                document.getElementById('export-data-section').classList.remove('hidden');
                
            } catch (error) {
                console.error('显示导出数据失败:', error);
                alert('❌ 显示导出数据失败: ' + error.message);
            }
        }
        
        // 复制导出数据
        function copyExportData() {
            const textarea = document.getElementById('export-data-textarea');
            textarea.select();
            document.execCommand('copy');
            alert('✅ 数据已复制到剪贴板');
        }
        
        // 清理收藏数据
        function clearFavorites() {
            if (confirm('⚠️ 确定要清理localStorage中的收藏数据吗？此操作不可恢复！')) {
                localStorage.removeItem(STORAGE_KEY);
                alert('✅ 收藏数据已清理');
                checkFavorites();
            }
        }
        
        // 添加测试收藏数据
        function addTestFavorites() {
            const testFavorites = [
                {
                    id: 1001,
                    name: 'test_image_1.jpg',
                    type: 'file',
                    size: 1024000,
                    modified_at: new Date().toISOString(),
                    favorited_at: new Date().toISOString(),
                    folder_id: 1,
                    folder_name: '测试文件夹'
                },
                {
                    id: 1002,
                    name: 'test_design.psd',
                    type: 'file',
                    size: 5120000,
                    modified_at: new Date().toISOString(),
                    favorited_at: new Date().toISOString(),
                    folder_id: 2,
                    folder_name: '设计文件'
                }
            ];
            
            try {
                const existingData = localStorage.getItem(STORAGE_KEY);
                const existing = existingData ? JSON.parse(existingData) : [];
                
                // 合并数据，避免重复
                const combined = [...existing];
                testFavorites.forEach(test => {
                    if (!combined.find(item => item.id === test.id)) {
                        combined.push(test);
                    }
                });
                
                localStorage.setItem(STORAGE_KEY, JSON.stringify(combined));
                alert(`✅ 已添加 ${testFavorites.length} 条测试数据`);
                checkFavorites();
                
            } catch (error) {
                console.error('添加测试数据失败:', error);
                alert('❌ 添加测试数据失败: ' + error.message);
            }
        }
        
        // 清除所有数据
        function clearAllData() {
            if (confirm('⚠️ 确定要清除所有数据吗？此操作不可恢复！')) {
                localStorage.removeItem(STORAGE_KEY);
                alert('✅ 所有数据已清除');
                checkFavorites();
            }
        }
        
        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatDate(dateStr) {
            if (!dateStr) return '未知';
            try {
                return new Date(dateStr).toLocaleString('zh-CN');
            } catch {
                return '无效日期';
            }
        }
    </script>
</body>
</html>
