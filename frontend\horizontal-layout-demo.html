<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>横向布局演示 - 文件操作功能 - 文件共享系统</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>横向布局演示 - 文件操作功能</h1>
        
        <div class="view-controls">
            <div class="layout-toggle">
                <button class="layout-btn" data-layout="grid" title="网格布局">
                    <i class="fas fa-th"></i>
                </button>
                <button class="layout-btn active" data-layout="horizontal" title="横向布局">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
        
        <div class="file-grid horizontal-layout" id="demo-grid">
            <!-- 文件夹1 -->
            <div class="file-item folder-item" data-file-id="1" data-file-type="folder">
                <div class="file-icon">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="file-name" title="设计文件">设计文件</div>
                <div class="file-actions">
                    <button class="action-btn" data-action="open" title="打开">
                        <i class="fas fa-folder-open"></i>
                    </button>
                </div>
            </div>

            <!-- 文件夹2 -->
            <div class="file-item folder-item" data-file-id="2" data-file-type="folder">
                <div class="file-icon">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="file-name" title="产品图片">产品图片</div>
                <div class="file-actions">
                    <button class="action-btn" data-action="open" title="打开">
                        <i class="fas fa-folder-open"></i>
                    </button>
                </div>
            </div>

            <!-- 图片文件1 -->
            <div class="file-item" data-file-id="101" data-file-type="file">
                <div class="file-icon">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmOGZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzMzNzNkYyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzwvdGV4dD4KPC9zdmc+" alt="示例图片1" />
                </div>
                <div class="file-name" title="664ace4d9f28cb621a39679e3d665673.jpg">664ace4d9f28cb621a39679e3d665673.jpg</div>
                <div class="file-meta">
                    <span class="file-size">31.46 KB</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn" data-action="favorite" title="收藏">
                        <i class="far fa-star"></i>
                    </button>
                </div>
            </div>

            <!-- 图片文件2 -->
            <div class="file-item" data-file-id="102" data-file-type="file">
                <div class="file-icon">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzI8L3RleHQ+Cjwvc3ZnPg==" alt="示例图片2" />
                </div>
                <div class="file-name" title="design-mockup.psd">design-mockup.psd</div>
                <div class="file-meta">
                    <span class="file-size">2.3 MB</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn favorited" data-action="favorite" title="取消收藏">
                        <i class="fas fa-star"></i>
                    </button>
                </div>
            </div>

            <!-- 文件夹3 -->
            <div class="file-item folder-item" data-file-id="3" data-file-type="folder">
                <div class="file-icon">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="file-name" title="营销素材">营销素材</div>
                <div class="file-actions">
                    <button class="action-btn" data-action="open" title="打开">
                        <i class="fas fa-folder-open"></i>
                    </button>
                </div>
            </div>

            <!-- 图片文件3 -->
            <div class="file-item" data-file-id="103" data-file-type="file">
                <div class="file-icon">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZWZmNmZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5MzNjYyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzM8L3RleHQ+Cjwvc3ZnPg==" alt="示例图片3" />
                </div>
                <div class="file-name" title="product-banner.png">product-banner.png</div>
                <div class="file-meta">
                    <span class="file-size">856 KB</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn" data-action="favorite" title="收藏">
                        <i class="far fa-star"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="description">
            <h3>布局特点：</h3>
            <ul>
                <li>文件夹和文件卡片横向排列，每行一个</li>
                <li>图标在左侧，文件名称在右侧</li>
                <li>悬停效果：卡片上移，颜色变化，显示操作按钮</li>
                <li>响应式设计，适配不同屏幕尺寸</li>
                <li>保持文件夹的视觉识别性</li>
            </ul>

            <h3>操作功能：</h3>
            <ul>
                <li><strong>眼睛图标 <i class="fas fa-eye" style="color: #007bff;"></i></strong> - 点击预览图片，支持缩放、旋转等操作</li>
                <li><strong>星星图标 <i class="far fa-star" style="color: #ffc107;"></i></strong> - 点击添加到个人收藏夹</li>
                <li><strong>下载图标 <i class="fas fa-download" style="color: #28a745;"></i></strong> - 点击下载文件</li>
                <li><strong>文件夹图标 <i class="fas fa-folder-open" style="color: #6c757d;"></i></strong> - 点击打开文件夹</li>
                <li>悬停时显示操作按钮，点击执行相应功能</li>
                <li>收藏状态实时更新：空心星星 → 实心星星</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 简单的布局切换演示
        document.addEventListener('DOMContentLoaded', function() {
            const layoutBtns = document.querySelectorAll('.layout-btn');
            const grid = document.getElementById('demo-grid');

            layoutBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 更新按钮状态
                    layoutBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 切换布局
                    const layout = this.dataset.layout;
                    if (layout === 'horizontal') {
                        grid.className = 'file-grid horizontal-layout';
                    } else {
                        grid.className = 'file-grid';
                    }
                });
            });

            // 演示操作按钮功能
            document.addEventListener('click', function(e) {
                const actionBtn = e.target.closest('.action-btn');
                if (!actionBtn) return;

                e.preventDefault();
                e.stopPropagation();

                const action = actionBtn.dataset.action;
                const fileItem = actionBtn.closest('.file-item');
                const fileName = fileItem.querySelector('.file-name').textContent;
                const fileId = fileItem.dataset.fileId;

                switch (action) {
                    case 'preview':
                        showToast(`🔍 预览文件: ${fileName}`, 'info');
                        // 模拟预览功能
                        setTimeout(() => {
                            showToast('图片预览功能已启动，支持缩放、旋转等操作', 'success');
                        }, 500);
                        break;

                    case 'favorite':
                        const icon = actionBtn.querySelector('i');
                        const isFavorited = actionBtn.classList.contains('favorited');

                        if (isFavorited) {
                            // 取消收藏
                            icon.className = 'far fa-star';
                            actionBtn.classList.remove('favorited');
                            actionBtn.title = '收藏';
                            showToast(`⭐ 已取消收藏: ${fileName}`, 'warning');
                        } else {
                            // 添加收藏
                            icon.className = 'fas fa-star';
                            actionBtn.classList.add('favorited');
                            actionBtn.title = '取消收藏';
                            showToast(`⭐ 已收藏: ${fileName}`, 'success');
                        }
                        break;

                    case 'download':
                        showToast(`📥 开始下载: ${fileName}`, 'info');
                        // 模拟下载进度
                        setTimeout(() => {
                            showToast('文件下载完成', 'success');
                        }, 1000);
                        break;

                    case 'open':
                        showToast(`📁 打开文件夹: ${fileName}`, 'info');
                        break;
                }
            });
        });

        // 简单的提示消息功能
        function showToast(message, type = 'info') {
            // 移除现有的提示
            const existingToast = document.querySelector('.demo-toast');
            if (existingToast) {
                existingToast.remove();
            }

            const toast = document.createElement('div');
            toast.className = `demo-toast demo-toast-${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#d4edda' : type === 'warning' ? '#fff3cd' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
                color: ${type === 'success' ? '#155724' : type === 'warning' ? '#856404' : type === 'error' ? '#721c24' : '#0c5460'};
                border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'warning' ? '#ffeaa7' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
                border-radius: 8px;
                padding: 12px 16px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(toast);

            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => toast.remove(), 300);
                }
            }, 3000);
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
