#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试下载记录数据
"""

import pymysql
from datetime import datetime, timed<PERSON><PERSON>

def create_test_data():
    """创建测试数据"""
    try:
        print("🔄 连接数据库...")
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 获取用户fjj的ID
            cursor.execute("SELECT id FROM users WHERE username = 'fjj'")
            user = cursor.fetchone()
            
            if not user:
                print("❌ 找不到用户fjj")
                return False
            
            user_id = user[0]
            print(f"✅ 找到用户fjj，ID: {user_id}")
            
            # 清除现有的测试记录
            cursor.execute("DELETE FROM download_records WHERE user_id = %s", (user_id,))
            cursor.execute("DELETE FROM download_statistics")
            print("🗑️ 清除现有测试记录")
            
            # 获取一些文件用于测试
            cursor.execute("SELECT id, filename FROM shared_files LIMIT 5")
            files = cursor.fetchall()
            
            if not files:
                print("⚠️ 没有找到共享文件，创建一些测试文件记录")
                # 创建测试文件记录
                test_files = [
                    ("test_image1.jpg", "/test/path1"),
                    ("test_document.pdf", "/test/path2"),
                    ("test_image2.png", "/test/path3"),
                    ("test_archive.zip", "/test/path4"),
                    ("test_video.mp4", "/test/path5")
                ]
                
                # 先确保有文件夹
                cursor.execute("SELECT id FROM shared_folders LIMIT 1")
                folder = cursor.fetchone()
                if not folder:
                    cursor.execute("""
                        INSERT INTO shared_folders (name, path, description, created_at)
                        VALUES ('测试文件夹', '/test', '测试用文件夹', NOW())
                    """)
                    folder_id = cursor.lastrowid
                else:
                    folder_id = folder[0]
                
                # 创建测试文件
                file_ids = []
                for filename, path in test_files:
                    cursor.execute("""
                        INSERT INTO shared_files (filename, relative_path, file_size, folder_id, created_at)
                        VALUES (%s, %s, %s, %s, NOW())
                    """, (filename, path, 1024*1024, folder_id))
                    file_ids.append(cursor.lastrowid)
                
                files = [(file_id, filename) for file_id, (filename, _) in zip(file_ids, test_files)]
                print(f"✅ 创建了 {len(files)} 个测试文件")
            
            # 创建测试下载记录
            print("📝 创建测试下载记录...")
            for i, (file_id, filename) in enumerate(files):
                download_time = datetime.now() - timedelta(days=i, hours=i*2)
                is_encrypted = i % 2 == 0
                
                # 插入下载记录
                cursor.execute("""
                    INSERT INTO download_records 
                    (file_id, user_id, download_type, zip_filename, zip_path, file_size, 
                     is_encrypted, password, download_status, created_at, downloaded_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    file_id, user_id, 'single',
                    f"{filename}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                    f"/tmp/downloads/{filename}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                    1024 * 1024 * (i + 1),  # 不同大小
                    is_encrypted,
                    f"pass{i+1}" if is_encrypted else None,
                    'completed',
                    download_time,
                    download_time
                ))
                
                # 插入下载统计
                cursor.execute("""
                    INSERT INTO download_statistics 
                    (file_id, total_downloads, encrypted_downloads, first_download, last_download)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE 
                    total_downloads = total_downloads + VALUES(total_downloads),
                    encrypted_downloads = encrypted_downloads + VALUES(encrypted_downloads),
                    last_download = VALUES(last_download)
                """, (
                    file_id, 
                    i + 1,  # 下载次数
                    1 if is_encrypted else 0,
                    download_time,
                    download_time
                ))
            
            connection.commit()
            print(f"✅ 创建了 {len(files)} 条测试下载记录")
            
            # 验证创建的记录
            cursor.execute("""
                SELECT dr.id, dr.file_id, sf.filename, dr.zip_filename, dr.is_encrypted, 
                       dr.file_size, dr.downloaded_at, ds.total_downloads
                FROM download_records dr
                LEFT JOIN shared_files sf ON dr.file_id = sf.id
                LEFT JOIN download_statistics ds ON dr.file_id = ds.file_id
                WHERE dr.user_id = %s 
                ORDER BY dr.downloaded_at DESC
            """, (user_id,))
            
            records = cursor.fetchall()
            print(f"\n📋 验证创建的记录 ({len(records)} 条):")
            print("ID | 文件ID | 原文件名 | 压缩文件名 | 加密 | 大小(MB) | 下载时间 | 下载次数")
            print("-" * 100)
            
            for record in records:
                file_size_mb = record[5] / (1024 * 1024) if record[5] else 0
                print(f"{record[0]:3d} | {record[1]:6d} | {record[2][:12]:12s} | {record[3][:20]:20s} | {record[4]:5s} | {file_size_mb:7.1f} | {record[6]} | {record[7]:8d}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 创建测试下载记录数据...")
    success = create_test_data()
    if success:
        print("\n✅ 测试数据创建完成")
    else:
        print("\n❌ 测试数据创建失败")
