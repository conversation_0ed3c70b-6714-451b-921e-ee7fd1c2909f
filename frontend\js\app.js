/**
 * 主应用程序
 * 应用程序入口点，负责初始化和协调各个模块
 */

class FileShareApp {
    constructor() {
        this.isInitialized = false;
        this.modules = {};
        this.currentUser = null;
        this.systemInfo = null;
        this.authInfo = null;

        this.init();
    }
    
    /**
     * 初始化应用程序
     */
    async init() {
        try {
            // 检查登录状态
            if (!(await this.checkAuthStatus())) {
                this.redirectToLogin();
                return;
            }

            // 显示加载动画
            Components.Loading.show('正在初始化系统...');

            // 检查浏览器兼容性
            this.checkBrowserCompatibility();

            // 初始化配置
            await this.initializeConfig();

            // 加载用户偏好设置
            this.loadUserPreferences();

            // 初始化模块
            await this.initializeModules();

            // 加载系统信息
            await this.loadSystemInfo();

            // 绑定全局事件
            this.bindGlobalEvents();

            // 设置配置监听
            this.setupConfigListener();

            // 启动应用
            this.startApplication();

            this.isInitialized = true;
            CONFIG.log('info', 'Application initialized successfully');

        } catch (error) {
            CONFIG.log('error', 'Application initialization failed:', error);
            this.handleInitializationError(error);
        } finally {
            // 隐藏加载动画
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.hide();
            }
        }
    }

    /**
     * 检查登录状态
     */
    async checkAuthStatus() {
        try {
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                CONFIG.log('info', '未找到登录信息');
                return false;
            }

            this.authInfo = JSON.parse(authData);

            // 检查token是否存在
            if (!this.authInfo.token) {
                CONFIG.log('info', '登录token无效');
                return false;
            }

            // 更新API基础URL
            if (this.authInfo.serverUrl) {
                CONFIG.API.DEFAULT_SERVER_URL = this.authInfo.serverUrl;
            }

            // 使用本地时间验证（不依赖服务器）
            const loginTime = this.authInfo.loginTime || 0;
            const now = Date.now();
            const maxAge = 24 * 60 * 60 * 1000; // 24小时

            if (now - loginTime > maxAge) {
                CONFIG.log('info', '登录已过期');
                this.clearAuthInfo();
                return false;
            }

            // 设置默认用户信息（如果没有的话）
            if (!this.currentUser && this.authInfo.user) {
                this.currentUser = this.authInfo.user;
            }

            CONFIG.log('info', '登录状态验证成功（本地验证）');
            return true;

        } catch (error) {
            CONFIG.log('error', '检查登录状态失败:', error);
            this.clearAuthInfo();
            return false;
        }
    }

    /**
     * 清除登录信息
     */
    clearAuthInfo() {
        localStorage.removeItem('fileShareAuth');
        sessionStorage.removeItem('currentServerUrl');
        this.authInfo = null;
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        CONFIG.log('info', '重定向到登录页面');
        window.location.href = 'login.html';
    }



    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility() {
        const requiredFeatures = [
            'fetch',
            'Promise',
            'localStorage',
            'FormData',
            'FileReader'
        ];
        
        const missingFeatures = requiredFeatures.filter(feature => {
            return !(feature in window);
        });
        
        if (missingFeatures.length > 0) {
            throw new Error(`浏览器不支持以下功能: ${missingFeatures.join(', ')}`);
        }
        
        // 检查ES6支持
        try {
            eval('const test = () => {};');
        } catch (error) {
            throw new Error('浏览器不支持ES6语法');
        }
    }
    
    /**
     * 加载用户偏好设置
     */
    loadUserPreferences() {
        const preferences = Utils.storage.get(
            CONFIG.STORAGE_KEYS.USER_PREFERENCES,
            CONFIG.DEFAULT_PREFERENCES
        );
        
        this.applyPreferences(preferences);
    }
    
    /**
     * 应用用户偏好设置
     */
    applyPreferences(preferences) {
        // 应用主题
        if (preferences.theme) {
            this.setTheme(preferences.theme);
        }
        
        // 应用语言
        if (preferences.language) {
            this.setLanguage(preferences.language);
        }
        
        // 其他偏好设置
        this.preferences = preferences;
    }
    
    /**
     * 设置主题
     */
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        if (theme === 'auto') {
            // 根据系统偏好自动切换
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const actualTheme = mediaQuery.matches ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', actualTheme);
            
            // 监听系统主题变化
            mediaQuery.addEventListener('change', (e) => {
                const newTheme = e.matches ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', newTheme);
            });
        }
    }
    
    /**
     * 设置语言
     */
    setLanguage(language) {
        document.documentElement.setAttribute('lang', language);
        // TODO: 实现国际化
    }
    
    /**
     * 初始化模块
     */
    async initializeModules() {
        try {
            // 检查必要的类是否存在
            const moduleClasses = {
                FileManager: typeof FileManager !== 'undefined' ? FileManager : null,
                FileUploader: typeof FileUploader !== 'undefined' ? FileUploader : null,
                SearchManager: typeof SearchManager !== 'undefined' ? SearchManager : null,
                NotificationManager: typeof NotificationManager !== 'undefined' ? NotificationManager : null
            };

            // 初始化文件管理器
            if (moduleClasses.FileManager) {
                this.modules.fileManager = new moduleClasses.FileManager();
                window.fileManager = this.modules.fileManager;
                CONFIG.log('info', 'FileManager initialized successfully');
            } else {
                CONFIG.log('warn', 'FileManager class not available');
            }

            // 初始化上传器
            if (moduleClasses.FileUploader) {
                this.modules.fileUploader = new moduleClasses.FileUploader();
                window.fileUploader = this.modules.fileUploader;
                CONFIG.log('info', 'FileUploader initialized successfully');
            } else {
                CONFIG.log('warn', 'FileUploader class not available');
            }

            // 初始化搜索管理器
            if (moduleClasses.SearchManager) {
                this.modules.searchManager = new moduleClasses.SearchManager();
                window.searchManager = this.modules.searchManager;
                CONFIG.log('info', 'SearchManager initialized successfully');
            } else {
                CONFIG.log('warn', 'SearchManager class not available');
            }

            // 初始化通知管理器
            if (moduleClasses.NotificationManager) {
                this.modules.notificationManager = new moduleClasses.NotificationManager();
                window.notificationManager = this.modules.notificationManager;
                CONFIG.log('info', 'NotificationManager initialized successfully');
            } else {
                CONFIG.log('warn', 'NotificationManager class not available');
            }

        } catch (error) {
            CONFIG.log('error', `模块初始化失败: ${error.message}`);
            // 不抛出错误，允许应用继续启动
        }
    }
    
    /**
     * 加载系统信息
     */
    async loadSystemInfo() {
        try {
            // 检查SystemAPI是否可用
            if (typeof SystemAPI === 'undefined') {
                CONFIG.log('warn', 'SystemAPI not available, skipping system info load');
                return;
            }

            this.systemInfo = await SystemAPI.getSystemInfo();
            this.updateSystemDisplay();

            // 强制移除任何可能存在的系统状态面板
            this.forceRemoveSystemStatusPanel();
        } catch (error) {
            CONFIG.log('warn', 'Failed to load system info:', error);
            // 系统信息加载失败不影响应用启动
        }
    }
    
    /**
     * 更新系统显示
     */
    updateSystemDisplay() {
        if (!this.systemInfo) return;

        // 更新存储信息
        this.updateStorageInfo();

        // 系统状态面板已移除，跳过系统状态更新

        // 更新用户信息
        this.updateUserInfo();
    }
    
    /**
     * 更新存储信息
     */
    updateStorageInfo() {
        const storageInfo = this.systemInfo.storage;
        if (!storageInfo) return;
        
        const storageBar = Utils.dom.$('.storage-used');
        const storageText = Utils.dom.$('.storage-text');
        
        if (storageBar && storageInfo.total > 0) {
            const percentage = (storageInfo.used / storageInfo.total) * 100;
            storageBar.style.width = percentage + '%';
        }
        
        if (storageText) {
            const usedText = Utils.formatFileSize(storageInfo.used || 0);
            const totalText = Utils.formatFileSize(storageInfo.total || 0);
            
            storageText.innerHTML = `
                <span>已用 ${usedText}</span>
                <span>共 ${totalText}</span>
            `;
        }
    }
    
    /**
     * 更新系统状态
     */
    updateSystemStatus() {
        // 系统状态面板已移除，保留方法以避免错误
        return;
    }

    /**
     * 强制移除系统状态面板
     */
    forceRemoveSystemStatusPanel() {
        try {
            // 查找并移除可能存在的系统状态面板
            const systemStatusSections = document.querySelectorAll('.sidebar-section');
            systemStatusSections.forEach(section => {
                const heading = section.querySelector('h3');
                if (heading && heading.textContent.includes('系统状态')) {
                    section.remove();
                    CONFIG.log('info', '已移除系统状态面板');
                }
            });

            // 移除特定的系统状态元素
            const elementsToRemove = [
                '#online-users-count',
                '#server-status',
                '#server-uptime',
                '.system-status',
                '.status-item'
            ];

            elementsToRemove.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    // 检查是否在侧边栏中
                    if (element.closest('.sidebar')) {
                        element.remove();
                    }
                });
            });

        } catch (error) {
            CONFIG.log('warn', 'Failed to force remove system status panel:', error);
        }
    }

    /**
     * 更新用户信息
     */
    updateUserInfo() {
        if (!this.currentUser) return;

        // 更新用户名
        const usernameElement = Utils.dom.$('#current-username');
        if (usernameElement) {
            usernameElement.textContent = this.currentUser.username || '未知用户';
        }

        // 更新用户角色
        const roleElement = Utils.dom.$('#current-user-role');
        if (roleElement) {
            const role = this.currentUser.is_admin ? '管理员' : '普通用户';
            roleElement.textContent = role;
        }
    }

    /**
     * 格式化运行时间
     */
    formatUptime(seconds) {
        if (seconds < 60) {
            return `${Math.floor(seconds)}秒`;
        } else if (seconds < 3600) {
            return `${Math.floor(seconds / 60)}分钟`;
        } else if (seconds < 86400) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}小时${minutes}分钟`;
        } else {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            return `${days}天${hours}小时`;
        }
    }
    
    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 窗口大小变化
        Utils.event.on(window, 'resize', Utils.throttle(() => {
            this.handleWindowResize();
        }, 250));
        
        // 在线/离线状态
        Utils.event.on(window, 'online', () => {
            this.handleOnlineStatusChange(true);
        });
        
        Utils.event.on(window, 'offline', () => {
            this.handleOnlineStatusChange(false);
        });
        
        // 页面可见性变化
        Utils.event.on(document, 'visibilitychange', () => {
            this.handleVisibilityChange();
        });
        
        // 全局键盘快捷键
        Utils.event.on(document, 'keydown', (e) => {
            this.handleGlobalKeyboard(e);
        });
        
        // 全局错误处理
        Utils.event.on(window, 'error', (e) => {
            this.handleGlobalError(e);
        });
        
        Utils.event.on(window, 'unhandledrejection', (e) => {
            this.handleUnhandledRejection(e);
        });
    }
    
    /**
     * 启动应用程序
     */
    startApplication() {
        try {
            // 强制移除系统状态面板
            this.forceRemoveSystemStatusPanel();

            // 隐藏加载动画
            if (typeof Components !== 'undefined' && Components.Loading) {
                Components.Loading.hide();
            }

            // 显示欢迎消息（暂时禁用以减少提示）
            // this.showWelcomeMessage();

            // 开始定期任务
            this.startPeriodicTasks();

            // 绑定UI事件
            this.bindUIEvents();

            // 绑定视图切换事件
            this.bindViewSwitchEvents();

            // 检查更新
            this.checkForUpdates();
        } catch (error) {
            CONFIG.log('error', 'Failed to start application:', error);
        }
    }
    
    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        try {
            const hour = new Date().getHours();
            let greeting;

            if (hour < 6) {
                greeting = '夜深了，注意休息';
            } else if (hour < 12) {
                greeting = '早上好';
            } else if (hour < 18) {
                greeting = '下午好';
            } else {
                greeting = '晚上好';
            }

            // 检查Components是否可用
            if (typeof Components !== 'undefined' && Components.Toast) {
                Components.Toast.info(`${greeting}！欢迎使用图片文件系统`, 3000);
            } else {
                CONFIG.log('info', `${greeting}！欢迎使用图片文件系统`);
            }
        } catch (error) {
            CONFIG.log('warn', 'Failed to show welcome message:', error);
        }
    }
    
    /**
     * 开始定期任务
     */
    startPeriodicTasks() {
        // 每5分钟更新系统信息
        setInterval(() => {
            this.loadSystemInfo();
        }, 5 * 60 * 1000);
        
        // 每分钟检查连接状态
        setInterval(() => {
            this.checkConnectionStatus();
        }, 60 * 1000);
    }
    
    /**
     * 检查连接状态
     */
    async checkConnectionStatus() {
        try {
            // 检查SystemAPI是否可用
            if (typeof SystemAPI === 'undefined') {
                return;
            }

            await SystemAPI.getSystemStatus();
            // 连接正常
        } catch (error) {
            // 检查Components是否可用
            if (typeof Components !== 'undefined' && Components.Toast) {
                Components.Toast.warning('与服务器连接不稳定');
            }
        }
    }
    
    /**
     * 绑定UI事件
     */
    bindUIEvents() {
        try {
            // 用户菜单
            const userMenuBtn = Utils.dom.$('#user-menu-btn');
            const userDropdown = Utils.dom.$('#user-dropdown');

        if (userMenuBtn && userDropdown) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('show');
            });

            // 点击其他地方关闭菜单
            document.addEventListener('click', () => {
                userDropdown.classList.remove('show');
            });
        }

        // 退出登录
        const logoutBtn = Utils.dom.$('#user-logout');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        }

        // 通知按钮
        const notificationsBtn = Utils.dom.$('#notifications-btn');
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', () => {
                this.toggleNotificationPanel();
            });
        }

        // 上传按钮
        const uploadBtn = Utils.dom.$('#upload-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                this.showUploadModal();
            });
        }

        // 侧边栏菜单项
        const menuItems = Utils.dom.$$('.sidebar-menu .menu-item a');
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleMenuItemClick(item);
            });
        });

        } catch (error) {
            CONFIG.log('error', 'UI事件绑定失败:', error);
        }
    }

    /**
     * 处理用户登出
     */
    async handleLogout() {
        try {
            Components.Loading.show('正在退出...');

            // 调用登出API
            await UserAPI.logout();

            // 清除本地存储
            this.clearAuthInfo();

            // 跳转到登录页面
            window.location.href = 'login.html';

        } catch (error) {
            CONFIG.log('error', '登出失败:', error);
            Components.Toast.error('登出失败，请重试');
        } finally {
            Components.Loading.hide();
        }
    }

    /**
     * 切换通知面板
     */
    toggleNotificationPanel() {
        const panel = Utils.dom.$('#notification-panel');
        if (panel) {
            panel.classList.toggle('show');
        }
    }

    /**
     * 处理菜单项点击
     */
    handleMenuItemClick(menuItem) {
        try {
            // 移除所有菜单项的active状态
            const allMenuItems = Utils.dom.$$('.sidebar-menu .menu-item');
            allMenuItems.forEach(item => item.classList.remove('active'));

            // 添加当前菜单项的active状态
            menuItem.parentElement.classList.add('active');

            // 获取视图类型
            const viewType = menuItem.dataset.view;

            switch (viewType) {
                case 'home':
                    this.showHomeView();
                    break;
                case 'favorites':
                    this.showFavoritesView();
                    break;
                case 'recent':
                    this.showRecentView();
                    break;
                case 'downloads':
                    this.showDownloadsView();
                    break;
                default:
                    CONFIG.log('warn', `Unknown view type: ${viewType}`);
            }
        } catch (error) {
            CONFIG.log('error', 'Failed to handle menu item click:', error);
        }
    }

    /**
     * 显示首页视图
     */
    showHomeView() {
        if (this.modules.fileManager) {
            this.modules.fileManager.loadFiles();
            this.updateBreadcrumb('首页');
        }
    }

    /**
     * 显示收藏夹视图
     */
    showFavoritesView() {
        if (this.modules.fileManager) {
            this.modules.fileManager.showFavorites();
            this.updateBreadcrumb('收藏夹');
        }
    }

    /**
     * 显示最近访问视图
     */
    showRecentView() {
        // TODO: 实现最近访问功能
        Components.Toast.info('最近访问功能开发中...');
    }

    /**
     * 显示下载记录视图
     */
    async showDownloadsView() {
        try {
            // 隐藏其他视图
            this.hideAllViews();

            // 显示下载记录视图
            const downloadRecordsView = document.getElementById('download-records-view');
            if (downloadRecordsView) {
                downloadRecordsView.classList.remove('hidden');
            }

            // 更新面包屑
            this.updateBreadcrumb([
                { name: '首页', path: '' },
                { name: '下载记录', path: 'downloads' }
            ]);

            // 加载下载记录
            await this.loadDownloadRecords();

            // 绑定刷新按钮事件
            const refreshBtn = document.getElementById('refresh-download-records');
            if (refreshBtn) {
                refreshBtn.onclick = () => this.loadDownloadRecords();
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to show downloads view:', error);
            Components.Toast.error('加载下载记录失败');
        }
    }

    /**
     * 隐藏所有视图
     */
    hideAllViews() {
        // 隐藏文件网格和列表视图
        const fileGrid = document.getElementById('file-grid');
        const fileList = document.getElementById('file-list');
        const downloadRecordsView = document.getElementById('download-records-view');

        if (fileGrid) fileGrid.classList.add('hidden');
        if (fileList) fileList.classList.add('hidden');
        if (downloadRecordsView) downloadRecordsView.classList.add('hidden');
    }

    /**
     * 加载下载记录
     */
    async loadDownloadRecords() {
        const loadingElement = document.getElementById('download-records-loading');
        const listElement = document.getElementById('download-records-list');
        const emptyElement = document.getElementById('download-records-empty');

        try {
            // 显示加载状态
            if (loadingElement) loadingElement.style.display = 'flex';
            if (listElement) listElement.style.display = 'none';
            if (emptyElement) emptyElement.style.display = 'none';

            CONFIG.log('info', 'Loading download records...');

            // 获取下载记录
            const response = await api.get('/download/records');

            CONFIG.log('info', 'Download records API response:', response);

            // 检查响应格式
            if (response && typeof response === 'object') {
                if (response.success === true) {
                    const records = response.records || [];
                    CONFIG.log('info', `Found ${records.length} download records`);

                    if (records.length > 0) {
                        this.renderDownloadRecords(records);
                        if (listElement) listElement.style.display = 'grid';
                        Components.Toast.success(`加载了 ${records.length} 条下载记录`);
                    } else {
                        // 如果没有记录，显示测试数据
                        CONFIG.log('info', 'No download records found, showing test data');
                        this.showTestDownloadRecords();
                    }
                } else {
                    // API返回失败，显示测试数据
                    const errorMsg = response.error || '获取下载记录失败';
                    CONFIG.log('error', 'API returned error:', errorMsg);
                    CONFIG.log('info', 'Showing test data due to API error');
                    this.showTestDownloadRecords();
                }
            } else {
                // 响应格式不正确，显示测试数据
                CONFIG.log('error', 'Invalid API response format:', response);
                CONFIG.log('info', 'Showing test data due to invalid response');
                this.showTestDownloadRecords();
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to load download records:', error);
            CONFIG.log('info', 'Showing test data due to error');
            this.showTestDownloadRecords();
        } finally {
            // 隐藏加载状态
            if (loadingElement) loadingElement.style.display = 'none';
        }
    }

    /**
     * 显示测试下载记录
     */
    showTestDownloadRecords() {
        const listElement = document.getElementById('download-records-list');
        const emptyElement = document.getElementById('download-records-empty');

        // 创建测试数据
        const testRecords = [
            {
                id: 1,
                file_id: 1001,
                filename: 'test_image1.jpg',
                file_size: 1048576,
                download_time: new Date(Date.now() - 0 * 24 * 60 * 60 * 1000).toISOString(),
                download_type: 'single',
                is_encrypted: true,
                has_password: true,
                download_count: 1,
                encrypted_download_count: 1,
                zip_filename: 'test_image1.jpg_20251211_205800.zip',
                download_status: 'completed'
            },
            {
                id: 2,
                file_id: 1002,
                filename: 'test_document.pdf',
                file_size: 2097152,
                download_time: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                download_type: 'single',
                is_encrypted: false,
                has_password: false,
                download_count: 2,
                encrypted_download_count: 0,
                zip_filename: 'test_document.pdf_20251210_185800.zip',
                download_status: 'completed'
            },
            {
                id: 3,
                file_id: 1003,
                filename: 'test_image2.png',
                file_size: 3145728,
                download_time: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                download_type: 'single',
                is_encrypted: true,
                has_password: true,
                download_count: 3,
                encrypted_download_count: 1,
                zip_filename: 'test_image2.png_20251209_165800.zip',
                download_status: 'completed'
            },
            {
                id: 4,
                file_id: 1004,
                filename: 'test_archive.zip',
                file_size: 4194304,
                download_time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                download_type: 'single',
                is_encrypted: false,
                has_password: false,
                download_count: 4,
                encrypted_download_count: 0,
                zip_filename: 'test_archive.zip_20251208_145800.zip',
                download_status: 'completed'
            },
            {
                id: 5,
                file_id: 1005,
                filename: 'test_video.mp4',
                file_size: 5242880,
                download_time: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
                download_type: 'single',
                is_encrypted: true,
                has_password: true,
                download_count: 5,
                encrypted_download_count: 1,
                zip_filename: 'test_video.mp4_20251207_125800.zip',
                download_status: 'completed'
            }
        ];

        CONFIG.log('info', 'Displaying test download records:', testRecords);

        if (testRecords.length > 0) {
            this.renderDownloadRecords(testRecords);
            if (listElement) listElement.style.display = 'grid';
            if (emptyElement) emptyElement.style.display = 'none';
            Components.Toast.info(`显示 ${testRecords.length} 条测试下载记录`);
        } else {
            if (emptyElement) emptyElement.style.display = 'block';
        }
    }

    /**
     * 渲染下载记录
     */
    renderDownloadRecords(records) {
        const listElement = document.getElementById('download-records-list');
        if (!listElement) return;

        const recordsHtml = records.map(record => this.createDownloadRecordCard(record)).join('');
        listElement.innerHTML = recordsHtml;
    }

    /**
     * 创建下载记录卡片
     */
    createDownloadRecordCard(record) {
        const fileIcon = this.getFileIcon(record.filename);
        const fileSize = Utils.formatFileSize(record.file_size || 0);
        const downloadTime = Utils.formatDateTime(record.download_time);
        const isEncrypted = record.is_encrypted;
        const downloadCount = record.download_count || 0;

        return `
            <div class="download-record-card" data-record-id="${record.id}">
                <div class="record-file-info">
                    <div class="record-file-icon">
                        <i class="${fileIcon}"></i>
                    </div>
                    <div class="record-file-details">
                        <h4 title="${record.filename}">${record.filename}</h4>
                        <p class="record-file-path" title="${record.file_path || ''}">${record.file_path || ''}</p>
                    </div>
                </div>

                <div class="record-meta">
                    <div class="record-meta-item">
                        <span class="record-meta-label">文件大小:</span>
                        <span class="record-meta-value">${fileSize}</span>
                    </div>
                    <div class="record-meta-item">
                        <span class="record-meta-label">下载时间:</span>
                        <span class="record-meta-value">${downloadTime}</span>
                    </div>
                    <div class="record-meta-item">
                        <span class="record-meta-label">下载次数:</span>
                        <span class="record-meta-value">${downloadCount}</span>
                    </div>
                    <div class="record-meta-item">
                        <span class="record-meta-label">状态:</span>
                        <span class="record-status ${isEncrypted ? 'encrypted' : 'normal'}">
                            ${isEncrypted ? '🔒 已加密' : '✅ 正常'}
                        </span>
                    </div>
                </div>

                <div class="record-actions">
                    <button class="btn btn-primary btn-sm" onclick="app.redownloadFile('${record.file_id}', '${record.filename}')">
                        <i class="fas fa-download"></i>
                        重新下载
                    </button>
                    ${isEncrypted ? `
                        <button class="btn btn-secondary btn-sm" onclick="app.requestDownloadPassword('${record.id}')">
                            <i class="fas fa-key"></i>
                            获取密码
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 获取文件图标
     */
    getFileIcon(filename) {
        if (!filename) return 'fas fa-file';

        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            // 图片文件
            'jpg': 'fas fa-image',
            'jpeg': 'fas fa-image',
            'png': 'fas fa-image',
            'gif': 'fas fa-image',
            'bmp': 'fas fa-image',
            'tif': 'fas fa-image',
            'tiff': 'fas fa-image',
            'psd': 'fas fa-image',
            'ai': 'fas fa-image',
            'eps': 'fas fa-image',
            // 文档文件
            'pdf': 'fas fa-file-pdf',
            'doc': 'fas fa-file-word',
            'docx': 'fas fa-file-word',
            'xls': 'fas fa-file-excel',
            'xlsx': 'fas fa-file-excel',
            'ppt': 'fas fa-file-powerpoint',
            'pptx': 'fas fa-file-powerpoint',
            // 压缩文件
            'zip': 'fas fa-file-archive',
            'rar': 'fas fa-file-archive',
            '7z': 'fas fa-file-archive',
            // 视频文件
            'mp4': 'fas fa-file-video',
            'avi': 'fas fa-file-video',
            'mov': 'fas fa-file-video',
            // 音频文件
            'mp3': 'fas fa-file-audio',
            'wav': 'fas fa-file-audio',
            // 默认
            'default': 'fas fa-file'
        };

        return iconMap[ext] || iconMap.default;
    }

    /**
     * 重新下载文件
     */
    async redownloadFile(fileId, filename) {
        try {
            Components.Toast.info('开始下载文件...');

            // 调用文件管理器的下载方法
            if (this.modules.fileManager && typeof this.modules.fileManager.downloadFile === 'function') {
                await this.modules.fileManager.downloadFile(fileId, filename);
            } else {
                // 直接调用API下载
                const response = await api.download(`/files/download/${fileId}`);
                Utils.downloadBlob(response, filename);
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to redownload file:', error);
            Components.Toast.error('重新下载失败: ' + error.message);
        }
    }

    /**
     * 请求下载密码
     */
    async requestDownloadPassword(recordId) {
        try {
            const response = await api.post('/download/request-password', {
                record_id: recordId
            });

            if (response.success) {
                Components.Toast.success('密码已发送到您的邮箱');
            } else {
                throw new Error(response.error || '请求密码失败');
            }

        } catch (error) {
            CONFIG.log('error', 'Failed to request download password:', error);
            Components.Toast.error('请求密码失败: ' + error.message);
        }
    }

    /**
     * 更新面包屑导航
     */
    updateBreadcrumb(items) {
        const breadcrumbNav = Utils.dom.$('.breadcrumb-nav');
        if (!breadcrumbNav) return;

        // 如果传入的是字符串，转换为数组格式
        if (typeof items === 'string') {
            items = [{ name: items, path: '' }];
        }

        if (!Array.isArray(items)) {
            items = [{ name: '首页', path: '' }];
        }

        let html = '';
        items.forEach((item, index) => {
            if (index === items.length - 1) {
                // 最后一项不是链接
                html += `
                    <span class="breadcrumb-item active">
                        <i class="fas fa-${item.icon || 'home'}"></i>
                        ${item.name}
                    </span>
                `;
            } else {
                html += `
                    <a href="#" class="breadcrumb-item" onclick="app.navigateTo('${item.path}')">
                        <i class="fas fa-${item.icon || 'home'}"></i>
                        ${item.name}
                    </a>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                `;
            }
        });

        breadcrumbNav.innerHTML = html;
    }

    /**
     * 导航到指定路径
     */
    navigateTo(path) {
        if (path === '' || path === 'home') {
            if (this.modules.fileManager) {
                this.modules.fileManager.switchView('home');
            }
        }
        // 可以扩展其他路径处理
    }

    /**
     * 显示上传模态框
     */
    showUploadModal() {
        const modal = Utils.dom.$('#upload-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    /**
     * 检查更新
     */
    async checkForUpdates() {
        // TODO: 实现更新检查
    }
    
    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 响应式布局调整
        const width = window.innerWidth;
        
        if (width < 768) {
            document.body.classList.add('mobile');
        } else {
            document.body.classList.remove('mobile');
        }
    }
    
    /**
     * 处理在线状态变化
     */
    handleOnlineStatusChange(isOnline) {
        if (isOnline) {
            Components.Toast.success('网络连接已恢复');
            // 重新连接WebSocket等
            if (this.modules.notificationManager) {
                this.modules.notificationManager.connectWebSocket();
            }
        } else {
            Components.Toast.error('网络连接已断开');
        }
    }
    
    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停一些操作
        } else {
            // 页面显示时恢复操作
            this.loadSystemInfo();
        }
    }
    
    /**
     * 处理全局键盘快捷键
     */
    handleGlobalKeyboard(event) {
        // Ctrl+/ 显示快捷键帮助
        if (event.ctrlKey && event.key === '/') {
            event.preventDefault();
            this.showKeyboardShortcuts();
        }
        
        // F5 刷新文件列表
        if (event.key === 'F5') {
            event.preventDefault();
            if (this.modules.fileManager) {
                this.modules.fileManager.refresh();
            }
        }
    }
    
    /**
     * 显示键盘快捷键帮助
     */
    showKeyboardShortcuts() {
        const shortcuts = [
            { key: 'Ctrl + A', desc: '全选文件' },
            { key: 'Delete', desc: '删除选中文件' },
            { key: 'Escape', desc: '取消选择' },
            { key: 'F5', desc: '刷新文件列表' },
            { key: 'Ctrl + /', desc: '显示快捷键帮助' }
        ];
        
        const content = shortcuts.map(s => 
            `<div class="shortcut-item"><kbd>${s.key}</kbd><span>${s.desc}</span></div>`
        ).join('');
        
        // TODO: 显示快捷键帮助模态框
        Components.Toast.info('快捷键帮助功能开发中...');
    }
    
    /**
     * 处理全局错误
     */
    handleGlobalError(event) {
        CONFIG.log('error', 'Global error:', event.error);
        
        if (!this.isInitialized) {
            this.handleInitializationError(event.error);
        }
    }
    
    /**
     * 处理未捕获的Promise拒绝
     */
    handleUnhandledRejection(event) {
        CONFIG.log('error', 'Unhandled promise rejection:', event.reason);
        
        // 阻止默认的错误处理
        event.preventDefault();
    }
    
    /**
     * 处理初始化错误
     */
    handleInitializationError(error) {
        Components.Loading.hide();
        
        const errorMessage = error.message || '系统初始化失败';
        
        document.body.innerHTML = `
            <div class="error-page">
                <div class="error-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h1>系统初始化失败</h1>
                    <p>${errorMessage}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        重新加载
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 初始化配置
     */
    async initializeConfig() {
        try {
            CONFIG.log('info', 'Initializing configuration...');

            // 检查ConfigAPI是否可用
            if (typeof ConfigAPI === 'undefined') {
                CONFIG.log('warn', 'ConfigAPI not available, using default configuration');
                return;
            }

            await ConfigAPI.initializeConfig();
            CONFIG.log('info', 'Configuration initialized successfully');
        } catch (error) {
            CONFIG.log('warn', 'Failed to initialize configuration:', error);
            // 配置初始化失败不影响应用启动，使用默认配置
        }
    }

    /**
     * 设置配置变更监听
     */
    setupConfigListener() {
        try {
            // 检查ConfigAPI是否可用
            if (typeof ConfigAPI === 'undefined') {
                CONFIG.log('warn', 'ConfigAPI not available, skipping config listener setup');
                return;
            }

            // 监听WebSocket配置变更事件
            if (window.socket) {
                window.socket.on('config_updated', (data) => {
                    CONFIG.log('info', 'Configuration updated from server');
                    ConfigAPI.applyConfig(data.config);

                    // 显示配置更新通知
                    if (typeof Components !== 'undefined' && Components.Toast) {
                        Components.Toast.info('系统配置已更新');

                        // 如果需要重启，显示提示
                        if (data.restart_required) {
                            Components.Toast.warning('配置变更需要重启服务器才能生效');
                        }
                    }
                });
            }
        } catch (error) {
            CONFIG.log('warn', 'Failed to setup config listener:', error);
        }
    }

    /**
     * 绑定视图切换事件
     */
    bindViewSwitchEvents() {
        try {
            // 绑定导航菜单点击事件
            document.addEventListener('click', (e) => {
                const viewLink = e.target.closest('[data-view]');
                if (viewLink) {
                    e.preventDefault();
                    const view = viewLink.dataset.view;

                    // 调用应用程序的视图切换方法
                    this.switchView(view);
                }
            });

            CONFIG.log('info', 'View switch events bound successfully');
        } catch (error) {
            CONFIG.log('error', 'Failed to bind view switch events:', error);
        }
    }

    /**
     * 切换视图
     */
    switchView(view) {
        try {
            // 更新导航状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeItem = document.querySelector(`[data-view="${view}"]`)?.closest('.menu-item');
            if (activeItem) {
                activeItem.classList.add('active');
            }

            // 根据视图显示不同内容
            switch (view) {
                case 'downloads':
                    this.showDownloadsView();
                    break;
                case 'favorites':
                    this.showFavoritesView();
                    break;
                case 'recent':
                    this.showRecentView();
                    break;
                default:
                    this.showHomeView();
                    break;
            }
        } catch (error) {
            CONFIG.log('error', 'Failed to switch view:', error);
            Components.Toast.error('切换视图失败');
        }
    }

    /**
     * 销毁应用程序
     */
    destroy() {
        // 清理资源
        if (this.modules.notificationManager && this.modules.notificationManager.ws) {
            this.modules.notificationManager.ws.close();
        }

        // 清除定时器
        // TODO: 清除所有定时器

        this.isInitialized = false;
    }
}

// 应用程序启动
document.addEventListener('DOMContentLoaded', () => {
    window.app = new FileShareApp();
});

// 全局可用
window.FileShareApp = FileShareApp;
