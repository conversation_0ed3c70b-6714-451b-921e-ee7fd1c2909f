/**
 * 搜索结果视图切换修复测试脚本
 * 在浏览器控制台中运行此脚本来测试修复效果
 */

// 测试函数
function testSearchViewToggleFix() {
    console.log('🔧 开始测试搜索结果视图切换修复...');
    
    // 检查必要的对象是否存在
    if (typeof fileManager === 'undefined') {
        console.error('❌ fileManager 对象不存在');
        return false;
    }
    
    if (typeof searchManager === 'undefined') {
        console.error('❌ searchManager 对象不存在');
        return false;
    }
    
    console.log('✅ 必要对象检查通过');
    
    // 检查搜索状态属性
    console.log('📊 当前状态:');
    console.log(`  - fileManager.isInSearchMode: ${fileManager.isInSearchMode}`);
    console.log(`  - fileManager.searchResults.length: ${fileManager.searchResults?.length || 0}`);
    console.log(`  - fileManager.files.length: ${fileManager.files?.length || 0}`);
    console.log(`  - searchManager.isInSearchMode: ${searchManager.isInSearchMode}`);
    console.log(`  - searchManager.searchResults.length: ${searchManager.searchResults?.length || 0}`);
    
    // 模拟搜索状态
    console.log('🔍 模拟搜索状态...');
    const mockSearchResults = [
        { id: 'test1', name: 'test_image_1.jpg', type: 'file', size: 1024 },
        { id: 'test2', name: 'test_image_2.png', type: 'file', size: 2048 },
        { id: 'test3', name: 'test_image_3.psd', type: 'file', size: 4096 }
    ];
    
    // 设置搜索状态
    fileManager.isInSearchMode = true;
    fileManager.searchResults = mockSearchResults;
    fileManager.files = mockSearchResults;
    
    searchManager.isInSearchMode = true;
    searchManager.searchResults = mockSearchResults;
    
    console.log('✅ 搜索状态设置完成');
    console.log(`  - 搜索结果数量: ${mockSearchResults.length}`);
    
    // 测试视图切换
    console.log('🔄 测试视图切换...');
    const originalViewMode = fileManager.viewMode;
    const testViewModes = ['large-icons', 'medium-icons', 'small-icons', 'extra-large-icons'];
    
    let testsPassed = 0;
    let totalTests = testViewModes.length;
    
    testViewModes.forEach((viewMode, index) => {
        console.log(`  测试 ${index + 1}/${totalTests}: 切换到 ${viewMode}`);
        
        // 记录切换前的状态
        const beforeFiles = fileManager.files.length;
        const beforeSearchMode = fileManager.isInSearchMode;
        const beforeSearchResults = fileManager.searchResults.length;
        
        // 执行视图切换
        fileManager.setViewMode(viewMode);
        
        // 检查切换后的状态
        const afterFiles = fileManager.files.length;
        const afterSearchMode = fileManager.isInSearchMode;
        const afterSearchResults = fileManager.searchResults.length;
        
        // 验证结果
        const filesPreserved = (beforeFiles === afterFiles);
        const searchModePreserved = (beforeSearchMode === afterSearchMode);
        const searchResultsPreserved = (beforeSearchResults === afterSearchResults);
        const viewModeChanged = (fileManager.viewMode === viewMode);
        
        if (filesPreserved && searchModePreserved && searchResultsPreserved && viewModeChanged) {
            console.log(`    ✅ ${viewMode} 测试通过`);
            testsPassed++;
        } else {
            console.log(`    ❌ ${viewMode} 测试失败:`);
            if (!filesPreserved) console.log(`      - 文件数量变化: ${beforeFiles} -> ${afterFiles}`);
            if (!searchModePreserved) console.log(`      - 搜索模式变化: ${beforeSearchMode} -> ${afterSearchMode}`);
            if (!searchResultsPreserved) console.log(`      - 搜索结果变化: ${beforeSearchResults} -> ${afterSearchResults}`);
            if (!viewModeChanged) console.log(`      - 视图模式未更新: 期望 ${viewMode}, 实际 ${fileManager.viewMode}`);
        }
    });
    
    // 恢复原始状态
    fileManager.setViewMode(originalViewMode);
    
    // 输出测试结果
    console.log('📋 测试结果:');
    console.log(`  - 通过: ${testsPassed}/${totalTests}`);
    console.log(`  - 成功率: ${(testsPassed / totalTests * 100).toFixed(1)}%`);
    
    if (testsPassed === totalTests) {
        console.log('🎉 所有测试通过！搜索结果视图切换修复成功！');
        return true;
    } else {
        console.log('⚠️ 部分测试失败，需要进一步调试');
        return false;
    }
}

// 清除搜索状态的辅助函数
function clearTestSearchState() {
    console.log('🧹 清除测试搜索状态...');
    
    if (typeof fileManager !== 'undefined') {
        fileManager.isInSearchMode = false;
        fileManager.searchResults = [];
    }
    
    if (typeof searchManager !== 'undefined') {
        searchManager.isInSearchMode = false;
        searchManager.searchResults = [];
    }
    
    console.log('✅ 测试状态已清除');
}

// 检查当前状态的辅助函数
function checkCurrentState() {
    console.log('📊 当前系统状态:');
    
    if (typeof fileManager !== 'undefined') {
        console.log('FileManager:');
        console.log(`  - isInSearchMode: ${fileManager.isInSearchMode}`);
        console.log(`  - searchResults: ${fileManager.searchResults?.length || 0} 项`);
        console.log(`  - files: ${fileManager.files?.length || 0} 项`);
        console.log(`  - viewMode: ${fileManager.viewMode}`);
        console.log(`  - currentFolderId: ${fileManager.currentFolderId}`);
    } else {
        console.log('❌ FileManager 不可用');
    }
    
    if (typeof searchManager !== 'undefined') {
        console.log('SearchManager:');
        console.log(`  - isInSearchMode: ${searchManager.isInSearchMode}`);
        console.log(`  - searchResults: ${searchManager.searchResults?.length || 0} 项`);
        console.log(`  - currentQuery: "${searchManager.currentQuery}"`);
    } else {
        console.log('❌ SearchManager 不可用');
    }
}

// 模拟真实搜索的辅助函数
function simulateRealSearch(query = 'test') {
    console.log(`🔍 模拟真实搜索: "${query}"`);
    
    if (typeof searchManager === 'undefined') {
        console.error('❌ SearchManager 不可用');
        return false;
    }
    
    // 设置搜索输入
    const searchInput = document.querySelector('#search-input');
    if (searchInput) {
        searchInput.value = query;
    }
    
    // 模拟搜索结果
    const mockResults = [
        { id: 'real1', name: `${query}_image_1.jpg`, type: 'file', size: 1024 },
        { id: 'real2', name: `${query}_photo_2.png`, type: 'file', size: 2048 },
        { id: 'real3', name: `${query}_design_3.psd`, type: 'file', size: 4096 }
    ];
    
    // 执行搜索
    searchManager.currentQuery = query;
    searchManager.searchResults = mockResults;
    searchManager.renderSearchResults();
    
    console.log(`✅ 搜索完成，找到 ${mockResults.length} 个结果`);
    return true;
}

// 导出函数到全局作用域
window.testSearchViewToggleFix = testSearchViewToggleFix;
window.clearTestSearchState = clearTestSearchState;
window.checkCurrentState = checkCurrentState;
window.simulateRealSearch = simulateRealSearch;

console.log('🔧 搜索修复测试脚本已加载');
console.log('可用函数:');
console.log('  - testSearchViewToggleFix() - 运行完整测试');
console.log('  - checkCurrentState() - 检查当前状态');
console.log('  - simulateRealSearch(query) - 模拟真实搜索');
console.log('  - clearTestSearchState() - 清除测试状态');
