#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新下载记录脚本
"""

import pymysql

def update_download_records():
    """更新下载记录，分配用户ID"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 获取用户fjj的ID
            cursor.execute("SELECT id FROM users WHERE username = 'fjj'")
            user_result = cursor.fetchone()
            
            if not user_result:
                print("❌ 找不到用户fjj")
                return
            
            user_id = user_result[0]
            print(f"✅ 找到用户fjj，ID: {user_id}")
            
            # 更新所有没有用户ID的下载记录
            cursor.execute("""
                UPDATE download_records 
                SET user_id = %s 
                WHERE user_id IS NULL
            """, (user_id,))
            
            updated_count = cursor.rowcount
            print(f"✅ 更新了 {updated_count} 条下载记录")
            
            # 查看更新后的记录
            cursor.execute("""
                SELECT id, user_id, file_id, zip_filename, downloaded_at
                FROM download_records 
                WHERE user_id = %s
                ORDER BY downloaded_at DESC
                LIMIT 5
            """, (user_id,))
            
            records = cursor.fetchall()
            print(f"\n📋 用户 {user_id} 的下载记录 (前5条):")
            for record in records:
                print(f"ID: {record[0]}, 文件ID: {record[2]}, 文件名: {record[3]}, 时间: {record[4]}")
            
            connection.commit()
            print("\n✅ 数据库更新完成")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")

if __name__ == "__main__":
    print("🔄 更新下载记录...")
    update_download_records()
