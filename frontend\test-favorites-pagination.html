<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏分页测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .favorites-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .favorite-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: white;
        }
        .favorite-item .name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .favorite-item .meta {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 收藏分页功能测试</h1>
        
        <div class="test-section">
            <h3>📊 收藏统计信息</h3>
            <div id="stats-display" class="status info">正在加载统计信息...</div>
            <button class="btn" onclick="loadStats()">🔄 刷新统计</button>
        </div>

        <div class="test-section">
            <h3>📋 收藏列表测试</h3>
            <div>
                <label>页面大小: 
                    <select id="page-size">
                        <option value="10">10</option>
                        <option value="50" selected>50</option>
                        <option value="100">100</option>
                        <option value="500">500</option>
                        <option value="1000">1000</option>
                    </select>
                </label>
                <label>页码: 
                    <input type="number" id="page-number" value="1" min="1" style="width: 60px;">
                </label>
                <button class="btn" onclick="loadFavorites()">📥 加载收藏</button>
                <button class="btn success" onclick="loadAllFavorites()">📥 加载所有收藏</button>
            </div>
            <div id="favorites-status" class="status info">点击按钮开始测试</div>
            <div id="favorites-display" class="favorites-grid"></div>
        </div>

        <div class="test-section">
            <h3>🔧 API测试</h3>
            <button class="btn" onclick="testAPI()">🧪 测试API连接</button>
            <button class="btn" onclick="testAuth()">🔐 测试认证</button>
            <button class="btn danger" onclick="clearLog()">🗑️ 清除日志</button>
            <div id="api-log" class="log">等待测试...</div>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8086/api';
        let authToken = localStorage.getItem('auth_token');

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('api-log');
            logElement.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function clearLog() {
            document.getElementById('api-log').textContent = '';
        }

        // API请求函数
        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                log(`发送请求: ${options.method || 'GET'} ${url}`);
                const response = await fetch(url, {
                    ...options,
                    headers
                });

                const data = await response.json();
                
                if (response.ok) {
                    log(`请求成功: ${JSON.stringify(data)}`);
                    return { success: true, data };
                } else {
                    log(`请求失败: ${response.status} - ${JSON.stringify(data)}`, 'error');
                    return { success: false, error: data.error || '未知错误' };
                }
            } catch (error) {
                log(`网络错误: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        // 测试函数
        async function testAPI() {
            log('开始API连接测试...');
            const result = await apiRequest('/system/info');
            if (result.success) {
                log('API连接正常', 'success');
            } else {
                log('API连接失败', 'error');
            }
        }

        async function testAuth() {
            log('开始认证测试...');
            if (!authToken) {
                log('未找到认证令牌，请先登录', 'error');
                return;
            }
            
            const result = await apiRequest('/system/info');
            if (result.success) {
                log('认证有效', 'success');
            } else {
                log('认证失败，请重新登录', 'error');
            }
        }

        // 收藏相关函数
        async function loadStats() {
            log('加载收藏统计...');
            const result = await apiRequest('/favorites/stats');
            const statsDisplay = document.getElementById('stats-display');
            
            if (result.success) {
                const stats = result.data;
                statsDisplay.innerHTML = `
                    <strong>收藏统计:</strong><br>
                    总收藏数: ${stats.total_count || 0}<br>
                    最近7天收藏: ${stats.recent_count || 0}<br>
                    文件夹分布: ${JSON.stringify(stats.folder_stats || [])}
                `;
                statsDisplay.className = 'status success';
                log('统计信息加载成功', 'success');
            } else {
                statsDisplay.innerHTML = `加载失败: ${result.error}`;
                statsDisplay.className = 'status error';
                log('统计信息加载失败', 'error');
            }
        }

        async function loadFavorites() {
            const pageSize = document.getElementById('page-size').value;
            const pageNumber = document.getElementById('page-number').value;
            
            log(`加载收藏列表: 页码=${pageNumber}, 页面大小=${pageSize}`);
            
            const result = await apiRequest(`/favorites?page=${pageNumber}&page_size=${pageSize}`);
            const statusDisplay = document.getElementById('favorites-status');
            const favoritesDisplay = document.getElementById('favorites-display');
            
            if (result.success) {
                const data = result.data;
                const favorites = data.favorites || [];
                
                statusDisplay.innerHTML = `
                    <strong>加载成功:</strong><br>
                    当前页: ${data.page || 1}/${data.total_pages || 1}<br>
                    页面大小: ${data.page_size || 0}<br>
                    总数: ${data.total_count || 0}<br>
                    当前页显示: ${favorites.length} 个
                `;
                statusDisplay.className = 'status success';
                
                // 显示收藏列表
                favoritesDisplay.innerHTML = '';
                favorites.forEach(fav => {
                    const fileData = fav.file || fav;
                    const item = document.createElement('div');
                    item.className = 'favorite-item';
                    item.innerHTML = `
                        <div class="name">${fileData.filename || fileData.name || '未知文件'}</div>
                        <div class="meta">
                            ID: ${fileData.id}<br>
                            大小: ${formatFileSize(fileData.file_size || fileData.size || 0)}<br>
                            收藏时间: ${formatDate(fav.favorited_at)}
                        </div>
                    `;
                    favoritesDisplay.appendChild(item);
                });
                
                log(`收藏列表加载成功: ${favorites.length}/${data.total_count}`, 'success');
            } else {
                statusDisplay.innerHTML = `加载失败: ${result.error}`;
                statusDisplay.className = 'status error';
                favoritesDisplay.innerHTML = '';
                log('收藏列表加载失败', 'error');
            }
        }

        async function loadAllFavorites() {
            log('加载所有收藏（页面大小=1000）...');
            document.getElementById('page-size').value = '1000';
            document.getElementById('page-number').value = '1';
            await loadFavorites();
        }

        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            if (!dateString) return '未知';
            return new Date(dateString).toLocaleString('zh-CN');
        }

        // 页面加载时自动测试
        window.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始自动测试...');
            testAPI();
            if (authToken) {
                loadStats();
            } else {
                log('未找到认证令牌，请先登录系统', 'error');
            }
        });
    </script>
</body>
</html>
