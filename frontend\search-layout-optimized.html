<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的搜索框布局测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #dbeafe;
            --primary-color-alpha: rgba(37, 99, 235, 0.1);
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-hover: #f1f5f9;
            --border-color: #e2e8f0;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
            --spacing-6: 1.5rem;
            --font-size-sm: 14px;
            --transition: all 0.2s ease;
            --border-radius: 6px;
            --border-radius-lg: 8px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* 导航栏样式 */
        .navbar {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-6);
            height: 64px;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            font-weight: 600;
            color: var(--primary-color);
            font-size: 18px;
            flex-shrink: 0;
            min-width: 200px;
        }

        .nav-brand i {
            font-size: 24px;
        }

        /* 优化后的搜索框样式 */
        .nav-search {
            flex: 1;
            max-width: 450px;
            margin: 0 var(--spacing-4);
        }

        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 28px;
            transition: all 0.2s ease;
            overflow: hidden;
            height: 44px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .search-container:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .search-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-color-alpha), 0 4px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
        }

        .search-container .fa-search {
            position: absolute;
            left: 18px;
            color: var(--gray-500);
            font-size: 16px;
            z-index: 2;
            transition: color 0.2s ease;
        }

        .search-container:focus-within .fa-search {
            color: var(--primary-color);
        }

        .search-container input {
            width: 100%;
            padding: 12px 56px 12px 48px;
            font-size: 15px;
            font-weight: 400;
            border: none;
            background: transparent;
            color: var(--gray-800);
            outline: none;
            height: 20px;
            line-height: 20px;
        }

        .search-container input::placeholder {
            color: var(--gray-500);
            font-weight: 400;
        }

        .search-type-indicator {
            position: absolute;
            right: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            color: white;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .search-container:focus-within .search-type-indicator {
            transform: scale(1.05);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
        }

        .search-type-indicator .fa-image {
            font-size: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-search {
                max-width: 300px;
                margin: 0 var(--spacing-2);
            }
            
            .search-container {
                height: 40px;
                border-radius: 24px;
            }
            
            .search-container input {
                padding: 10px 48px 10px 40px;
                font-size: 14px;
            }
            
            .search-container .fa-search {
                left: 14px;
                font-size: 14px;
            }
            
            .search-type-indicator {
                right: 12px;
                width: 24px;
                height: 24px;
                font-size: 10px;
            }
            
            .search-type-indicator .fa-image {
                font-size: 10px;
            }
        }

        @media (max-width: 480px) {
            .nav-search {
                max-width: 250px;
                margin: 0 var(--spacing-1);
            }
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-4);
            flex-shrink: 0;
            min-width: 220px;
            justify-content: flex-end;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            padding: var(--spacing-2) var(--spacing-4);
            background-color: transparent;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            color: var(--gray-700);
            cursor: pointer;
            transition: var(--transition);
        }

        .nav-btn:hover {
            background-color: var(--gray-100);
        }

        /* 测试区域样式 */
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list {
            list-style: none;
            margin: 1rem 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list li::before {
            content: "✨";
            font-size: 1.2rem;
        }

        .highlight-box {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .highlight-box h4 {
            color: #0369a1;
            margin-bottom: 0.5rem;
        }

        .demo-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .demo-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            background: transparent;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .demo-btn:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 优化后的导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-cloud"></i>
                <span>文件共享系统</span>
            </div>
            
            <div class="nav-search">
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" placeholder="搜索图片文件...">
                    <div class="search-type-indicator">
                        <i class="fas fa-image"></i>
                    </div>
                </div>
            </div>
            
            <div class="nav-actions">
                <button class="nav-btn">
                    <i class="fas fa-upload"></i>
                    <span>上传</span>
                </button>
                <button class="nav-btn">
                    <i class="fas fa-bell"></i>
                </button>
            </div>
        </div>
    </nav>

    <div class="test-container">
        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-magic"></i>
                专业级搜索框设计
            </div>
            
            <div class="highlight-box">
                <h4>🎯 设计亮点</h4>
                <ul class="feature-list">
                    <li>现代化圆角设计 (28px 圆角)</li>
                    <li>优雅的悬停和焦点效果</li>
                    <li>渐变色图片类型指示器</li>
                    <li>微妙的阴影和动画效果</li>
                    <li>完全移除历史记录功能</li>
                    <li>专注于图片搜索体验</li>
                </ul>
            </div>

            <div class="demo-actions">
                <button class="demo-btn" onclick="testSearch()">测试搜索功能</button>
                <button class="demo-btn" onclick="testResponsive()">测试响应式</button>
                <button class="demo-btn" onclick="clearSearch()">清空搜索</button>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-mobile-alt"></i>
                响应式测试
            </div>
            <p>请调整浏览器窗口大小测试不同屏幕尺寸下的搜索框表现：</p>
            <ul class="feature-list">
                <li>大屏幕 (>768px): 最大宽度 450px，完整功能</li>
                <li>中等屏幕 (≤768px): 最大宽度 300px，适当缩小</li>
                <li>小屏幕 (≤480px): 最大宽度 250px，紧凑布局</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">
                <i class="fas fa-check-circle"></i>
                功能改进总结
            </div>
            <div class="highlight-box">
                <h4>✅ 已完成的优化</h4>
                <ul class="feature-list">
                    <li>完全移除搜索历史记录功能</li>
                    <li>移除历史记录下拉建议框</li>
                    <li>简化搜索事件处理逻辑</li>
                    <li>优化搜索框视觉设计</li>
                    <li>增强响应式布局支持</li>
                    <li>专注于图片文件搜索</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 测试搜索功能
        function testSearch() {
            const searchInput = document.getElementById('search-input');
            searchInput.value = '测试搜索内容';
            searchInput.focus();
            console.log('搜索测试: ', searchInput.value);
        }

        // 测试响应式
        function testResponsive() {
            alert('请手动调整浏览器窗口大小来测试响应式效果');
        }

        // 清空搜索
        function clearSearch() {
            const searchInput = document.getElementById('search-input');
            searchInput.value = '';
            searchInput.blur();
        }

        // 搜索框交互测试
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            const searchContainer = document.querySelector('.search-container');

            searchInput.addEventListener('focus', function() {
                console.log('✅ 搜索框获得焦点 - 无历史记录弹出');
            });

            searchInput.addEventListener('input', function() {
                console.log('🔍 搜索内容:', this.value);
            });

            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    console.log('🚀 执行搜索:', this.value);
                }
                if (e.key === 'Escape') {
                    this.value = '';
                    this.blur();
                    console.log('❌ 清空搜索');
                }
            });
        });
    </script>
</body>
</html>
