#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

try:
    print("连接数据库...")
    conn = pymysql.connect(
        host='localhost',
        user='root', 
        password='123456',
        database='file_share_system',
        charset='utf8mb4'
    )
    
    cursor = conn.cursor()
    
    # 添加 folder_id 字段
    try:
        cursor.execute("""
            ALTER TABLE download_records 
            ADD COLUMN folder_id INT NULL COMMENT '文件夹ID（文件夹下载时使用）' 
            AFTER file_id
        """)
        print("✅ 添加 folder_id 字段成功")
    except Exception as e:
        if "Duplicate column name" in str(e):
            print("✅ folder_id 字段已存在")
        else:
            print(f"❌ 添加字段失败: {e}")
    
    # 修改 file_id 为可空
    try:
        cursor.execute("""
            ALTER TABLE download_records 
            MODIFY COLUMN file_id INT NULL COMMENT '文件ID（单文件/批量下载时使用）'
        """)
        print("✅ 修改 file_id 字段成功")
    except Exception as e:
        print(f"❌ 修改字段失败: {e}")
    
    # 添加索引
    try:
        cursor.execute("""
            CREATE INDEX idx_folder_download 
            ON download_records (folder_id, downloaded_at)
        """)
        print("✅ 添加索引成功")
    except Exception as e:
        if "Duplicate key name" in str(e):
            print("✅ 索引已存在")
        else:
            print(f"❌ 添加索引失败: {e}")
    
    # 更新用户ID
    cursor.execute("SELECT id FROM users WHERE username = 'fjj'")
    user_result = cursor.fetchone()
    if user_result:
        user_id = user_result[0]
        cursor.execute("UPDATE download_records SET user_id = %s WHERE user_id IS NULL", (user_id,))
        updated = cursor.rowcount
        print(f"✅ 更新了 {updated} 条记录的用户ID")
    
    conn.commit()
    
    # 显示表结构
    cursor.execute("DESCRIBE download_records")
    columns = cursor.fetchall()
    print("\n📋 表结构:")
    for col in columns:
        print(f"  {col[0]} - {col[1]} - {col[2]}")
    
    cursor.close()
    conn.close()
    print("\n🎉 更新完成!")
    
except Exception as e:
    print(f"❌ 错误: {e}")
