#!/usr/bin/env python3
"""
检查收藏功能是否正常工作
"""

import pymysql
import sys

def check_favorites_system():
    """检查收藏系统状态"""
    print("🔍 检查收藏功能状态...")
    print("=" * 50)
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查收藏表是否存在
            cursor.execute("SHOW TABLES LIKE 'user_favorites'")
            if cursor.fetchone():
                print("✅ user_favorites 表存在")
                
                # 检查表结构
                cursor.execute("DESCRIBE user_favorites")
                columns = cursor.fetchall()
                print(f"📋 表结构 ({len(columns)} 列):")
                for col in columns:
                    print(f"   - {col[0]}: {col[1]}")
                
                # 检查收藏数据
                cursor.execute("SELECT COUNT(*) FROM user_favorites")
                count = cursor.fetchone()[0]
                print(f"📊 当前收藏记录数: {count}")
                
            else:
                print("❌ user_favorites 表不存在")
                return False
            
            # 检查用户表
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"👥 用户数量: {user_count}")
            
            # 检查文件表
            cursor.execute("SELECT COUNT(*) FROM shared_files")
            file_count = cursor.fetchone()[0]
            print(f"📁 文件数量: {file_count}")
            
            if file_count == 0:
                print("⚠️ 没有文件数据，创建一些示例文件...")
                # 创建示例文件
                cursor.execute("""
                    INSERT INTO shared_files (
                        folder_id, filename, relative_path, file_size, 
                        mime_type, extension, is_image, has_thumbnail,
                        file_modified, created_at
                    ) VALUES 
                    (1, 'test1.jpg', 'test/test1.jpg', 1024000, 'image/jpeg', '.jpg', TRUE, TRUE, NOW(), NOW()),
                    (1, 'test2.png', 'test/test2.png', 2048000, 'image/png', '.png', TRUE, TRUE, NOW(), NOW()),
                    (1, 'test3.gif', 'test/test3.gif', 512000, 'image/gif', '.gif', TRUE, TRUE, NOW(), NOW())
                """)
                connection.commit()
                print("✅ 创建了3个示例文件")
            
            # 测试收藏功能
            print("\n🧪 测试收藏功能...")
            
            # 获取第一个用户和第一个文件
            cursor.execute("SELECT id FROM users LIMIT 1")
            user_result = cursor.fetchone()
            if not user_result:
                print("❌ 没有用户数据")
                return False
            user_id = user_result[0]
            
            cursor.execute("SELECT id FROM shared_files WHERE is_image = TRUE LIMIT 1")
            file_result = cursor.fetchone()
            if not file_result:
                print("❌ 没有图片文件")
                return False
            file_id = file_result[0]
            
            # 测试添加收藏
            cursor.execute("""
                INSERT IGNORE INTO user_favorites (user_id, file_id, notes)
                VALUES (%s, %s, '测试收藏')
            """, (user_id, file_id))
            
            if cursor.rowcount > 0:
                connection.commit()
                print(f"✅ 成功添加测试收藏 (用户:{user_id}, 文件:{file_id})")
            else:
                print(f"ℹ️ 收藏已存在 (用户:{user_id}, 文件:{file_id})")
            
            # 验证收藏
            cursor.execute("""
                SELECT uf.id, uf.notes, sf.filename 
                FROM user_favorites uf 
                JOIN shared_files sf ON uf.file_id = sf.id 
                WHERE uf.user_id = %s AND uf.is_active = TRUE
            """, (user_id,))
            
            favorites = cursor.fetchall()
            print(f"📋 用户 {user_id} 的收藏列表:")
            for fav in favorites:
                print(f"   - ID:{fav[0]}, 文件:{fav[2]}, 备注:{fav[1]}")
            
        connection.close()
        
        print("\n✅ 收藏功能检查完成")
        print("\n💡 测试建议:")
        print("1. 登录前端系统 (admin/admin123)")
        print("2. 查看文件列表中的星星图标")
        print("3. 点击星星图标测试收藏/取消收藏")
        print("4. 点击左侧菜单的'收藏夹'查看收藏列表")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    if check_favorites_system():
        print("\n🎉 收藏功能准备就绪！")
        sys.exit(0)
    else:
        print("\n💥 收藏功能存在问题，请检查配置")
        sys.exit(1)
