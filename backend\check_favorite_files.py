#!/usr/bin/env python3
"""
检查收藏文件的详细信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import Database

def check_favorite_files():
    """检查收藏文件的详细信息"""
    db = Database()
    
    try:
        # 连接数据库
        db.connect()
        
        print("=== 检查收藏文件详细信息 ===")
        
        # 1. 查找test1用户
        print("\n1. 查找test1用户:")
        test1_query = "SELECT id, username FROM users WHERE username = 'test1'"
        test1_user = db.fetch_one(test1_query)
        
        if not test1_user:
            print("❌ 未找到test1用户")
            return
        
        user_id = test1_user['id']
        print(f"✅ 找到test1用户: ID={user_id}")
        
        # 2. 检查该用户的所有收藏记录
        print(f"\n2. 检查用户ID {user_id} 的所有收藏记录:")
        all_favorites_query = """
        SELECT 
            f.id as favorite_id,
            f.file_id,
            f.notes,
            f.favorited_at,
            f.is_active
        FROM favorites f
        WHERE f.user_id = %s
        ORDER BY f.favorited_at DESC
        """
        
        all_favorites = db.fetch_all(all_favorites_query, (user_id,))
        print(f"总收藏记录数: {len(all_favorites)}")
        
        if all_favorites:
            print("\n所有收藏记录:")
            for i, fav in enumerate(all_favorites, 1):
                print(f"  {i}. 收藏ID: {fav['favorite_id']}, 文件ID: {fav['file_id']}, 活跃: {fav['is_active']}")
        
        # 3. 检查活跃的收藏记录
        print(f"\n3. 检查活跃的收藏记录:")
        active_favorites_query = """
        SELECT 
            f.id as favorite_id,
            f.file_id,
            f.notes,
            f.favorited_at
        FROM favorites f
        WHERE f.user_id = %s AND f.is_active = 1
        ORDER BY f.favorited_at DESC
        """
        
        active_favorites = db.fetch_all(active_favorites_query, (user_id,))
        print(f"活跃收藏记录数: {len(active_favorites)}")
        
        if not active_favorites:
            print("❌ 没有活跃的收藏记录")
            return
        
        # 4. 检查对应的文件信息
        print(f"\n4. 检查对应的文件信息:")
        file_ids = [str(fav['file_id']) for fav in active_favorites]
        files_query = f"""
        SELECT 
            id,
            filename,
            file_size,
            is_image,
            folder_id,
            created_at
        FROM files 
        WHERE id IN ({','.join(['%s'] * len(file_ids))})
        """
        
        files = db.fetch_all(files_query, file_ids)
        print(f"找到文件数: {len(files)}")
        
        if files:
            print("\n文件详情:")
            for file in files:
                print(f"  文件ID: {file['id']}")
                print(f"  文件名: {file['filename']}")
                print(f"  文件大小: {file['file_size']}")
                print(f"  是否图片: {file['is_image']}")
                print(f"  文件夹ID: {file['folder_id']}")
                print(f"  创建时间: {file['created_at']}")
                print()
        
        # 5. 检查图片文件过滤的影响
        print(f"\n5. 检查图片文件过滤的影响:")
        image_files_query = f"""
        SELECT 
            fi.id,
            fi.filename,
            fi.is_image
        FROM files fi
        WHERE fi.id IN ({','.join(['%s'] * len(file_ids))}) AND fi.is_image = 1
        """
        
        image_files = db.fetch_all(image_files_query, file_ids)
        print(f"图片文件数: {len(image_files)}")
        
        if len(image_files) != len(files):
            print(f"⚠️ 过滤掉了 {len(files) - len(image_files)} 个非图片文件")
            
            non_image_query = f"""
            SELECT 
                fi.id,
                fi.filename,
                fi.is_image
            FROM files fi
            WHERE fi.id IN ({','.join(['%s'] * len(file_ids))}) AND (fi.is_image = 0 OR fi.is_image IS NULL)
            """
            
            non_image_files = db.fetch_all(non_image_query, file_ids)
            if non_image_files:
                print("被过滤的非图片文件:")
                for file in non_image_files:
                    print(f"  文件ID: {file['id']}, 文件名: {file['filename']}, is_image: {file['is_image']}")
        
        # 6. 模拟后端查询
        print(f"\n6. 模拟后端查询:")
        backend_query = """
        SELECT 
            f.id as favorite_id,
            f.file_id,
            f.notes,
            f.favorited_at,
            fi.filename,
            fi.file_size,
            fi.is_image,
            fo.name as folder_name
        FROM favorites f
        JOIN files fi ON f.file_id = fi.id
        LEFT JOIN folders fo ON fi.folder_id = fo.id
        WHERE f.user_id = %s 
        AND f.is_active = 1 
        AND fi.is_image = 1
        ORDER BY f.favorited_at DESC
        """
        
        backend_result = db.fetch_all(backend_query, (user_id,))
        print(f"后端查询结果数: {len(backend_result)}")
        
        if backend_result:
            print("后端查询结果:")
            for i, result in enumerate(backend_result, 1):
                print(f"  {i}. 收藏ID: {result['favorite_id']}")
                print(f"     文件ID: {result['file_id']}")
                print(f"     文件名: {result['filename']}")
                print(f"     是否图片: {result['is_image']}")
                print(f"     文件夹: {result['folder_name']}")
                print()
        else:
            print("❌ 后端查询没有返回结果")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    check_favorite_files()
