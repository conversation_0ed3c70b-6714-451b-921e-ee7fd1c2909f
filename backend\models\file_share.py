#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件共享模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, BigInteger, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base
import os
import hashlib
from datetime import datetime
from typing import Optional, Dict, Any

class SharedFolder(Base):
    """共享文件夹模型"""
    
    __tablename__ = 'shared_folders'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False, comment='文件夹名称')
    path = Column(Text, nullable=False, comment='文件夹路径')
    description = Column(Text, nullable=True, comment='描述')
    
    # 权限设置
    is_active = Column(Boolean, default=True, comment='是否启用')
    allow_read = Column(Boolean, default=True, comment='允许读取')
    allow_write = Column(Boolean, default=False, comment='允许写入')
    allow_delete = Column(Boolean, default=False, comment='允许删除')
    allow_upload = Column(Boolean, default=False, comment='允许上传')
    allow_download = Column(Boolean, default=True, comment='允许下载')
    
    # 网络访问设置
    allow_internal = Column(Boolean, default=True, comment='允许内网访问')
    allow_external = Column(Boolean, default=False, comment='允许外网访问')
    
    # 显示设置
    show_details = Column(Boolean, default=True, comment='显示详细信息')
    enable_thumbnail = Column(Boolean, default=True, comment='启用缩略图')
    
    # 限制设置
    max_file_size = Column(BigInteger, default=0, comment='最大文件大小(字节)')
    allowed_extensions = Column(JSON, nullable=True, comment='允许的文件扩展名')
    
    # 统计信息
    file_count = Column(Integer, default=0, comment='文件数量')
    total_size = Column(BigInteger, default=0, comment='总大小(字节)')
    access_count = Column(Integer, default=0, comment='访问次数')
    download_count = Column(Integer, default=0, comment='下载次数')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    last_scanned = Column(DateTime, nullable=True, comment='最后扫描时间')
    
    # 关联关系
    files = relationship("SharedFile", back_populates="folder", cascade="all, delete-orphan")
    
    def __init__(self, name: str, path: str, **kwargs):
        self.name = name
        self.path = os.path.abspath(path)
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def is_path_valid(self) -> bool:
        """检查路径是否有效"""
        return os.path.exists(self.path) and os.path.isdir(self.path)
    
    def get_permissions(self) -> Dict[str, bool]:
        """获取权限设置"""
        return {
            'read': self.allow_read,
            'write': self.allow_write,
            'delete': self.allow_delete,
            'upload': self.allow_upload,
            'download': self.allow_download
        }
    
    def update_statistics(self):
        """更新统计信息"""
        if not self.is_path_valid():
            return
        
        file_count = 0
        total_size = 0
        
        try:
            for root, dirs, files in os.walk(self.path):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.isfile(file_path):
                        file_count += 1
                        total_size += os.path.getsize(file_path)
            
            self.file_count = file_count
            self.total_size = total_size
            self.last_scanned = datetime.now()
            
        except Exception as e:
            print(f"更新文件夹统计信息失败: {e}")
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'path': self.path,
            'description': self.description,
            'is_active': self.is_active,
            'permissions': self.get_permissions(),
            'network_access': {
                'internal': self.allow_internal,
                'external': self.allow_external
            },
            'display_settings': {
                'show_details': self.show_details,
                'enable_thumbnail': self.enable_thumbnail
            },
            'limits': {
                'max_file_size': self.max_file_size,
                'allowed_extensions': self.allowed_extensions
            },
            'statistics': {
                'file_count': self.file_count,
                'total_size': self.total_size,
                'access_count': self.access_count,
                'download_count': self.download_count
            },
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_scanned': self.last_scanned.isoformat() if self.last_scanned else None
        }

class SharedFile(Base):
    """共享文件模型"""
    
    __tablename__ = 'shared_files'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    folder_id = Column(Integer, ForeignKey('shared_folders.id'), nullable=False)
    
    # 文件信息
    filename = Column(String(255), nullable=False, comment='文件名')
    relative_path = Column(Text, nullable=False, comment='相对路径')
    file_size = Column(BigInteger, nullable=False, comment='文件大小(字节)')
    file_hash = Column(String(64), nullable=True, comment='文件哈希值')
    mime_type = Column(String(100), nullable=True, comment='MIME类型')
    extension = Column(String(20), nullable=True, comment='文件扩展名')
    
    # 文件属性
    is_image = Column(Boolean, default=False, comment='是否为图片')
    is_video = Column(Boolean, default=False, comment='是否为视频')
    is_document = Column(Boolean, default=False, comment='是否为文档')
    
    # 缩略图信息
    has_thumbnail = Column(Boolean, default=False, comment='是否有缩略图')
    thumbnail_path = Column(Text, nullable=True, comment='缩略图路径')
    
    # 图像信息（如果是图片）
    image_width = Column(Integer, nullable=True, comment='图片宽度')
    image_height = Column(Integer, nullable=True, comment='图片高度')
    image_format = Column(String(20), nullable=True, comment='图片格式')
    
    # 统计信息
    view_count = Column(Integer, default=0, comment='查看次数')
    download_count = Column(Integer, default=0, comment='下载次数')
    
    # 时间戳
    file_modified = Column(DateTime, nullable=True, comment='文件修改时间')
    created_at = Column(DateTime, default=func.now(), comment='记录创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='记录更新时间')
    last_accessed = Column(DateTime, nullable=True, comment='最后访问时间')
    
    # 关联关系
    folder = relationship("SharedFolder", back_populates="files")
    
    def __init__(self, folder_id: int, filename: str, relative_path: str, **kwargs):
        self.folder_id = folder_id
        self.filename = filename
        self.relative_path = relative_path
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_full_path(self) -> str:
        """获取完整文件路径"""
        if self.folder:
            return os.path.join(self.folder.path, self.relative_path)
        return self.relative_path
    
    def calculate_hash(self) -> str:
        """计算文件哈希值"""
        try:
            full_path = self.get_full_path()
            if not os.path.exists(full_path):
                return ""
            
            hash_md5 = hashlib.md5()
            with open(full_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            
            self.file_hash = hash_md5.hexdigest()
            return self.file_hash
            
        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return ""
    
    def update_file_info(self):
        """更新文件信息"""
        try:
            full_path = self.get_full_path()
            if not os.path.exists(full_path):
                return False
            
            # 获取文件统计信息
            stat = os.stat(full_path)
            self.file_size = stat.st_size
            self.file_modified = datetime.fromtimestamp(stat.st_mtime)
            
            # 获取文件扩展名
            _, ext = os.path.splitext(self.filename)
            self.extension = ext.lower()
            
            # 判断文件类型
            image_exts = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp'}
            video_exts = {'.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'}
            doc_exts = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}
            
            self.is_image = self.extension in image_exts
            self.is_video = self.extension in video_exts
            self.is_document = self.extension in doc_exts
            
            return True
            
        except Exception as e:
            print(f"更新文件信息失败: {e}")
            return False
    
    def increment_view_count(self):
        """增加查看次数"""
        self.view_count += 1
        self.last_accessed = datetime.now()
    
    def increment_download_count(self):
        """增加下载次数"""
        self.download_count += 1
        self.last_accessed = datetime.now()
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'id': self.id,
            'folder_id': self.folder_id,
            'filename': self.filename,
            'relative_path': self.relative_path,
            'file_size': self.file_size,
            'file_hash': self.file_hash,
            'mime_type': self.mime_type,
            'extension': self.extension,
            'file_type': {
                'is_image': self.is_image,
                'is_video': self.is_video,
                'is_document': self.is_document
            },
            'thumbnail': {
                'has_thumbnail': self.has_thumbnail,
                'thumbnail_path': self.thumbnail_path
            },
            'image_info': {
                'width': self.image_width,
                'height': self.image_height,
                'format': self.image_format
            } if self.is_image else None,
            'statistics': {
                'view_count': self.view_count,
                'download_count': self.download_count
            },
            'timestamps': {
                'file_modified': self.file_modified.isoformat() if self.file_modified else None,
                'created_at': self.created_at.isoformat() if self.created_at else None,
                'last_accessed': self.last_accessed.isoformat() if self.last_accessed else None
            }
        }
