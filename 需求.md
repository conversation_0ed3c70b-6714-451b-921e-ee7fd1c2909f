软件一定要稳定流畅，搜索快，注意数据安全，原码给我们 
 
有几台电脑有共用的图片文件，要求有的只允许读不能更改，有的允许读允许改，可以设置不同权限。好几个盘，里面文件非常多，多文件夹，多目录，其它所有电脑都能快速的调取这个公用的图片、文件，用户端可以同时搜索或勾选选择搜索这些文件(服务端可以设置权限，哪个共享是否只读 修改 删除 替换 显示目录详细信息等)   
要有外网功能，能外网访问，服务端可关闭可开启（不同共享文件夹可以勾选内网外网） 
 
 
有缩略图(超大图标 大图标 中等图标 详情) 格式有JPG PSD TIF AI EPS PNG等，可放大缩小，用户端浏览看图时能快速浏览并找到对应文件。 
单文件下载 、打包下载 、文件夹下载、批量多选下载(各项可在服务端开启或关闭。服务端可更改批量下载数，可设置单次打包下载、文件夹下载大小多少MB) 
各用户访问浏览观看 上传 下载功能 服务端可开启或关闭 
 
用户搜索、下载行为需有记录，服务端可以快速清楚查到哪个用户行为，有排行，方便查取统计 
不允许搜索的信息，服务端可以设置屏蔽，让用户无法查取。可设置敏感文件，一但搜索及下载服务端重点标注，警告 
限制访问 限流 违规禁止登陆并有时间限制倒计时解开 服务端可设置 
服务端能看到当前在线用户数量名称 可以设置搜索、下载超过设定量减速或禁止下载 
用户端有登陆账户，一户一密码，服务端可以控制修改 删除 注册 密码等，注册可以开启或关闭，服务端可设登陆密码 
不同用户端在不同组，就是在服务端方便查看其等级，如只读、下载、上传等 
软件界面有通知功能 滚动字幕 能带截图 
能显示当前数据总量 各种文件多少 今日下载量  上传量 
对于删除、修改了哪个文件服务端需要严格清楚记录监控并提示警示 


下载文件，下载后的压缩包是加密的，然后这个可以服务端设置下载多少次后你再下载的压缩文件就是加密的了，加密就是正常压缩加密就行。 加密后的文件得有解压密码 才能解开，解压密码可以向服务端自动申请，申请次数服务端可以设置，
 
软件要兼容新版系统，兼容性要好，不能出新版就不能用了 
 
软件要加密 做个注册机 
 
两套搜索引擎 一个类似Everything搜索的速度或超越，一个识图（识图要快准）  用户对引擎可勾选 服务端可关闭或开启显示某个引擎 
 
软件可远程管理，方便维护 
 
软件不允许对共享文件及文件夹做任何变化，如果需要生成任何数据请存储到其它指定文件夹，不能存储在原共享文件及文件夹（原图片文件就是原图片文件，不能掺杂其它数据，这样清晰明了） 
 
整体要求 快准稳简洁   软件不能有侵权行为保证原创