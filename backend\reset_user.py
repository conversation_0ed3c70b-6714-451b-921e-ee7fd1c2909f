#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重置用户状态脚本
"""

import pymysql
from datetime import datetime

def reset_user_status():
    """重置用户状态"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 重置所有用户的失败登录次数和禁用状态
            cursor.execute("""
                UPDATE users 
                SET failed_login_attempts = 0, 
                    is_banned = FALSE, 
                    ban_until = NULL 
                WHERE username IN ('fjj', 'admin')
            """)
            
            # 查看用户信息
            cursor.execute("""
                SELECT username, full_name, is_banned, failed_login_attempts, 
                       last_login, ban_until
                FROM users 
                WHERE username IN ('fjj', 'admin')
            """)
            
            users = cursor.fetchall()
            print("用户状态:")
            for user in users:
                print(f"用户名: {user[0]}")
                print(f"全名: {user[1]}")
                print(f"是否被禁用: {user[2]}")
                print(f"失败登录次数: {user[3]}")
                print(f"最后登录: {user[4]}")
                print(f"禁用到期: {user[5]}")
                print("-" * 30)
            
            connection.commit()
            print("✅ 用户状态重置成功")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ 重置失败: {e}")

def create_test_user():
    """创建测试用户"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查用户是否存在
            cursor.execute("SELECT id FROM users WHERE username = 'test'")
            if cursor.fetchone():
                print("测试用户已存在")
                return
            
            # 创建测试用户 - 使用简单密码哈希
            import hashlib
            import secrets
            
            password = "test123"
            salt = secrets.token_hex(16)
            combined = password + salt
            password_hash = hashlib.sha256(combined.encode('utf-8')).hexdigest()
            
            cursor.execute("""
                INSERT INTO users (username, password_hash, salt, full_name, user_group)
                VALUES ('test', %s, %s, '测试用户', 'user')
            """, (password_hash, salt))
            
            connection.commit()
            print("✅ 测试用户创建成功")
            print("用户名: test")
            print("密码: test123")
            
        connection.close()
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")

if __name__ == "__main__":
    print("🔧 重置用户状态...")
    reset_user_status()
    
    print("\n👤 创建测试用户...")
    create_test_user()
