#!/usr/bin/env python3
"""
自动迁移脚本 - 一键完成收藏功能迁移
"""

import sys
import os
import subprocess
import time

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 文件共享系统 - 收藏功能自动迁移工具")
    print("=" * 60)
    print()

def run_command(command, description, check_success=True):
    """运行命令并显示结果"""
    print(f"📋 {description}...")
    print(f"💻 执行命令: {command}")
    
    try:
        if isinstance(command, list):
            result = subprocess.run(command, capture_output=True, text=True, cwd=os.path.dirname(os.path.abspath(__file__)))
        else:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd=os.path.dirname(os.path.abspath(__file__)))
        
        if result.stdout:
            print("📤 输出:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误信息:")
            print(result.stderr)
        
        if check_success and result.returncode != 0:
            print(f"❌ {description}失败 (返回码: {result.returncode})")
            return False
        else:
            print(f"✅ {description}完成")
            return True
            
    except Exception as e:
        print(f"❌ {description}失败: {e}")
        return False

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    print("✅ Python版本检查通过")
    return True

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_packages = ['pymysql']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"⚠️ 缺少依赖: {', '.join(missing_packages)}")
        print("💡 尝试自动安装...")
        
        for package in missing_packages:
            if not run_command([sys.executable, '-m', 'pip', 'install', package], f"安装 {package}"):
                return False
    
    return True

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        import pymysql
        
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'charset': 'utf8mb4'
        }
        
        # 测试连接
        connection = pymysql.connect(**config)
        connection.close()
        print("✅ 数据库连接测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print("💡 请确保MySQL服务已启动，用户名密码正确")
        return False

def initialize_database():
    """初始化数据库"""
    print("🗄️ 初始化数据库...")
    
    # 运行数据库初始化脚本
    if not run_command([sys.executable, 'init_database.py'], "数据库初始化"):
        return False
    
    print("✅ 数据库初始化完成")
    return True

def test_favorite_service():
    """测试收藏服务"""
    print("🧪 测试收藏服务...")
    
    try:
        from services.favorite_service_simple import SimpleFavoriteService
        
        service = SimpleFavoriteService()
        
        # 测试获取收藏列表（应该返回空列表）
        result = service.get_user_favorites(1)
        
        if result.get('success'):
            print("✅ 收藏服务测试成功")
            return True
        else:
            print(f"❌ 收藏服务测试失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 收藏服务测试失败: {e}")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    startup_script = """@echo off
echo 启动文件共享系统...
cd /d "%~dp0"
python main.py
pause
"""
    
    try:
        with open('start_server.bat', 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        print("✅ 启动脚本创建成功: start_server.bat")
        return True
        
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    steps = [
        ("检查Python版本", check_python_version),
        ("检查依赖", check_dependencies),
        ("测试数据库连接", test_database_connection),
        ("初始化数据库", initialize_database),
        ("测试收藏服务", test_favorite_service),
        ("创建启动脚本", create_startup_script),
    ]
    
    print("🎯 开始自动迁移流程...")
    print(f"📋 总共 {len(steps)} 个步骤")
    print()
    
    failed_steps = []
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        print(f"📍 步骤 {i}/{len(steps)}: {step_name}")
        print("-" * 40)
        
        if step_func():
            print(f"✅ 步骤 {i} 完成")
        else:
            print(f"❌ 步骤 {i} 失败")
            failed_steps.append(step_name)
        
        print()
        time.sleep(1)  # 短暂暂停，让用户看清输出
    
    # 总结
    print("=" * 60)
    print("📊 迁移完成总结")
    print("=" * 60)
    
    if not failed_steps:
        print("🎉 所有步骤都成功完成！")
        print()
        print("✅ 收藏功能已成功迁移到数据库")
        print("✅ 系统已准备就绪，可以正常使用")
        print()
        print("🚀 启动方式:")
        print("   方式1: 双击 start_server.bat")
        print("   方式2: 运行 python main.py")
        print()
        print("🌐 访问地址:")
        print("   前端: http://localhost:3000")
        print("   API: http://localhost:8080")
        print()
        print("👤 默认管理员账户:")
        print("   用户名: admin")
        print("   密码: admin123")
        print()
        return 0
    else:
        print(f"⚠️ {len(failed_steps)} 个步骤失败:")
        for step in failed_steps:
            print(f"   - {step}")
        print()
        print("💡 建议:")
        print("   1. 检查错误信息并解决问题")
        print("   2. 重新运行此脚本")
        print("   3. 或手动执行失败的步骤")
        print()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        
        print("按任意键退出...")
        input()
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
