#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查依赖
"""

print("检查Python版本...")
import sys
print(f"Python版本: {sys.version}")

print("\n检查基础模块...")
try:
    import os
    print("✅ os")
except ImportError as e:
    print(f"❌ os: {e}")

try:
    from pathlib import Path
    print("✅ pathlib")
except ImportError as e:
    print(f"❌ pathlib: {e}")

print("\n检查图像处理库...")
try:
    from PIL import Image
    print("✅ PIL (Pillow)")
except ImportError as e:
    print(f"❌ PIL: {e}")

try:
    import cv2
    print("✅ OpenCV")
except ImportError as e:
    print(f"❌ OpenCV: {e}")

print("\n检查Flask...")
try:
    from flask import Flask
    print("✅ Flask")
except ImportError as e:
    print(f"❌ Flask: {e}")

try:
    from flask_cors import CORS
    print("✅ Flask-CORS")
except ImportError as e:
    print(f"❌ Flask-CORS: {e}")

print("\n检查数据库...")
try:
    import pymysql
    print("✅ PyMySQL")
except ImportError as e:
    print(f"❌ PyMySQL: {e}")

print("\n检查项目模块...")
try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from utils.logger import setup_logger
    print("✅ utils.logger")
except ImportError as e:
    print(f"❌ utils.logger: {e}")

try:
    from services.thumbnail_service import ThumbnailService
    print("✅ services.thumbnail_service")
except ImportError as e:
    print(f"❌ services.thumbnail_service: {e}")

try:
    from config.database import DatabaseManager
    print("✅ config.database")
except ImportError as e:
    print(f"❌ config.database: {e}")

print("\n检查完成！")
