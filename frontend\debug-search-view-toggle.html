<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索视图切换调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-panel h3 {
            margin-top: 0;
            color: #333;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-value {
            font-weight: bold;
        }
        .status-value.true {
            color: #10b981;
        }
        .status-value.false {
            color: #ef4444;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn.primary {
            background: #2563eb;
            color: white;
        }
        .test-btn.secondary {
            background: #6b7280;
            color: white;
        }
        .test-btn.success {
            background: #10b981;
            color: white;
        }
        .test-btn.warning {
            background: #f59e0b;
            color: white;
        }
        .log-panel {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
        }
        .log-entry.info {
            color: #60a5fa;
        }
        .log-entry.warn {
            color: #fbbf24;
        }
        .log-entry.error {
            color: #f87171;
        }
        .instructions {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h4 {
            margin-top: 0;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h4>🔧 调试说明</h4>
        <p>1. 打开浏览器开发者工具 (F12)</p>
        <p>2. 切换到 Console 标签页</p>
        <p>3. 在实际系统中搜索任意内容</p>
        <p>4. 在搜索结果页面点击视图切换按钮</p>
        <p>5. 观察控制台输出的调试信息</p>
    </div>

    <div class="debug-panel">
        <h3>🔍 搜索状态监控</h3>
        <div class="status-item">
            <span>文件管理器搜索模式:</span>
            <span class="status-value" id="fileManager-isInSearchMode">未知</span>
        </div>
        <div class="status-item">
            <span>搜索管理器搜索模式:</span>
            <span class="status-value" id="searchManager-isInSearchMode">未知</span>
        </div>
        <div class="status-item">
            <span>文件管理器搜索结果数量:</span>
            <span class="status-value" id="fileManager-searchResults">未知</span>
        </div>
        <div class="status-item">
            <span>搜索管理器搜索结果数量:</span>
            <span class="status-value" id="searchManager-searchResults">未知</span>
        </div>
        <div class="status-item">
            <span>当前文件列表数量:</span>
            <span class="status-value" id="fileManager-files">未知</span>
        </div>
        <div class="status-item">
            <span>当前视图模式:</span>
            <span class="status-value" id="fileManager-viewMode">未知</span>
        </div>
    </div>

    <div class="test-buttons">
        <button class="test-btn primary" onclick="checkStatus()">检查状态</button>
        <button class="test-btn secondary" onclick="simulateViewToggle()">模拟视图切换</button>
        <button class="test-btn success" onclick="clearLogs()">清空日志</button>
        <button class="test-btn warning" onclick="openMainPage()">打开主页面</button>
    </div>

    <div class="debug-panel">
        <h3>📋 调试日志</h3>
        <div class="log-panel" id="debug-logs">
            <div class="log-entry info">[INFO] 调试面板已加载</div>
        </div>
    </div>

    <script>
        let logCount = 0;

        function log(level, message) {
            const logsContainer = document.getElementById('debug-logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            logCount++;
            if (logCount > 100) {
                // 保持日志数量在合理范围内
                const firstLog = logsContainer.firstElementChild;
                if (firstLog) firstLog.remove();
                logCount--;
            }
        }

        function checkStatus() {
            try {
                // 检查是否在主页面的iframe中
                if (window.parent && window.parent.fileManager) {
                    const fm = window.parent.fileManager;
                    const sm = window.parent.searchManager;
                    
                    updateStatus('fileManager-isInSearchMode', fm.isInSearchMode);
                    updateStatus('fileManager-searchResults', fm.searchResults ? fm.searchResults.length : 0);
                    updateStatus('fileManager-files', fm.files ? fm.files.length : 0);
                    updateStatus('fileManager-viewMode', fm.viewMode || '未知');
                    
                    if (sm) {
                        updateStatus('searchManager-isInSearchMode', sm.isInSearchMode);
                        updateStatus('searchManager-searchResults', sm.searchResults ? sm.searchResults.length : 0);
                    } else {
                        updateStatus('searchManager-isInSearchMode', '未找到');
                        updateStatus('searchManager-searchResults', '未找到');
                    }
                    
                    log('info', `文件管理器状态: 搜索模式=${fm.isInSearchMode}, 搜索结果=${fm.searchResults?.length || 0}, 文件数=${fm.files?.length || 0}`);
                    
                } else {
                    // 尝试直接访问全局对象
                    if (typeof window.fileManager !== 'undefined') {
                        const fm = window.fileManager;
                        updateStatus('fileManager-isInSearchMode', fm.isInSearchMode);
                        updateStatus('fileManager-searchResults', fm.searchResults ? fm.searchResults.length : 0);
                        updateStatus('fileManager-files', fm.files ? fm.files.length : 0);
                        updateStatus('fileManager-viewMode', fm.viewMode || '未知');
                        
                        log('info', `文件管理器状态: 搜索模式=${fm.isInSearchMode}, 搜索结果=${fm.searchResults?.length || 0}`);
                    } else {
                        log('warn', '无法访问文件管理器对象，请在主页面中运行此调试工具');
                        updateStatus('fileManager-isInSearchMode', '无法访问');
                        updateStatus('fileManager-searchResults', '无法访问');
                        updateStatus('fileManager-files', '无法访问');
                        updateStatus('fileManager-viewMode', '无法访问');
                    }
                    
                    if (typeof window.searchManager !== 'undefined') {
                        const sm = window.searchManager;
                        updateStatus('searchManager-isInSearchMode', sm.isInSearchMode);
                        updateStatus('searchManager-searchResults', sm.searchResults ? sm.searchResults.length : 0);
                    } else {
                        updateStatus('searchManager-isInSearchMode', '无法访问');
                        updateStatus('searchManager-searchResults', '无法访问');
                    }
                }
            } catch (error) {
                log('error', `检查状态失败: ${error.message}`);
            }
        }

        function updateStatus(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
                element.className = `status-value ${value}`;
            }
        }

        function simulateViewToggle() {
            try {
                const fm = window.parent?.fileManager || window.fileManager;
                if (fm) {
                    log('info', '模拟视图切换: large-icons -> medium-icons');
                    fm.setViewMode('medium-icons');
                    setTimeout(() => {
                        checkStatus();
                    }, 100);
                } else {
                    log('error', '无法找到文件管理器对象');
                }
            } catch (error) {
                log('error', `模拟视图切换失败: ${error.message}`);
            }
        }

        function clearLogs() {
            const logsContainer = document.getElementById('debug-logs');
            logsContainer.innerHTML = '<div class="log-entry info">[INFO] 日志已清空</div>';
            logCount = 1;
        }

        function openMainPage() {
            window.open('index.html', '_blank');
        }

        // 定期检查状态
        setInterval(checkStatus, 2000);

        // 初始检查
        setTimeout(checkStatus, 1000);

        // 监听来自主页面的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'debug') {
                log(event.data.level || 'info', event.data.message || '未知消息');
            }
        });

        log('info', '调试工具已启动，每2秒自动检查状态');
    </script>
</body>
</html>
