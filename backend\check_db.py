#!/usr/bin/env python3
"""
检查数据库中的文件和文件夹
"""

import sys
import os
sys.path.append('.')

from services.database_manager import DatabaseManager
from models.database_models import SharedFolder, SharedFile

def check_database():
    """检查数据库内容"""
    print("检查数据库内容...")
    
    try:
        db = DatabaseManager()
        db.init_database()
        
        with db.get_session() as session:
            # 检查共享文件夹
            folders = session.query(SharedFolder).all()
            print(f"\n共享文件夹数量: {len(folders)}")
            
            for folder in folders:
                print(f"  - ID: {folder.id}")
                print(f"    名称: {folder.name}")
                print(f"    路径: {folder.path}")
                print(f"    存在: {os.path.exists(folder.path)}")
                print()
            
            # 检查共享文件
            files = session.query(SharedFile).all()
            print(f"共享文件数量: {len(files)}")
            
            for i, file in enumerate(files[:10]):  # 只显示前10个
                print(f"  {i+1}. ID: {file.id}")
                print(f"     名称: {file.filename}")
                print(f"     文件夹ID: {file.folder_id}")
                print(f"     扩展名: {file.extension}")
                print(f"     大小: {file.file_size}")
                
                # 检查文件是否存在
                try:
                    full_path = file.get_full_path()
                    print(f"     完整路径: {full_path}")
                    print(f"     文件存在: {os.path.exists(full_path)}")
                except Exception as e:
                    print(f"     路径获取失败: {e}")
                print()
                
            if len(files) > 10:
                print(f"  ... 还有 {len(files) - 10} 个文件")
                
    except Exception as e:
        print(f"检查数据库失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
