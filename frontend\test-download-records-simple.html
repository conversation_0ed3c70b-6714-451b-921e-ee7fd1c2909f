<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载记录简单测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 15px; border-radius: 5px; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>下载记录简单测试</h1>
        
        <div>
            <button onclick="testLogin()">1. 登录</button>
            <button onclick="createTestData()">2. 创建测试数据</button>
            <button onclick="testAPI()">3. 测试API</button>
            <button onclick="testMainApp()">4. 测试主应用</button>
        </div>
        
        <div id="result" class="result info">点击按钮开始测试...</div>
    </div>

    <script>
        let currentToken = localStorage.getItem('token');
        
        function showResult(content, type = 'info') {
            const element = document.getElementById('result');
            element.className = `result ${type}`;
            element.textContent = content;
        }

        async function testLogin() {
            try {
                showResult('正在登录...', 'info');
                
                const response = await fetch('http://localhost:8086/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'fjj', password: '123456' })
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    currentToken = result.token;
                    localStorage.setItem('token', currentToken);
                    localStorage.setItem('fileShareAuth', JSON.stringify({
                        token: currentToken,
                        user: result.user,
                        loginTime: new Date().toISOString()
                    }));
                    showResult(`登录成功！\nToken: ${currentToken.substring(0, 20)}...\n用户: ${result.user.username}`, 'success');
                } else {
                    showResult(`登录失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`登录请求失败: ${error.message}`, 'error');
            }
        }

        async function createTestData() {
            try {
                showResult('正在创建测试数据...', 'info');
                
                if (!currentToken) {
                    showResult('请先登录', 'error');
                    return;
                }

                // 直接调用后端创建测试数据的接口（如果有的话）
                // 或者通过数据库操作创建
                
                const response = await fetch('http://localhost:8086/api/test/create-download-records', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(`测试数据创建成功: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    // 如果没有专门的接口，我们手动创建一些模拟数据
                    showResult('后端没有测试数据创建接口，请手动在数据库中添加测试记录', 'info');
                }
            } catch (error) {
                showResult(`创建测试数据失败: ${error.message}`, 'error');
            }
        }

        async function testAPI() {
            try {
                showResult('正在测试下载记录API...', 'info');
                
                if (!currentToken) {
                    showResult('请先登录', 'error');
                    return;
                }

                const response = await fetch('http://localhost:8086/api/download/records', {
                    headers: { 'Authorization': `Bearer ${currentToken}` }
                });

                const result = await response.json();
                
                showResult(`API测试结果:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`, 
                          response.ok ? 'success' : 'error');

                if (response.ok && result.success && result.records) {
                    window.testRecords = result.records;
                }
            } catch (error) {
                showResult(`API测试失败: ${error.message}`, 'error');
            }
        }

        async function testMainApp() {
            try {
                showResult('正在测试主应用下载记录功能...', 'info');
                
                // 检查主应用是否可用
                if (typeof window.app === 'undefined') {
                    showResult('主应用未加载，请在主页面中测试', 'error');
                    return;
                }

                // 调用主应用的下载记录加载方法
                await window.app.loadDownloadRecords();
                showResult('主应用下载记录功能测试完成，请查看主页面', 'success');
                
            } catch (error) {
                showResult(`主应用测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查登录状态
        window.onload = function() {
            if (currentToken) {
                showResult(`已有Token: ${currentToken.substring(0, 20)}...`, 'info');
            } else {
                showResult('未登录，请先点击登录按钮', 'info');
            }
        };
    </script>
</body>
</html>
