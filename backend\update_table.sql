-- 更新下载记录表结构
USE file_share_system;

-- 检查并添加 folder_id 字段
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'file_share_system' 
    AND TABLE_NAME = 'download_records' 
    AND COLUMN_NAME = 'folder_id'
);

-- 如果字段不存在则添加
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE download_records ADD COLUMN folder_id INT NULL COMMENT "文件夹ID（文件夹下载时使用）" AFTER file_id',
    'SELECT "folder_id字段已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修改 file_id 字段为可空
ALTER TABLE download_records 
MODIFY COLUMN file_id INT NULL COMMENT '文件ID（单文件/批量下载时使用）';

-- 添加索引（如果不存在）
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'file_share_system' 
    AND TABLE_NAME = 'download_records' 
    AND INDEX_NAME = 'idx_folder_download'
);

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_folder_download ON download_records (folder_id, downloaded_at)',
    'SELECT "索引已存在" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 更新现有记录的用户ID
UPDATE download_records 
SET user_id = (SELECT id FROM users WHERE username = 'fjj' LIMIT 1)
WHERE user_id IS NULL;

-- 显示更新后的表结构
DESCRIBE download_records;

-- 显示更新统计
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as records_with_user,
    COUNT(CASE WHEN file_id IS NOT NULL THEN 1 END) as file_downloads,
    COUNT(CASE WHEN folder_id IS NOT NULL THEN 1 END) as folder_downloads
FROM download_records;
