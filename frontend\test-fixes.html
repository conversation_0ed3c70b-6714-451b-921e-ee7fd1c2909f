<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试 - 文件分享系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .iframe-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 修复验证测试</h1>
        <p>本页面用于验证前端排版、在线用户统计和活动日志记录的修复情况。</p>
        
        <div class="test-section">
            <h3>🎨 1. 前端排版修复验证</h3>
            <p>检查主界面的布局是否正常显示</p>
            <button onclick="openMainPage()">打开主界面</button>
            <button onclick="testLayout()">测试布局</button>
            <div id="layoutTest" class="test-result">等待测试...</div>
            
            <div class="iframe-container">
                <iframe id="mainPageFrame" src="about:blank"></iframe>
            </div>
        </div>
        
        <div class="test-section">
            <h3>👥 2. 在线用户统计修复验证</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>系统信息API测试</h4>
                    <button onclick="testSystemInfo()">测试系统信息</button>
                    <div id="systemInfoResult">等待测试...</div>
                </div>
                <div class="test-card">
                    <h4>在线用户API测试</h4>
                    <button onclick="testOnlineUsers()">测试在线用户</button>
                    <div id="onlineUsersResult">等待测试...</div>
                </div>
                <div class="test-card">
                    <h4>统计信息API测试</h4>
                    <button onclick="testStatistics()">测试统计信息</button>
                    <div id="statisticsResult">等待测试...</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 3. 活动日志记录验证</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4>登录活动记录</h4>
                    <button onclick="testLoginActivity()">测试登录记录</button>
                    <div id="loginActivityResult">等待测试...</div>
                </div>
                <div class="test-card">
                    <h4>搜索活动记录</h4>
                    <button onclick="testSearchActivity()">测试搜索记录</button>
                    <div id="searchActivityResult">等待测试...</div>
                </div>
                <div class="test-card">
                    <h4>下载活动记录</h4>
                    <button onclick="testDownloadActivity()">测试下载记录</button>
                    <div id="downloadActivityResult">等待测试...</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔄 4. 完整流程测试</h3>
            <button onclick="runFullTest()">运行完整测试</button>
            <div id="fullTestProgress">等待测试...</div>
            <pre id="fullTestLog">测试日志将在这里显示...</pre>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script>
        const logs = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${message}`);
            const logElement = document.getElementById('fullTestLog');
            if (logElement) {
                logElement.textContent = logs.join('\n');
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        // 1. 前端排版测试
        function openMainPage() {
            const frame = document.getElementById('mainPageFrame');
            frame.src = 'index.html';
            log('已加载主界面到iframe中');
        }
        
        function testLayout() {
            const resultDiv = document.getElementById('layoutTest');
            
            // 检查CSS文件是否包含必要的样式
            fetch('css/style.css')
                .then(response => response.text())
                .then(css => {
                    const hasContentArea = css.includes('.content-area');
                    const hasBreadcrumb = css.includes('.breadcrumb');
                    const hasFileGrid = css.includes('.file-grid');
                    const hasSystemStatus = css.includes('.system-status');
                    
                    let result = '<div class="status-ok">✅ CSS样式检查:</div>';
                    result += `<ul>`;
                    result += `<li>${hasContentArea ? '✅' : '❌'} .content-area 样式</li>`;
                    result += `<li>${hasBreadcrumb ? '✅' : '❌'} .breadcrumb 样式</li>`;
                    result += `<li>${hasFileGrid ? '✅' : '❌'} .file-grid 样式</li>`;
                    result += `<li>${hasSystemStatus ? '✅' : '❌'} .system-status 样式</li>`;
                    result += `</ul>`;
                    
                    resultDiv.innerHTML = result;
                    log('布局样式检查完成');
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="status-error">❌ CSS检查失败: ${error.message}</div>`;
                    log(`布局测试失败: ${error.message}`);
                });
        }
        
        // 2. 在线用户统计测试
        async function testSystemInfo() {
            const resultDiv = document.getElementById('systemInfoResult');
            
            try {
                const response = await SystemAPI.getSystemInfo();
                
                resultDiv.innerHTML = `
                    <div class="status-ok">✅ 系统信息获取成功</div>
                    <ul>
                        <li>版本: ${response.version}</li>
                        <li>在线用户: ${response.online_users || 0}</li>
                        <li>运行状态: ${response.status}</li>
                        <li>运行时间: ${Math.floor(response.uptime || 0)}秒</li>
                    </ul>
                `;
                log(`系统信息测试成功，在线用户: ${response.online_users || 0}`);
            } catch (error) {
                resultDiv.innerHTML = `<div class="status-error">❌ 测试失败: ${error.message}</div>`;
                log(`系统信息测试失败: ${error.message}`);
            }
        }
        
        async function testOnlineUsers() {
            const resultDiv = document.getElementById('onlineUsersResult');
            
            try {
                const response = await SystemAPI.getOnlineUsers();
                const users = response.users || [];
                
                resultDiv.innerHTML = `
                    <div class="status-ok">✅ 在线用户获取成功</div>
                    <p>在线用户数: ${users.length}</p>
                    ${users.length > 0 ? `
                        <ul>
                            ${users.map(user => `
                                <li>${user.username} (${user.ip_address || '未知IP'})</li>
                            `).join('')}
                        </ul>
                    ` : '<p>当前无在线用户</p>'}
                `;
                log(`在线用户测试成功，用户数: ${users.length}`);
            } catch (error) {
                resultDiv.innerHTML = `<div class="status-warning">⚠️ 需要管理员权限: ${error.message}</div>`;
                log(`在线用户测试: ${error.message}`);
            }
        }
        
        async function testStatistics() {
            const resultDiv = document.getElementById('statisticsResult');
            
            try {
                const response = await SystemAPI.getStatistics();
                
                resultDiv.innerHTML = `
                    <div class="status-ok">✅ 统计信息获取成功</div>
                    <ul>
                        <li>总用户数: ${response.total_users || 0}</li>
                        <li>在线用户: ${response.online_users || 0}</li>
                        <li>活跃用户: ${response.active_users || 0}</li>
                        <li>总活动数: ${response.total_activities || 0}</li>
                        <li>24小时活动: ${response.recent_activities_24h || 0}</li>
                    </ul>
                `;
                log(`统计信息测试成功`);
            } catch (error) {
                resultDiv.innerHTML = `<div class="status-warning">⚠️ 需要管理员权限: ${error.message}</div>`;
                log(`统计信息测试: ${error.message}`);
            }
        }
        
        // 3. 活动日志记录测试
        async function testLoginActivity() {
            const resultDiv = document.getElementById('loginActivityResult');
            
            try {
                // 模拟登录测试
                const response = await fetch('http://localhost:8086/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'test', password: 'test123' })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="status-ok">✅ 登录活动记录测试成功</div>
                        <p>登录成功，活动应已记录到监控服务</p>
                    `;
                    log('登录活动记录测试成功');
                } else {
                    resultDiv.innerHTML = `<div class="status-error">❌ 登录失败: ${result.error}</div>`;
                    log(`登录活动测试失败: ${result.error}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="status-error">❌ 测试失败: ${error.message}</div>`;
                log(`登录活动测试异常: ${error.message}`);
            }
        }
        
        async function testSearchActivity() {
            const resultDiv = document.getElementById('searchActivityResult');
            
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    resultDiv.innerHTML = '<div class="status-warning">⚠️ 需要先登录</div>';
                    return;
                }
                
                const auth = JSON.parse(authData);
                const response = await fetch('http://localhost:8086/api/search', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${auth.token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query: '测试搜索', type: 'text' })
                });
                
                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="status-ok">✅ 搜索活动记录测试成功</div>
                    <p>搜索请求已发送，活动应已记录</p>
                `;
                log('搜索活动记录测试成功');
            } catch (error) {
                resultDiv.innerHTML = `<div class="status-error">❌ 测试失败: ${error.message}</div>`;
                log(`搜索活动测试失败: ${error.message}`);
            }
        }
        
        async function testDownloadActivity() {
            const resultDiv = document.getElementById('downloadActivityResult');
            
            resultDiv.innerHTML = `
                <div class="status-warning">⚠️ 下载活动测试</div>
                <p>下载功能需要实际文件，此处仅验证API接口存在</p>
                <p>实际下载时会自动记录活动日志</p>
            `;
            log('下载活动记录功能已集成到API中');
        }
        
        // 4. 完整流程测试
        async function runFullTest() {
            const progressDiv = document.getElementById('fullTestProgress');
            
            progressDiv.innerHTML = '<div class="status-warning">🔄 正在运行完整测试...</div>';
            logs.length = 0; // 清空日志
            
            log('开始完整流程测试');
            
            // 测试1: 布局
            log('1. 测试前端布局...');
            testLayout();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试2: 系统信息
            log('2. 测试系统信息...');
            await testSystemInfo();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试3: 在线用户
            log('3. 测试在线用户...');
            await testOnlineUsers();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试4: 统计信息
            log('4. 测试统计信息...');
            await testStatistics();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试5: 活动记录
            log('5. 测试活动记录...');
            await testSearchActivity();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            progressDiv.innerHTML = '<div class="status-ok">✅ 完整测试完成</div>';
            log('完整流程测试完成！');
        }
        
        // 页面加载时自动运行基础测试
        window.addEventListener('load', () => {
            log('测试页面加载完成');
            openMainPage();
            setTimeout(() => {
                testSystemInfo();
            }, 2000);
        });
    </script>
</body>
</html>
