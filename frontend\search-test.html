<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #dbeafe;
            --primary-color-alpha: rgba(37, 99, 235, 0.1);
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e2e8f0;
            --gray-500: #6b7280;
            --gray-800: #1f2937;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --spacing-4: 1rem;
            --spacing-6: 1.5rem;
            --transition: all 0.2s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--gray-800);
            line-height: 1.6;
            padding: 2rem;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .test-header h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .test-header p {
            font-size: 1.1rem;
            color: var(--gray-500);
        }

        .search-demo {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .search-demo h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 搜索框样式 */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 28px;
            transition: all 0.2s ease;
            overflow: hidden;
            height: 44px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin-bottom: 1rem;
        }

        .search-container:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .search-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-color-alpha), 0 4px 16px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
        }

        .search-container .fa-search {
            position: absolute;
            left: 18px;
            color: var(--gray-500);
            font-size: 16px;
            z-index: 2;
            transition: color 0.2s ease;
        }

        .search-container:focus-within .fa-search {
            color: var(--primary-color);
        }

        .search-container input {
            width: 100%;
            padding: 12px 56px 12px 48px;
            font-size: 15px;
            font-weight: 400;
            border: none;
            background: transparent;
            color: var(--gray-800);
            outline: none;
            height: 20px;
            line-height: 20px;
        }

        .search-container input::placeholder {
            color: var(--gray-500);
            font-weight: 400;
        }

        .search-type-indicator {
            position: absolute;
            right: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            color: white;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .search-container:focus-within .search-type-indicator {
            transform: scale(1.05);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
        }

        .search-type-indicator .fa-image {
            font-size: 12px;
        }

        .test-cases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .test-case {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .test-case h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .test-case .description {
            color: var(--gray-500);
            margin-bottom: 1rem;
        }

        .test-case .example {
            background: var(--bg-secondary);
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            border-left: 4px solid var(--primary-color);
        }

        .test-case .expected {
            margin-top: 1rem;
            padding: 1rem;
            background: #f0f9ff;
            border-radius: 6px;
            border-left: 4px solid var(--success-color);
        }

        .test-case .expected strong {
            color: var(--success-color);
        }

        .demo-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            background: transparent;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .demo-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .status-panel {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .status-panel h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-value {
            font-weight: 600;
        }

        .status-value.success {
            color: var(--success-color);
        }

        .status-value.warning {
            color: var(--warning-color);
        }

        .status-value.error {
            color: var(--error-color);
        }

        .log-panel {
            background: #1a1a1a;
            color: #00ff00;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 1rem;
        }

        .highlight-box {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .highlight-box h4 {
            color: #0369a1;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-search"></i> 搜索功能测试</h1>
            <p>测试图片文件搜索功能，确保只显示图片文件，不显示文件夹</p>
        </div>

        <div class="search-demo">
            <h2><i class="fas fa-vial"></i> 搜索演示</h2>
            <div class="search-container">
                <i class="fas fa-search"></i>
                <input type="text" id="search-input" placeholder="搜索图片文件...">
                <div class="search-type-indicator">
                    <i class="fas fa-image"></i>
                </div>
            </div>
            
            <div class="highlight-box">
                <h4>🎯 搜索逻辑说明</h4>
                <ul>
                    <li><strong>只搜索图片文件</strong>：JPG, PNG, PSD, TIF, AI, EPS等格式</li>
                    <li><strong>排除文件夹</strong>：搜索结果不包含任何文件夹</li>
                    <li><strong>文件名匹配</strong>：搜索关键词在文件名中的包含匹配</li>
                    <li><strong>实时搜索</strong>：输入时自动搜索，无需手动触发</li>
                </ul>
            </div>
        </div>

        <div class="test-cases">
            <div class="test-case">
                <h3><i class="fas fa-keyboard"></i> 基本搜索测试</h3>
                <div class="description">测试基本的文件名搜索功能</div>
                <div class="example">搜索: "3"</div>
                <div class="expected">
                    <strong>期望结果:</strong> 只显示文件名包含"3"的图片文件，不显示文件夹
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testSearch('3')">测试搜索"3"</button>
                    <button class="demo-btn" onclick="testSearch('image')">测试搜索"image"</button>
                </div>
            </div>

            <div class="test-case">
                <h3><i class="fas fa-filter"></i> 文件类型过滤</h3>
                <div class="description">确保只显示图片格式文件</div>
                <div class="example">允许格式: jpg, png, psd, tif, ai, eps等</div>
                <div class="expected">
                    <strong>期望结果:</strong> 文档、视频等非图片文件不会出现在搜索结果中
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testSearch('test')">测试混合文件</button>
                </div>
            </div>

            <div class="test-case">
                <h3><i class="fas fa-folder"></i> 文件夹排除测试</h3>
                <div class="description">确保搜索结果不包含文件夹</div>
                <div class="example">即使文件夹名包含搜索关键词</div>
                <div class="expected">
                    <strong>期望结果:</strong> 任何文件夹都不会出现在搜索结果中
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testSearch('folder')">测试文件夹排除</button>
                </div>
            </div>

            <div class="test-case">
                <h3><i class="fas fa-times"></i> 空结果测试</h3>
                <div class="description">测试没有匹配结果的情况</div>
                <div class="example">搜索不存在的关键词</div>
                <div class="expected">
                    <strong>期望结果:</strong> 显示"未找到"提示，清空文件列表
                </div>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="testSearch('nonexistent')">测试空结果</button>
                    <button class="demo-btn" onclick="clearSearch()">清空搜索</button>
                </div>
            </div>
        </div>

        <div class="status-panel">
            <h3><i class="fas fa-chart-line"></i> 测试状态</h3>
            <div class="status-item">
                <span>搜索功能状态:</span>
                <span class="status-value" id="search-status">待测试</span>
            </div>
            <div class="status-item">
                <span>最后搜索关键词:</span>
                <span class="status-value" id="last-query">-</span>
            </div>
            <div class="status-item">
                <span>搜索结果数量:</span>
                <span class="status-value" id="result-count">0</span>
            </div>
            <div class="status-item">
                <span>文件夹过滤:</span>
                <span class="status-value success" id="folder-filter">已启用</span>
            </div>
            <div class="status-item">
                <span>图片格式过滤:</span>
                <span class="status-value success" id="image-filter">已启用</span>
            </div>
            
            <div class="log-panel" id="log-panel">
                <div>搜索测试日志:</div>
                <div>等待测试开始...</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟搜索功能
        function testSearch(query) {
            const searchInput = document.getElementById('search-input');
            const logPanel = document.getElementById('log-panel');
            const searchStatus = document.getElementById('search-status');
            const lastQuery = document.getElementById('last-query');
            const resultCount = document.getElementById('result-count');
            
            // 更新输入框
            searchInput.value = query;
            searchInput.focus();
            
            // 更新状态
            searchStatus.textContent = '搜索中...';
            searchStatus.className = 'status-value warning';
            lastQuery.textContent = query;
            
            // 添加日志
            addLog(`开始搜索: "${query}"`);
            addLog('应用图片文件过滤器...');
            addLog('排除文件夹...');
            
            // 模拟搜索延迟
            setTimeout(() => {
                const mockResultCount = Math.floor(Math.random() * 10);
                resultCount.textContent = mockResultCount;
                
                if (mockResultCount > 0) {
                    searchStatus.textContent = '搜索完成';
                    searchStatus.className = 'status-value success';
                    addLog(`✅ 找到 ${mockResultCount} 个图片文件`);
                    addLog('✅ 已排除所有文件夹');
                    addLog('✅ 已过滤非图片文件');
                } else {
                    searchStatus.textContent = '无结果';
                    searchStatus.className = 'status-value warning';
                    addLog(`⚠️ 未找到包含"${query}"的图片文件`);
                }
            }, 1000);
        }
        
        function clearSearch() {
            const searchInput = document.getElementById('search-input');
            const searchStatus = document.getElementById('search-status');
            const lastQuery = document.getElementById('last-query');
            const resultCount = document.getElementById('result-count');
            
            searchInput.value = '';
            searchInput.blur();
            
            searchStatus.textContent = '已清空';
            searchStatus.className = 'status-value';
            lastQuery.textContent = '-';
            resultCount.textContent = '0';
            
            addLog('🔄 搜索已清空');
            addLog('📁 恢复原始文件列表');
        }
        
        function addLog(message) {
            const logPanel = document.getElementById('log-panel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }
        
        // 搜索框交互
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            
            searchInput.addEventListener('input', function() {
                if (this.value.length > 0) {
                    addLog(`输入搜索关键词: "${this.value}"`);
                }
            });
            
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    testSearch(this.value);
                }
                if (e.key === 'Escape') {
                    clearSearch();
                }
            });
            
            addLog('🚀 搜索测试页面已加载');
            addLog('📋 可以开始测试搜索功能');
        });
    </script>
</body>
</html>
