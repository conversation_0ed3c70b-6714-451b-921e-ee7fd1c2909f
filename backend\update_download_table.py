#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新下载记录表结构脚本
添加 folder_id 字段以支持文件夹下载记录
"""

import pymysql
import sys

def update_download_records_table():
    """更新下载记录表，添加 folder_id 字段"""
    try:
        print("🔄 连接数据库...")
        
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查 folder_id 字段是否已存在
            cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'file_share_system' 
                AND TABLE_NAME = 'download_records' 
                AND COLUMN_NAME = 'folder_id'
            """)
            
            result = cursor.fetchone()
            
            if result:
                print("✅ folder_id 字段已存在，无需更新")
                return True
            
            print("📝 添加 folder_id 字段...")
            
            # 添加 folder_id 字段
            cursor.execute("""
                ALTER TABLE download_records 
                ADD COLUMN folder_id INT NULL COMMENT '文件夹ID（文件夹下载时使用）' 
                AFTER file_id
            """)
            
            print("✅ folder_id 字段添加成功")
            
            # 修改 file_id 字段为可空
            cursor.execute("""
                ALTER TABLE download_records 
                MODIFY COLUMN file_id INT NULL COMMENT '文件ID（单文件/批量下载时使用）'
            """)
            
            print("✅ file_id 字段修改为可空")
            
            # 添加索引
            cursor.execute("""
                CREATE INDEX idx_folder_download 
                ON download_records (folder_id, downloaded_at)
            """)
            
            print("✅ 添加文件夹下载索引")
            
            # 提交更改
            connection.commit()
            print("✅ 数据库表结构更新完成")
            
            # 验证表结构
            cursor.execute("""
                DESCRIBE download_records
            """)
            
            columns = cursor.fetchall()
            print("\n📋 当前表结构:")
            for column in columns:
                print(f"   {column[0]} - {column[1]} - {column[2]} - {column[5]}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def update_user_records():
    """更新现有记录的用户ID"""
    try:
        print("\n🔄 更新现有记录的用户ID...")
        
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 获取用户fjj的ID
            cursor.execute("SELECT id FROM users WHERE username = 'fjj'")
            user_result = cursor.fetchone()
            
            if not user_result:
                print("⚠️ 找不到用户fjj，跳过用户ID更新")
                return True
            
            user_id = user_result[0]
            print(f"✅ 找到用户fjj，ID: {user_id}")
            
            # 更新所有没有用户ID的下载记录
            cursor.execute("""
                UPDATE download_records 
                SET user_id = %s 
                WHERE user_id IS NULL
            """, (user_id,))
            
            updated_count = cursor.rowcount
            print(f"✅ 更新了 {updated_count} 条下载记录的用户ID")
            
            connection.commit()
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新用户记录失败: {e}")
        return False

def main():
    """主函数"""
    print("🗄️ 下载记录表结构更新工具")
    print("=" * 50)
    
    # 步骤1: 更新表结构
    if not update_download_records_table():
        print("❌ 表结构更新失败")
        sys.exit(1)
    
    # 步骤2: 更新用户记录
    if not update_user_records():
        print("❌ 用户记录更新失败")
        sys.exit(1)
    
    print("\n🎉 所有更新完成！")
    print("✅ 下载记录表现在支持文件夹下载功能")
    print("✅ 现有记录已关联到用户账户")
    print("\n💡 现在可以重启服务器测试下载记录功能")

if __name__ == "__main__":
    main()
