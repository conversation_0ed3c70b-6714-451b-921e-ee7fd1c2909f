#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_api():
    """快速测试下载记录API"""
    
    # 1. 测试登录
    print("🔐 测试登录...")
    login_url = "http://localhost:8086/api/auth/login"
    login_data = {
        "username": "fjj",
        "password": "123456"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"登录结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                token = result.get('token')
                print(f"✅ 登录成功，Token: {token[:20]}...")
                
                # 2. 测试下载记录API
                print("\n📋 测试下载记录API...")
                records_url = "http://localhost:8086/api/download/records"
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                records_response = requests.get(records_url, headers=headers)
                print(f"下载记录API响应状态: {records_response.status_code}")
                
                if records_response.status_code == 200:
                    records_result = records_response.json()
                    print(f"下载记录结果: {json.dumps(records_result, indent=2, ensure_ascii=False)}")
                else:
                    print(f"下载记录API失败: {records_response.text}")
                
                # 3. 测试创建测试记录API
                print("\n📝 测试创建测试记录API...")
                create_url = "http://localhost:8086/api/test/create-download-records"
                
                create_response = requests.post(create_url, headers=headers)
                print(f"创建测试记录API响应状态: {create_response.status_code}")
                
                if create_response.status_code == 200:
                    create_result = create_response.json()
                    print(f"创建测试记录结果: {json.dumps(create_result, indent=2, ensure_ascii=False)}")
                else:
                    print(f"创建测试记录API失败: {create_response.text}")
                
            else:
                print(f"❌ 登录失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 登录请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_api()
