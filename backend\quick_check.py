#!/usr/bin/env python3
"""
快速检查收藏数据
"""

import mysql.connector

def quick_check():
    """快速检查收藏数据"""
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system'
        )
        
        cursor = connection.cursor(dictionary=True)
        
        print("=== 快速检查收藏数据 ===")
        
        # 1. 查找test1用户
        cursor.execute("SELECT id, username FROM users WHERE username = 'test1'")
        test1_user = cursor.fetchone()
        
        if not test1_user:
            print("❌ 未找到test1用户")
            return
        
        user_id = test1_user['id']
        print(f"✅ 找到test1用户: ID={user_id}")
        
        # 2. 检查收藏记录
        cursor.execute("""
            SELECT COUNT(*) as total 
            FROM user_favorites 
            WHERE user_id = %s
        """, (user_id,))
        total_favorites = cursor.fetchone()['total']
        print(f"总收藏记录数: {total_favorites}")
        
        # 3. 检查活跃收藏记录
        cursor.execute("""
            SELECT COUNT(*) as active 
            FROM user_favorites 
            WHERE user_id = %s AND is_active = 1
        """, (user_id,))
        active_favorites = cursor.fetchone()['active']
        print(f"活跃收藏记录数: {active_favorites}")
        
        # 4. 检查收藏的文件
        cursor.execute("""
            SELECT 
                uf.id as favorite_id,
                uf.file_id,
                uf.is_active,
                sf.filename,
                sf.is_image
            FROM user_favorites uf
            LEFT JOIN shared_files sf ON uf.file_id = sf.id
            WHERE uf.user_id = %s
            ORDER BY uf.favorited_at DESC
        """, (user_id,))
        
        favorites = cursor.fetchall()
        print(f"\n收藏详情:")
        for fav in favorites:
            print(f"  收藏ID: {fav['favorite_id']}, 文件ID: {fav['file_id']}, 活跃: {fav['is_active']}")
            if fav['filename']:
                print(f"    文件名: {fav['filename']}, 是否图片: {fav['is_image']}")
            else:
                print(f"    ⚠️ 文件不存在")
        
        # 5. 如果有非活跃记录，激活它们
        if active_favorites < total_favorites:
            print(f"\n发现 {total_favorites - active_favorites} 个非活跃记藏，正在激活...")
            cursor.execute("""
                UPDATE user_favorites 
                SET is_active = 1 
                WHERE user_id = %s AND is_active = 0
            """, (user_id,))
            connection.commit()
            print("✅ 已激活所有收藏记录")
        
        # 6. 最终统计
        cursor.execute("""
            SELECT COUNT(*) as final_count 
            FROM user_favorites uf
            JOIN shared_files sf ON uf.file_id = sf.id
            WHERE uf.user_id = %s AND uf.is_active = 1
        """, (user_id,))
        final_count = cursor.fetchone()['final_count']
        print(f"\n✅ 最终可用收藏数: {final_count}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    quick_check()
