<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载记录和密码申请系统测试</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        #demo-area {
            min-height: 500px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔽 下载记录和密码申请系统测试</h1>
            <p>测试完整的下载记录查看和密码申请功能</p>
        </div>

        <div class="test-section">
            <h3>1. 用户认证</h3>
            <button onclick="testLogin()">登录测试用户</button>
            <button onclick="checkToken()">检查Token状态</button>
            <div id="authResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 下载记录功能测试</h3>
            <button onclick="loadDownloadRecords()">加载下载记录</button>
            <button onclick="testDownloadAPI()">测试下载API</button>
            <button onclick="simulateDownload()">模拟文件下载</button>
            <div id="downloadResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 密码申请功能测试</h3>
            <button onclick="loadPasswordRequests()">加载密码申请记录</button>
            <button onclick="testPasswordRequest()">测试密码申请</button>
            <button onclick="simulatePasswordRequest()">模拟密码申请</button>
            <div id="passwordResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 界面演示</h3>
            <button onclick="showDownloadRecordsDemo()">显示下载记录界面</button>
            <button onclick="showPasswordRequestDemo()">显示密码申请界面</button>
            <button onclick="clearDemo()">清空演示区域</button>
            <div id="demo-area">
                <p style="text-align: center; color: #666; margin-top: 200px;">
                    <i class="fas fa-mouse-pointer" style="font-size: 48px; margin-bottom: 20px; opacity: 0.3;"></i><br>
                    点击上方按钮查看界面演示
                </p>
            </div>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/components.js"></script>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        let currentToken = localStorage.getItem('token');

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'fjj',
                        password: '123456'
                    })
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    currentToken = result.token;
                    localStorage.setItem('token', currentToken);
                    showResult('authResult', `登录成功！\nToken: ${currentToken}\n用户信息: ${JSON.stringify(result.user, null, 2)}`, 'success');
                } else {
                    showResult('authResult', `登录失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('authResult', `登录请求失败: ${error.message}`, 'error');
            }
        }

        function checkToken() {
            const token = localStorage.getItem('token');
            if (token) {
                showResult('authResult', `当前Token: ${token}`, 'info');
            } else {
                showResult('authResult', '没有找到Token，请先登录', 'error');
            }
        }

        async function loadDownloadRecords() {
            if (!currentToken) {
                showResult('downloadResult', '请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/download/records`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                const result = await response.json();
                showResult('downloadResult', `下载记录API响应:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`, response.ok ? 'success' : 'error');

                if (response.ok && result.records) {
                    window.testDownloadRecords = result.records;
                }
            } catch (error) {
                showResult('downloadResult', `获取下载记录失败: ${error.message}`, 'error');
            }
        }

        async function loadPasswordRequests() {
            if (!currentToken) {
                showResult('passwordResult', '请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/download/password-requests`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                const result = await response.json();
                showResult('passwordResult', `密码申请记录API响应:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`, response.ok ? 'success' : 'error');

                if (response.ok && result.requests) {
                    window.testPasswordRequests = result.requests;
                }
            } catch (error) {
                showResult('passwordResult', `获取密码申请记录失败: ${error.message}`, 'error');
            }
        }

        async function testPasswordRequest() {
            if (!currentToken) {
                showResult('passwordResult', '请先登录获取Token', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/download/password/request`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    },
                    body: JSON.stringify({
                        file_id: 1,
                        reason: '测试密码申请功能'
                    })
                });

                const result = await response.json();
                showResult('passwordResult', `密码申请API响应:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`, response.ok ? 'success' : 'error');
            } catch (error) {
                showResult('passwordResult', `密码申请失败: ${error.message}`, 'error');
            }
        }

        function simulateDownload() {
            showResult('downloadResult', '模拟下载功能：这里会触发实际的文件下载流程', 'info');
        }

        function simulatePasswordRequest() {
            showResult('passwordResult', '模拟密码申请：这里会显示密码申请对话框', 'info');
        }

        function testDownloadAPI() {
            showResult('downloadResult', '测试下载API：检查下载接口的可用性', 'info');
        }

        function showDownloadRecordsDemo() {
            const demoArea = document.getElementById('demo-area');

            // 模拟下载记录数据
            const mockRecords = [
                {
                    id: 1,
                    file_id: 101,
                    filename: '设计图片_20241210.zip',
                    file_size: 15728640,
                    download_time: new Date().toISOString(),
                    download_type: 'single',
                    is_encrypted: false,
                    download_count: 1
                },
                {
                    id: 2,
                    file_id: 102,
                    filename: '产品照片合集.zip',
                    file_size: 52428800,
                    download_time: new Date(Date.now() - 86400000).toISOString(),
                    download_type: 'batch',
                    is_encrypted: true,
                    download_count: 3
                }
            ];

            // 创建下载记录界面
            const html = `
                <div class="download-records-tabs">
                    <div class="tab-header">
                        <button class="tab-btn active" data-tab="downloads">
                            <i class="fas fa-download"></i>
                            下载记录
                        </button>
                        <button class="tab-btn" data-tab="password-requests">
                            <i class="fas fa-key"></i>
                            密码申请记录
                        </button>
                    </div>
                    <div class="tab-content">
                        <div class="tab-panel active" id="downloads-panel">
                            <div class="download-records-container">
                                <div class="download-group">
                                    <h3 class="download-date">今天</h3>
                                    <div class="download-items">
                                        ${renderMockDownloadRecord(mockRecords[0])}
                                    </div>
                                </div>
                                <div class="download-group">
                                    <h3 class="download-date">昨天</h3>
                                    <div class="download-items">
                                        ${renderMockDownloadRecord(mockRecords[1])}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-panel" id="password-requests-panel">
                            <div class="loading-state">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            demoArea.innerHTML = html;

            // 添加标签页切换功能
            const tabBtns = demoArea.querySelectorAll('.tab-btn');
            const tabPanels = demoArea.querySelectorAll('.tab-panel');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const tabName = e.target.closest('.tab-btn').dataset.tab;

                    tabBtns.forEach(b => b.classList.remove('active'));
                    e.target.closest('.tab-btn').classList.add('active');

                    tabPanels.forEach(p => p.classList.remove('active'));
                    const targetPanel = demoArea.querySelector(`#${tabName}-panel`);
                    if (targetPanel) {
                        targetPanel.classList.add('active');
                    }

                    if (tabName === 'password-requests') {
                        setTimeout(() => showPasswordRequestDemo(true), 500);
                    }
                });
            });
        }

        function renderMockDownloadRecord(record) {
            const isEncrypted = record.is_encrypted;
            const downloadCount = record.download_count || 0;
            const maxDownloads = 3;

            return `
                <div class="download-record-card" data-record-id="${record.id}">
                    <div class="record-header">
                        <div class="record-icon">
                            <i class="fas fa-file-archive"></i>
                            ${isEncrypted ? '<i class="fas fa-lock encrypted-badge"></i>' : ''}
                        </div>
                        <div class="record-info">
                            <h4 class="record-filename">${record.filename}</h4>
                            <div class="record-meta">
                                <span class="record-size">${formatFileSize(record.file_size)}</span>
                                <span class="record-time">${formatTime(record.download_time)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="record-stats">
                        <div class="download-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${(downloadCount / maxDownloads) * 100}%"></div>
                            </div>
                            <span class="progress-text">${downloadCount}/${maxDownloads} 次下载</span>
                        </div>

                        ${isEncrypted ? `
                            <div class="encryption-status">
                                <i class="fas fa-shield-alt"></i>
                                <span>已加密</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="record-actions">
                        ${isEncrypted ? `
                            <button class="btn btn-primary btn-sm" onclick="showPasswordDialog('${record.filename}')">
                                <i class="fas fa-key"></i>
                                申请密码
                            </button>
                        ` : `
                            <button class="btn btn-secondary btn-sm" onclick="alert('重新下载: ${record.filename}')">
                                <i class="fas fa-download"></i>
                                重新下载
                            </button>
                        `}

                        <button class="btn btn-outline btn-sm" onclick="alert('查看详情: ${record.filename}')">
                            <i class="fas fa-info-circle"></i>
                            详情
                        </button>
                    </div>
                </div>
            `;
        }

        function showPasswordRequestDemo(inTab = false) {
            const targetElement = inTab ?
                document.getElementById('password-requests-panel') :
                document.getElementById('demo-area');

            // 模拟密码申请记录数据
            const mockRequests = [
                {
                    id: 1,
                    file_id: 102,
                    filename: '产品照片合集.zip',
                    status: 'approved',
                    request_reason: '需要查看产品照片进行设计工作',
                    password_provided: 'Abc123!@#',
                    created_at: new Date(Date.now() - 3600000).toISOString(),
                    approved_at: new Date(Date.now() - 1800000).toISOString(),
                    is_expired: false
                },
                {
                    id: 2,
                    file_id: 103,
                    filename: '技术文档.zip',
                    status: 'pending',
                    request_reason: '项目开发需要参考技术文档',
                    created_at: new Date(Date.now() - 1800000).toISOString(),
                    is_expired: false
                }
            ];

            const html = inTab ? '' : `
                <div class="password-requests-container">
                    <div class="request-group">
                        <h3 class="request-status-header approved">
                            <i class="fas fa-check-circle"></i>
                            已批准 (1)
                        </h3>
                        <div class="request-items">
                            ${renderMockPasswordRequest(mockRequests[0])}
                        </div>
                    </div>
                    <div class="request-group">
                        <h3 class="request-status-header pending">
                            <i class="fas fa-clock"></i>
                            待审批 (1)
                        </h3>
                        <div class="request-items">
                            ${renderMockPasswordRequest(mockRequests[1])}
                        </div>
                    </div>
                </div>
            `;

            if (inTab) {
                targetElement.innerHTML = `
                    <div class="password-requests-container">
                        <div class="request-group">
                            <h3 class="request-status-header approved">
                                <i class="fas fa-check-circle"></i>
                                已批准 (1)
                            </h3>
                            <div class="request-items">
                                ${renderMockPasswordRequest(mockRequests[0])}
                            </div>
                        </div>
                        <div class="request-group">
                            <h3 class="request-status-header pending">
                                <i class="fas fa-clock"></i>
                                待审批 (1)
                            </h3>
                            <div class="request-items">
                                ${renderMockPasswordRequest(mockRequests[1])}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                targetElement.innerHTML = html;
            }
        }

        function renderMockPasswordRequest(request) {
            const hasPassword = request.password_provided && request.status === 'approved';

            return `
                <div class="password-request-card ${request.status}" data-request-id="${request.id}">
                    <div class="request-header">
                        <div class="request-icon">
                            <i class="fas fa-file-archive"></i>
                        </div>
                        <div class="request-info">
                            <h4 class="request-filename">${request.filename}</h4>
                            <div class="request-meta">
                                <span class="request-time">申请时间: ${formatTime(request.created_at)}</span>
                                ${request.approved_at ? `<span class="approval-time">处理时间: ${formatTime(request.approved_at)}</span>` : ''}
                            </div>
                        </div>
                    </div>

                    <div class="request-content">
                        ${request.request_reason ? `
                            <div class="request-reason">
                                <strong>申请原因:</strong> ${request.request_reason}
                            </div>
                        ` : ''}
                    </div>

                    <div class="request-actions">
                        ${hasPassword ? `
                            <button class="btn btn-primary btn-sm" onclick="showPasswordDialog('${request.filename}', '${request.password_provided}')">
                                <i class="fas fa-eye"></i>
                                查看密码
                            </button>
                        ` : ''}

                        ${request.status === 'pending' ? `
                            <span class="pending-badge">
                                <i class="fas fa-hourglass-half"></i>
                                等待审批
                            </span>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function showPasswordDialog(filename, password = 'Abc123!@#') {
            const dialogHtml = `
                <div class="password-display-dialog">
                    <div class="dialog-header">
                        <h3>解压密码</h3>
                        <button class="close-btn" onclick="this.closest('.dialog-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="dialog-content">
                        <div class="password-display">
                            <label>文件: ${filename}</label>
                            <div class="password-field">
                                <input type="text" class="password-input" value="${password}" readonly>
                                <button class="copy-btn" onclick="copyToClipboard('${password}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="password-note">
                            <i class="fas fa-info-circle"></i>
                            <span>请妥善保管此密码，用于解压下载的文件</span>
                        </div>
                    </div>
                    <div class="dialog-actions">
                        <button class="btn btn-primary" onclick="this.closest('.dialog-overlay').remove()">
                            确定
                        </button>
                    </div>
                </div>
            `;

            const overlay = document.createElement('div');
            overlay.className = 'dialog-overlay';
            overlay.innerHTML = dialogHtml;
            document.body.appendChild(overlay);

            const passwordInput = overlay.querySelector('.password-input');
            if (passwordInput) {
                passwordInput.select();
            }
        }

        function clearDemo() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <p style="text-align: center; color: #666; margin-top: 200px;">
                    <i class="fas fa-mouse-pointer" style="font-size: 48px; margin-bottom: 20px; opacity: 0.3;"></i><br>
                    点击上方按钮查看界面演示
                </p>
            `;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatTime(timeString) {
            const date = new Date(timeString);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        async function copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                alert('密码已复制到剪贴板');
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败，请手动复制');
            }
        }

        // 页面加载时检查Token
        window.onload = function() {
            checkToken();
        };
    </script>
</body>
</html>
