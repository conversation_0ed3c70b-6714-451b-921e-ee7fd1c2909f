#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索服务 - 双搜索引擎
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import threading
import time

from models.file_share import SharedFile, SharedFolder
from utils.logger import setup_logger

# 可选导入，如果包不存在则使用简化版本
try:
    import cv2
    import numpy as np
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False

try:
    from whoosh.index import create_index, open_dir, exists_in
    from whoosh.fields import Schema, TEXT, ID, DATETIME, NUMERIC
    from whoosh.qparser import QueryParser
    from whoosh.query import Every
    HAS_WHOOSH = True
except ImportError:
    HAS_WHOOSH = False

class TextSearchEngine:
    """文本搜索引擎 - 类似Everything的快速搜索"""

    def __init__(self, index_dir: str):
        self.index_dir = Path(index_dir)
        self.index_dir.mkdir(parents=True, exist_ok=True)
        self.logger = setup_logger("TextSearchEngine")

        if HAS_WHOOSH:
            # 定义索引模式
            self.schema = Schema(
                id=ID(stored=True, unique=True),
                filename=TEXT(stored=True),
                path=TEXT(stored=True),
                content=TEXT(stored=True),
                extension=TEXT(stored=True),
                size=NUMERIC(stored=True),
                modified=DATETIME(stored=True),
                folder_id=NUMERIC(stored=True)
            )
            self.index = None
            self.initialize_index()
        else:
            # 简化版本：使用内存存储
            self.file_index = {}
            self.logger.warning("Whoosh未安装，使用简化搜索引擎")
    
    def initialize_index(self):
        """初始化搜索索引"""
        try:
            if exists_in(str(self.index_dir)):
                self.index = open_dir(str(self.index_dir))
            else:
                self.index = create_index(self.schema, str(self.index_dir))
            
            self.logger.info("文本搜索引擎初始化成功")
        except Exception as e:
            self.logger.error(f"文本搜索引擎初始化失败: {e}")
    
    def add_file_to_index(self, file_record: SharedFile):
        """添加文件到索引"""
        try:
            writer = self.index.writer()
            
            writer.add_document(
                id=str(file_record.id),
                filename=file_record.filename,
                path=file_record.relative_path,
                extension=file_record.extension or "",
                size=file_record.file_size,
                modified=file_record.file_modified,
                folder_id=file_record.folder_id
            )
            
            writer.commit()
            
        except Exception as e:
            self.logger.error(f"添加文件到索引失败: {e}")
    
    def remove_file_from_index(self, file_id: int):
        """从索引中移除文件"""
        try:
            writer = self.index.writer()
            writer.delete_by_term('id', str(file_id))
            writer.commit()
            
        except Exception as e:
            self.logger.error(f"从索引移除文件失败: {e}")
    
    def search(self, query: str, limit: int = 100) -> List[Dict[str, Any]]:
        """搜索文件"""
        try:
            with self.index.searcher() as searcher:
                # 创建查询解析器
                parser = QueryParser("filename", self.index.schema)
                
                # 如果查询为空，返回所有文件
                if not query.strip():
                    query_obj = Every()
                else:
                    # 支持通配符搜索
                    if '*' not in query and '?' not in query:
                        query = f"*{query}*"
                    query_obj = parser.parse(query)
                
                # 执行搜索
                results = searcher.search(query_obj, limit=limit)
                
                # 转换结果
                search_results = []
                for result in results:
                    search_results.append({
                        'id': int(result['id']),
                        'filename': result['filename'],
                        'path': result['path'],
                        'extension': result['extension'],
                        'size': result['size'],
                        'modified': result['modified'],
                        'folder_id': result['folder_id'],
                        'score': result.score
                    })
                
                return search_results
                
        except Exception as e:
            self.logger.error(f"文本搜索失败: {e}")
            return []
    
    def rebuild_index(self, db_manager):
        """重建索引"""
        try:
            self.logger.info("开始重建文本搜索索引")
            
            # 清空现有索引
            writer = self.index.writer()
            writer.commit()  # 清空
            
            # 重新添加所有文件
            with db_manager.get_session() as session:
                files = session.query(SharedFile).all()
                
                writer = self.index.writer()
                for file_record in files:
                    writer.add_document(
                        id=str(file_record.id),
                        filename=file_record.filename,
                        path=file_record.relative_path,
                        extension=file_record.extension or "",
                        size=file_record.file_size,
                        modified=file_record.file_modified,
                        folder_id=file_record.folder_id
                    )
                
                writer.commit()
            
            self.logger.info("文本搜索索引重建完成")
            
        except Exception as e:
            self.logger.error(f"重建文本搜索索引失败: {e}")

class ImageSearchEngine:
    """图像搜索引擎 - 基于OpenCV的图像识别"""
    
    def __init__(self, index_dir: str):
        self.index_dir = Path(index_dir) / "image_features"
        self.index_dir.mkdir(parents=True, exist_ok=True)
        self.logger = setup_logger("ImageSearchEngine")
        
        # 初始化特征提取器
        self.orb = cv2.ORB_create(nfeatures=1000)
        self.sift = cv2.SIFT_create() if hasattr(cv2, 'SIFT_create') else None
        
        # 图像特征数据库
        self.features_db = {}
        self.load_features_db()
    
    def load_features_db(self):
        """加载特征数据库"""
        try:
            features_file = self.index_dir / "features.npy"
            if features_file.exists():
                self.features_db = np.load(str(features_file), allow_pickle=True).item()
            
            self.logger.info(f"加载图像特征数据库: {len(self.features_db)} 个文件")
            
        except Exception as e:
            self.logger.error(f"加载图像特征数据库失败: {e}")
            self.features_db = {}
    
    def save_features_db(self):
        """保存特征数据库"""
        try:
            features_file = self.index_dir / "features.npy"
            np.save(str(features_file), self.features_db)
            
        except Exception as e:
            self.logger.error(f"保存图像特征数据库失败: {e}")
    
    def extract_features(self, image_path: str) -> Optional[np.ndarray]:
        """提取图像特征"""
        try:
            # 读取图像
            image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
            if image is None:
                return None
            
            # 调整图像大小以提高处理速度
            height, width = image.shape
            if max(height, width) > 800:
                scale = 800 / max(height, width)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height))
            
            # 使用ORB提取特征
            keypoints, descriptors = self.orb.detectAndCompute(image, None)
            
            if descriptors is not None:
                # 计算特征向量的平均值作为图像的特征表示
                feature_vector = np.mean(descriptors, axis=0)
                return feature_vector
            
            return None
            
        except Exception as e:
            self.logger.error(f"提取图像特征失败 {image_path}: {e}")
            return None
    
    def add_image_to_index(self, file_record: SharedFile, image_path: str):
        """添加图像到索引"""
        try:
            if not file_record.is_image:
                return
            
            features = self.extract_features(image_path)
            if features is not None:
                self.features_db[file_record.id] = {
                    'features': features,
                    'filename': file_record.filename,
                    'path': file_record.relative_path,
                    'folder_id': file_record.folder_id
                }
                
                # 定期保存特征数据库
                if len(self.features_db) % 100 == 0:
                    self.save_features_db()
            
        except Exception as e:
            self.logger.error(f"添加图像到索引失败: {e}")
    
    def remove_image_from_index(self, file_id: int):
        """从索引中移除图像"""
        try:
            if file_id in self.features_db:
                del self.features_db[file_id]
                self.save_features_db()
            
        except Exception as e:
            self.logger.error(f"从索引移除图像失败: {e}")
    
    def search_similar_images(self, query_image_path: str, 
                            limit: int = 50, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """搜索相似图像"""
        try:
            # 提取查询图像的特征
            query_features = self.extract_features(query_image_path)
            if query_features is None:
                return []
            
            # 计算相似度
            similarities = []
            for file_id, data in self.features_db.items():
                try:
                    # 计算余弦相似度
                    similarity = self.calculate_similarity(query_features, data['features'])
                    
                    if similarity >= threshold:
                        similarities.append({
                            'id': file_id,
                            'filename': data['filename'],
                            'path': data['path'],
                            'folder_id': data['folder_id'],
                            'similarity': similarity
                        })
                        
                except Exception as e:
                    continue
            
            # 按相似度排序
            similarities.sort(key=lambda x: x['similarity'], reverse=True)
            
            return similarities[:limit]
            
        except Exception as e:
            self.logger.error(f"图像搜索失败: {e}")
            return []
    
    def calculate_similarity(self, features1: np.ndarray, features2: np.ndarray) -> float:
        """计算特征相似度"""
        try:
            # 计算余弦相似度
            dot_product = np.dot(features1, features2)
            norm1 = np.linalg.norm(features1)
            norm2 = np.linalg.norm(features2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return max(0.0, similarity)  # 确保相似度为正值
            
        except Exception as e:
            return 0.0

class SearchService:
    """搜索服务 - 整合文本和图像搜索"""
    
    def __init__(self, db_manager, index_dir: str = "./data/search_index"):
        self.db_manager = db_manager
        self.logger = setup_logger("SearchService")
        
        # 初始化搜索引擎
        self.text_engine = TextSearchEngine(index_dir)
        self.image_engine = ImageSearchEngine(index_dir)
        
        # 搜索统计
        self.search_stats = {
            'total_searches': 0,
            'text_searches': 0,
            'image_searches': 0
        }
    
    def search_text(self, query: str, limit: int = 100) -> List[Dict[str, Any]]:
        """文本搜索"""
        try:
            self.search_stats['total_searches'] += 1
            self.search_stats['text_searches'] += 1
            
            results = self.text_engine.search(query, limit)
            
            self.logger.info(f"文本搜索完成: 查询='{query}', 结果数={len(results)}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"文本搜索失败: {e}")
            return []
    
    def search_image(self, image_path: str, limit: int = 50, 
                    threshold: float = 0.7) -> List[Dict[str, Any]]:
        """图像搜索"""
        try:
            self.search_stats['total_searches'] += 1
            self.search_stats['image_searches'] += 1
            
            results = self.image_engine.search_similar_images(image_path, limit, threshold)
            
            self.logger.info(f"图像搜索完成: 查询图像='{image_path}', 结果数={len(results)}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"图像搜索失败: {e}")
            return []
    
    def add_file_to_index(self, file_record: SharedFile):
        """添加文件到搜索索引"""
        try:
            # 添加到文本索引
            self.text_engine.add_file_to_index(file_record)
            
            # 如果是图像，添加到图像索引
            if file_record.is_image:
                full_path = file_record.get_full_path()
                if os.path.exists(full_path):
                    self.image_engine.add_image_to_index(file_record, full_path)
            
        except Exception as e:
            self.logger.error(f"添加文件到搜索索引失败: {e}")
    
    def remove_file_from_index(self, file_id: int):
        """从搜索索引中移除文件"""
        try:
            self.text_engine.remove_file_from_index(file_id)
            self.image_engine.remove_image_from_index(file_id)
            
        except Exception as e:
            self.logger.error(f"从搜索索引移除文件失败: {e}")
    
    def rebuild_index(self):
        """重建搜索索引"""
        try:
            self.logger.info("开始重建搜索索引")
            
            # 重建文本索引
            self.text_engine.rebuild_index(self.db_manager)
            
            # 重建图像索引
            self.image_engine.features_db = {}
            
            with self.db_manager.get_session() as session:
                image_files = session.query(SharedFile).filter_by(is_image=True).all()
                
                for file_record in image_files:
                    full_path = file_record.get_full_path()
                    if os.path.exists(full_path):
                        self.image_engine.add_image_to_index(file_record, full_path)
            
            self.image_engine.save_features_db()
            
            self.logger.info("搜索索引重建完成")
            
        except Exception as e:
            self.logger.error(f"重建搜索索引失败: {e}")
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        return {
            'search_stats': self.search_stats,
            'text_index_size': len(self.text_engine.index.doc_count_all()) if self.text_engine.index else 0,
            'image_index_size': len(self.image_engine.features_db)
        }
