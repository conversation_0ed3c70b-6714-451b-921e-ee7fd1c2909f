<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试图片预览功能</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-file-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .test-file-icon {
            width: 60px;
            height: 60px;
            background: #e3f2fd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        .test-file-info {
            flex: 1;
        }
        .test-file-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .test-file-meta {
            color: #666;
            font-size: 14px;
        }
        .test-actions {
            display: flex;
            gap: 10px;
        }
        .test-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        .test-btn.preview {
            background: #2196f3;
            color: white;
        }
        .test-btn.preview:hover {
            background: #1976d2;
        }
        .test-btn.favorite {
            background: #ff9800;
            color: white;
        }
        .test-btn.favorite:hover {
            background: #f57c00;
        }
        .test-btn.download {
            background: #4caf50;
            color: white;
        }
        .test-btn.download:hover {
            background: #388e3c;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 图片预览功能测试</h1>
        
        <div id="status" class="status">正在加载文件列表...</div>
        
        <div id="file-list">
            <!-- 文件列表将在这里动态生成 -->
        </div>
    </div>

    <!-- 文件预览模态框 -->
    <div class="modal" id="preview-modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="preview-title">文件预览</h3>
                <button class="modal-close" onclick="hidePreview()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="preview-container" id="preview-container">
                    <!-- 预览内容将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/components.js"></script>
    
    <script>
        let testFiles = [];
        
        // 模拟文件数据
        const mockFiles = [
            {
                id: 11,
                name: '3.jpg',
                type: 'file',
                size: 31460,
                is_image: true,
                extension: 'jpg'
            },
            {
                id: 16,
                name: 'sample.png',
                type: 'file', 
                size: 45000,
                is_image: true,
                extension: 'png'
            },
            {
                id: 14,
                name: 'test.gif',
                type: 'file',
                size: 28000,
                is_image: true,
                extension: 'gif'
            }
        ];
        
        function updateStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function renderFileList(files) {
            const fileListDiv = document.getElementById('file-list');
            
            if (!files || files.length === 0) {
                fileListDiv.innerHTML = '<p>没有找到图片文件</p>';
                return;
            }
            
            fileListDiv.innerHTML = files.map(file => `
                <div class="test-file-item" data-file-id="${file.id}">
                    <div class="test-file-icon">
                        <i class="fas fa-image" style="font-size: 24px; color: #2196f3;"></i>
                    </div>
                    <div class="test-file-info">
                        <div class="test-file-name">${file.name}</div>
                        <div class="test-file-meta">
                            ${formatFileSize(file.size)} • ${file.extension?.toUpperCase() || '图片'}
                        </div>
                    </div>
                    <div class="test-actions">
                        <button class="test-btn preview" onclick="testPreview(${file.id}, '${file.name}')">
                            <i class="fas fa-eye"></i> 预览
                        </button>
                        <button class="test-btn favorite" onclick="testFavorite(${file.id})">
                            <i class="fas fa-star"></i> 收藏
                        </button>
                        <button class="test-btn download" onclick="testDownload(${file.id})">
                            <i class="fas fa-download"></i> 下载
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        async function loadFiles() {
            try {
                updateStatus('正在加载文件列表...');
                
                // 首先尝试从API获取真实文件
                const authData = localStorage.getItem('fileShareAuth');
                if (authData) {
                    const auth = JSON.parse(authData);
                    const response = await fetch(`${CONFIG.API.BASE_URL}/files`, {
                        headers: {
                            'Authorization': `Bearer ${auth.token}`
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        const imageFiles = data.files ? data.files.filter(f => f.is_image) : [];
                        
                        if (imageFiles.length > 0) {
                            testFiles = imageFiles;
                            renderFileList(imageFiles);
                            updateStatus(`✅ 加载了 ${imageFiles.length} 个图片文件`);
                            return;
                        }
                    }
                }
                
                // 如果API失败，使用模拟数据
                testFiles = mockFiles;
                renderFileList(mockFiles);
                updateStatus('⚠️ 使用模拟数据（请先登录以获取真实文件）', 'error');
                
            } catch (error) {
                console.error('加载文件失败:', error);
                testFiles = mockFiles;
                renderFileList(mockFiles);
                updateStatus('⚠️ 使用模拟数据（API连接失败）', 'error');
            }
        }
        
        function testPreview(fileId, fileName) {
            console.log(`测试预览: 文件ID=${fileId}, 文件名=${fileName}`);
            
            try {
                // 显示预览模态框
                showPreview();
                
                // 设置标题
                document.getElementById('preview-title').textContent = fileName;
                
                // 创建预览内容
                const container = document.getElementById('preview-container');
                container.innerHTML = '<div class="loading-spinner"><div class="spinner"></div><p>正在加载图片...</p></div>';
                
                // 创建图片预览
                setTimeout(() => {
                    createImagePreview(fileId, fileName, container);
                }, 500);
                
            } catch (error) {
                console.error('预览失败:', error);
                updateStatus(`预览失败: ${error.message}`, 'error');
            }
        }
        
        async function createImagePreview(fileId, fileName, container) {
            try {
                // 获取认证token
                const authData = localStorage.getItem('fileShareAuth');
                let token = '';
                if (authData) {
                    const auth = JSON.parse(authData);
                    token = auth.token;
                }

                // 使用fetch获取图片，这样可以传递认证头
                const previewUrl = `${CONFIG.API.BASE_URL}/files/preview/${fileId}`;
                console.log('预览URL:', previewUrl);

                const response = await fetch(previewUrl, {
                    headers: token ? {
                        'Authorization': `Bearer ${token}`
                    } : {}
                });

                if (response.ok) {
                    // 获取图片blob
                    const blob = await response.blob();
                    const imageUrl = URL.createObjectURL(blob);

                    // 创建图片元素
                    const img = document.createElement('img');
                    img.style.maxWidth = '100%';
                    img.style.maxHeight = '80vh';
                    img.style.objectFit = 'contain';
                    img.style.borderRadius = '8px';
                    img.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';

                    img.onload = () => {
                        console.log('图片加载成功');
                        container.innerHTML = '';
                        container.appendChild(img);
                        updateStatus(`✅ 图片预览成功: ${fileName}`);
                    };

                    img.src = imageUrl;

                } else {
                    console.log('预览失败，尝试缩略图');
                    // 尝试使用缩略图
                    const thumbnailUrl = `${CONFIG.API.BASE_URL}/files/thumbnail/${fileId}/large`;
                    const thumbnailResponse = await fetch(thumbnailUrl, {
                        headers: token ? {
                            'Authorization': `Bearer ${token}`
                        } : {}
                    });

                    if (thumbnailResponse.ok) {
                        const blob = await thumbnailResponse.blob();
                        const imageUrl = URL.createObjectURL(blob);

                        const img = document.createElement('img');
                        img.style.maxWidth = '100%';
                        img.style.maxHeight = '80vh';
                        img.style.objectFit = 'contain';
                        img.style.borderRadius = '8px';
                        img.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';

                        img.onload = () => {
                            console.log('缩略图加载成功');
                            container.innerHTML = '';
                            container.appendChild(img);
                            updateStatus(`✅ 图片预览成功（缩略图）: ${fileName}`);
                        };

                        img.src = imageUrl;
                    } else {
                        throw new Error('缩略图也加载失败');
                    }
                }

            } catch (error) {
                console.log('图片预览完全失败:', error);
                container.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="fas fa-image" style="font-size: 64px; color: #ddd;"></i>
                        <p>图片预览失败</p>
                        <p>错误: ${error.message}</p>
                        <p>文件ID: ${fileId}</p>
                        <button class="test-btn download" onclick="testDownload(${fileId})">
                            <i class="fas fa-download"></i> 下载文件
                        </button>
                    </div>
                `;
                updateStatus(`❌ 图片预览失败: ${fileName}`, 'error');
            }
        }
        
        function testFavorite(fileId) {
            console.log(`测试收藏: 文件ID=${fileId}`);
            updateStatus(`🌟 收藏功能测试: 文件${fileId}`);
        }
        
        function testDownload(fileId) {
            console.log(`测试下载: 文件ID=${fileId}`);
            const downloadUrl = `${CONFIG.API.BASE_URL}/files/download/${fileId}`;
            window.open(downloadUrl, '_blank');
            updateStatus(`📥 开始下载: 文件${fileId}`);
        }
        
        function showPreview() {
            const modal = document.getElementById('preview-modal');
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        
        function hidePreview() {
            const modal = document.getElementById('preview-modal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        // 点击模态框背景关闭
        document.getElementById('preview-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hidePreview();
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hidePreview();
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadFiles();
        });
    </script>
</body>
</html>
