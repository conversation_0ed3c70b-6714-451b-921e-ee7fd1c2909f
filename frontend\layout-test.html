<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试</title>
    <link rel="stylesheet" href="css/style.css?v=20250608">
    <link rel="stylesheet" href="css/components.css?v=20250608">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div style="padding: 20px;">
        <h2>文件夹布局测试</h2>
        
        <div class="file-grid">
            <!-- 测试文件夹 -->
            <div class="file-item folder-item" data-file-id="1" data-file-type="folder">
                <div class="file-icon">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="file-name" title="测试文件夹">测试文件夹</div>
                <div class="file-actions">
                    <button class="action-btn" data-action="open" title="打开">
                        <i class="fas fa-folder-open"></i>
                    </button>
                </div>
            </div>
            
            <!-- 测试文件 -->
            <div class="file-item" data-file-id="2" data-file-type="file">
                <div class="file-icon">
                    <i class="fas fa-image"></i>
                </div>
                <div class="file-name" title="测试图片.jpg">测试图片.jpg</div>
                <div class="file-meta">
                    <span class="file-size">2.5 MB</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <h3>说明</h3>
        <ul>
            <li>文件夹：只显示图标和名称，不显示时间</li>
            <li>文件：显示图标、名称和文件大小，不显示时间</li>
            <li>图标在上面，名称在下面</li>
        </ul>
    </div>
</body>
</html>
