<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简洁详情视图演示 - 文件共享系统</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>简洁详情视图演示</h1>
        <p>类似Windows文件管理器的简洁横向布局</p>
        
        <div class="file-list" id="demo-list">
            <table class="file-table">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>修改时间</th>
                        <th>类型</th>
                        <th>大小</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 图片文件1 -->
                    <tr class="file-item" data-file-id="1" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <i class="fas fa-image"></i>
                            </div>
                            <span class="file-name">3.jpg</span>
                        </td>
                        <td>2025-05-28 23:49</td>
                        <td>JPG 图片文件</td>
                        <td>32 KB</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 图片文件2 -->
                    <tr class="file-item" data-file-id="2" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <i class="fas fa-image"></i>
                            </div>
                            <span class="file-name">wechat_2025-06-08_19:10:23.png</span>
                        </td>
                        <td>2025-06-08 19:10</td>
                        <td>PNG 图片文件</td>
                        <td>83 KB</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 图片文件3 -->
                    <tr class="file-item" data-file-id="3" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <i class="fas fa-image"></i>
                            </div>
                            <span class="file-name">banner.jpg</span>
                        </td>
                        <td>2025-01-16 09:20</td>
                        <td>JPG 图片文件</td>
                        <td>1.2 MB</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 图片文件4 -->
                    <tr class="file-item" data-file-id="4" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <i class="fas fa-file-image"></i>
                            </div>
                            <span class="file-name">design.psd</span>
                        </td>
                        <td>2025-01-16 11:15</td>
                        <td>Photoshop文档</td>
                        <td>15.8 MB</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 图片文件5 -->
                    <tr class="file-item" data-file-id="5" data-file-type="file">
                        <td class="file-name-cell">
                            <div class="file-icon">
                                <i class="fas fa-file-image"></i>
                            </div>
                            <span class="file-name">logo.ai</span>
                        </td>
                        <td>2025-01-16 14:30</td>
                        <td>Illustrator文档</td>
                        <td>3.7 MB</td>
                        <td>
                            <div class="file-actions">
                                <button class="action-btn" data-action="download" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="action-btn" data-action="preview" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="description">
            <h3>简洁详情视图特点：</h3>
            <ul>
                <li>紧凑的行高（32px），类似Windows文件管理器</li>
                <li>小图标（16x16px）配合文件名</li>
                <li>简洁的列布局：名称、修改时间、类型、大小</li>
                <li>悬停时显示操作按钮</li>
                <li>右对齐的文件大小，便于比较</li>
                <li>清晰的信息层次，易于快速浏览</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const actionBtns = document.querySelectorAll('.action-btn');
            
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.dataset.action;
                    const fileName = this.closest('tr').querySelector('.file-name').textContent;
                    alert(`${action}: ${fileName}`);
                });
            });
        });
    </script>
</body>
</html>
