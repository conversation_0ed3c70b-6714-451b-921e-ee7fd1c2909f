#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载记录API
"""

import requests
import json
import pymysql
from datetime import datetime, timed<PERSON><PERSON>

def test_download_records_api():
    """测试下载记录API"""
    
    # 1. 先登录获取token
    print("🔐 登录获取token...")
    login_url = "http://localhost:8086/api/auth/login"
    login_data = {
        "username": "fjj",
        "password": "123456"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"登录响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                token = result.get('token')
                user_info = result.get('user')
                print(f"✅ 登录成功，用户: {user_info.get('username')}, Token: {token[:20]}...")
                
                # 2. 测试下载记录API
                print("\n📋 测试下载记录API...")
                records_url = "http://localhost:8086/api/download/records"
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                records_response = requests.get(records_url, headers=headers)
                print(f"下载记录API响应状态: {records_response.status_code}")
                
                if records_response.status_code == 200:
                    records_result = records_response.json()
                    print(f"API响应: {json.dumps(records_result, indent=2, ensure_ascii=False)}")
                    
                    if records_result.get('success'):
                        records = records_result.get('records', [])
                        print(f"✅ 获取到 {len(records)} 条下载记录")
                        
                        for i, record in enumerate(records[:3]):  # 只显示前3条
                            print(f"记录 {i+1}:")
                            print(f"  ID: {record.get('id')}")
                            print(f"  文件名: {record.get('filename')}")
                            print(f"  文件大小: {record.get('file_size')}")
                            print(f"  下载时间: {record.get('download_time')}")
                            print(f"  是否加密: {record.get('is_encrypted')}")
                            print(f"  下载次数: {record.get('download_count')}")
                    else:
                        print(f"❌ API返回失败: {records_result.get('error')}")
                else:
                    print(f"❌ 下载记录API请求失败: {records_response.text}")
            else:
                print(f"❌ 登录失败: {result.get('error')}")
        else:
            print(f"❌ 登录请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def create_test_download_records():
    """创建测试下载记录"""
    print("\n📝 创建测试下载记录...")
    
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 获取用户fjj的ID
            cursor.execute("SELECT id FROM users WHERE username = 'fjj'")
            user = cursor.fetchone()
            
            if not user:
                print("❌ 找不到用户fjj")
                return False
            
            user_id = user[0]
            print(f"✅ 找到用户fjj，ID: {user_id}")
            
            # 检查现有记录
            cursor.execute("SELECT COUNT(*) FROM download_records WHERE user_id = %s", (user_id,))
            count = cursor.fetchone()[0]
            print(f"📊 现有下载记录数量: {count}")
            
            if count == 0:
                # 获取一些文件用于测试
                cursor.execute("SELECT id, filename FROM shared_files LIMIT 3")
                files = cursor.fetchall()
                
                if not files:
                    print("⚠️ 没有找到共享文件")
                    return False
                
                # 创建测试记录
                for i, (file_id, filename) in enumerate(files):
                    download_time = datetime.now() - timedelta(days=i, hours=i*2)
                    is_encrypted = i % 2 == 0
                    
                    cursor.execute("""
                        INSERT INTO download_records 
                        (file_id, user_id, download_type, zip_filename, zip_path, file_size, 
                         is_encrypted, password, download_status, created_at, downloaded_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        file_id, user_id, 'single',
                        f"{filename}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                        f"/tmp/downloads/{filename}_{download_time.strftime('%Y%m%d_%H%M%S')}.zip",
                        1024 * 1024 * (i + 1),  # 不同大小
                        is_encrypted,
                        f"pass{i+1}" if is_encrypted else None,
                        'completed',
                        download_time,
                        download_time
                    ))
                
                # 创建下载统计记录
                for file_id, filename in files:
                    cursor.execute("""
                        INSERT INTO download_statistics (file_id, total_downloads, encrypted_downloads, first_download, last_download)
                        VALUES (%s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE 
                        total_downloads = total_downloads + VALUES(total_downloads),
                        encrypted_downloads = encrypted_downloads + VALUES(encrypted_downloads),
                        last_download = VALUES(last_download)
                    """, (
                        file_id, 1, 1 if files.index((file_id, filename)) % 2 == 0 else 0,
                        datetime.now() - timedelta(days=files.index((file_id, filename))),
                        datetime.now() - timedelta(days=files.index((file_id, filename)))
                    ))
                
                connection.commit()
                print(f"✅ 创建了 {len(files)} 条测试下载记录")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建测试记录失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试下载记录功能...")
    
    # 1. 创建测试数据
    create_test_download_records()
    
    # 2. 测试API
    test_download_records_api()
    
    print("\n✅ 测试完成")
