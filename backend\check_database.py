#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的文件信息
"""

import pymysql

def check_database():
    """检查数据库中的文件信息"""
    print("🔍 检查数据库中的文件信息")
    print("=" * 50)
    
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查共享文件夹
            print("📁 检查共享文件夹...")
            cursor.execute("SELECT id, name, path, file_count, total_size FROM shared_folders")
            folders = cursor.fetchall()
            
            print(f"✅ 找到 {len(folders)} 个共享文件夹")
            for folder in folders:
                print(f"   ID: {folder[0]}, 名称: {folder[1]}, 路径: {folder[2]}")
                print(f"   文件数: {folder[3]}, 总大小: {folder[4]} 字节")
                
                # 检查该文件夹的文件
                print(f"\n📄 检查文件夹 {folder[0]} 的文件...")
                cursor.execute("""
                    SELECT id, filename, relative_path, file_size, extension, 
                           file_modified, download_count, view_count
                    FROM shared_files 
                    WHERE folder_id = %s 
                    LIMIT 5
                """, (folder[0],))
                
                files = cursor.fetchall()
                print(f"   找到 {len(files)} 个文件（显示前5个）:")
                
                for file_info in files:
                    print(f"     文件 {file_info[0]}:")
                    print(f"       文件名: {file_info[1]}")
                    print(f"       相对路径: {file_info[2]}")
                    print(f"       文件大小: {file_info[3]} 字节")
                    print(f"       扩展名: {file_info[4]}")
                    print(f"       修改时间: {file_info[5]}")
                    print(f"       下载次数: {file_info[6]}")
                    print(f"       查看次数: {file_info[7]}")
                    print()
                
                print("-" * 30)
        
        connection.close()
        print("✅ 数据库检查完成")
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
