# 企业级文件共享系统 API 文档

## 📋 目录

- [基础信息](#基础信息)
- [认证系统](#认证系统)
- [用户管理](#用户管理)
- [文件管理](#文件管理)
- [搜索功能](#搜索功能)
- [下载功能](#下载功能)
- [缩略图服务](#缩略图服务)
- [系统管理](#系统管理)
- [监控统计](#监控统计)
- [WebSocket通信](#websocket通信)
- [错误代码](#错误代码)

## 🌐 基础信息

### 服务器地址
- **开发环境**: `http://localhost:8080`
- **生产环境**: `http://your-server-ip:8080`

### 请求格式
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **认证方式**: Bearer Token

### 响应格式
```json
{
    "success": true,
    "data": {},
    "error": null,
    "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🔐 认证系统

### 1. 用户登录
**POST** `/api/auth/login`

**请求参数:**
```json
{
    "username": "admin",
    "password": "admin123"
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "user": {
            "id": 1,
            "username": "admin",
            "full_name": "系统管理员",
            "is_admin": true,
            "user_group": "admin"
        },
        "expires_in": 3600
    }
}
```

### 2. 用户登出
**POST** `/api/auth/logout`

**请求头:**
```
Authorization: Bearer <token>
```

**响应示例:**
```json
{
    "success": true,
    "message": "登出成功"
}
```

### 3. 刷新Token
**POST** `/api/auth/refresh`

**请求头:**
```
Authorization: Bearer <token>
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "token": "new_token_here",
        "expires_in": 3600
    }
}
```

### 4. 验证Token
**GET** `/api/auth/verify`

**请求头:**
```
Authorization: Bearer <token>
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "valid": true,
        "user_id": 1,
        "expires_at": "2024-01-01T13:00:00Z"
    }
}
```

## 👥 用户管理

### 1. 获取用户列表
**GET** `/api/admin/users`

**请求参数:**
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 20)
- `search`: 搜索关键词
- `user_group`: 用户组筛选

**请求头:**
```
Authorization: Bearer <admin_token>
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "users": [
            {
                "id": 1,
                "username": "admin",
                "full_name": "系统管理员",
                "email": "<EMAIL>",
                "user_group": "admin",
                "is_admin": true,
                "is_active": true,
                "is_banned": false,
                "created_at": "2024-01-01T10:00:00Z",
                "last_login": "2024-01-01T12:00:00Z",
                "login_count": 10
            }
        ],
        "total_count": 1,
        "page": 1,
        "page_size": 20
    }
}
```

### 2. 创建用户
**POST** `/api/admin/users`

**请求参数:**
```json
{
    "username": "newuser",
    "password": "password123",
    "full_name": "新用户",
    "email": "<EMAIL>",
    "user_group": "user",
    "is_admin": false
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "user_id": 2,
        "message": "用户创建成功"
    }
}
```

### 3. 更新用户
**PUT** `/api/admin/users/{user_id}`

**请求参数:**
```json
{
    "full_name": "更新的姓名",
    "email": "<EMAIL>",
    "user_group": "admin",
    "is_active": true
}
```

### 4. 删除用户
**DELETE** `/api/admin/users/{user_id}`

### 5. 获取用户详情
**GET** `/api/admin/users/{user_id}`

### 6. 重置用户密码
**POST** `/api/admin/users/{user_id}/reset-password`

**请求参数:**
```json
{
    "new_password": "newpassword123"
}
```

## 📁 文件管理

### 1. 获取共享文件夹列表
**GET** `/api/files/folders`

**响应示例:**
```json
{
    "success": true,
    "data": {
        "folders": [
            {
                "id": 1,
                "name": "共享文档",
                "path": "D:/shared/documents",
                "file_count": 150,
                "is_active": true,
                "created_at": "2024-01-01T10:00:00Z"
            }
        ]
    }
}
```

### 2. 创建共享文件夹
**POST** `/api/admin/folders`

**请求参数:**
```json
{
    "name": "新共享文件夹",
    "path": "D:/shared/new_folder",
    "description": "文件夹描述"
}
```

### 3. 获取文件夹文件列表
**GET** `/api/files/folders/{folder_id}/files`

**请求参数:**
- `page`: 页码
- `page_size`: 每页数量
- `sort`: 排序字段 (name, size, modified_time)
- `order`: 排序方向 (asc, desc)

**响应示例:**
```json
{
    "success": true,
    "data": {
        "files": [
            {
                "id": 1,
                "name": "document.pdf",
                "path": "D:/shared/documents/document.pdf",
                "size": 1048576,
                "extension": ".pdf",
                "mime_type": "application/pdf",
                "modified_time": "2024-01-01T10:00:00Z",
                "download_count": 5,
                "has_thumbnail": false
            }
        ],
        "total_count": 150,
        "page": 1,
        "page_size": 20
    }
}
```

### 4. 扫描文件夹
**POST** `/api/admin/folders/{folder_id}/scan`

### 5. 删除共享文件夹
**DELETE** `/api/admin/folders/{folder_id}`

### 6. 获取文件详情
**GET** `/api/files/{file_id}`

### 7. 删除文件记录
**DELETE** `/api/admin/files/{file_id}`

## 🔍 搜索功能

### 1. 文本搜索
**POST** `/api/search`

**请求参数:**
```json
{
    "query": "搜索关键词",
    "type": "text",
    "folder_id": 1,
    "file_types": [".pdf", ".doc", ".txt"],
    "page": 1,
    "page_size": 20
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "results": [
            {
                "file_id": 1,
                "name": "document.pdf",
                "path": "D:/shared/documents/document.pdf",
                "folder_name": "共享文档",
                "size": 1048576,
                "modified_time": "2024-01-01T10:00:00Z",
                "relevance_score": 0.95
            }
        ],
        "total_count": 10,
        "search_time": 0.05
    }
}
```

## ⭐ 收藏功能

### 1. 获取用户收藏列表
**GET** `/api/favorites`

**请求参数:**
- `page`: 页码（可选，默认1）
- `page_size`: 每页数量（可选，默认50）
- `folder_id`: 文件夹ID过滤（可选）

**响应示例:**
```json
{
    "favorites": [
        {
            "id": 1,
            "user_id": 1,
            "file_id": 123,
            "favorited_at": "2024-01-01T10:00:00Z",
            "notes": "重要文件",
            "file": {
                "id": 123,
                "filename": "design.psd",
                "relative_path": "projects/design.psd",
                "file_size": 10485760,
                "mime_type": "image/vnd.adobe.photoshop",
                "is_image": true,
                "has_thumbnail": true,
                "thumbnail_path": "thumbnails/123_medium.jpg",
                "folder_id": 1,
                "folder_name": "设计文件"
            }
        }
    ],
    "total_count": 15,
    "page": 1,
    "page_size": 50,
    "total_pages": 1
}
```

### 2. 切换文件收藏状态
**POST** `/api/favorites/toggle`

**请求参数:**
```json
{
    "file_id": 123,
    "notes": "收藏备注（可选）"
}
```

**响应示例:**
```json
{
    "success": true,
    "action": "added",
    "is_favorited": true,
    "data": {
        "id": 1,
        "user_id": 1,
        "file_id": 123,
        "favorited_at": "2024-01-01T10:00:00Z",
        "notes": "收藏备注"
    }
}
```

### 3. 批量检查文件收藏状态
**POST** `/api/favorites/status`

**请求参数:**
```json
{
    "file_ids": [123, 124, 125]
}
```

**响应示例:**
```json
{
    "123": true,
    "124": false,
    "125": true
}
```

### 4. 获取收藏统计信息
**GET** `/api/favorites/stats`

**响应示例:**
```json
{
    "total_count": 15,
    "recent_count": 3,
    "folder_stats": [
        {
            "folder_id": 1,
            "count": 8
        },
        {
            "folder_id": 2,
            "count": 7
        }
    ]
}
```

### 2. 图像搜索
**POST** `/api/search`

**请求参数:**
```json
{
    "query": "image_features_or_path",
    "type": "image",
    "similarity_threshold": 0.8
}
```

### 3. 高级搜索
**POST** `/api/search/advanced`

**请求参数:**
```json
{
    "filename": "*.pdf",
    "content": "关键词",
    "size_min": 1024,
    "size_max": 10485760,
    "date_from": "2024-01-01",
    "date_to": "2024-12-31",
    "folder_ids": [1, 2, 3]
}
```

## 📥 下载功能

### 1. 单文件下载
**GET** `/api/files/{file_id}/download`

**请求头:**
```
Authorization: Bearer <token>
```

**响应:** 文件流

### 2. 批量下载
**POST** `/api/files/batch/download`

**请求参数:**
```json
{
    "file_ids": [1, 2, 3, 4],
    "format": "zip"
}
```

**响应:** ZIP文件流

### 3. 文件夹下载
**GET** `/api/files/folders/{folder_id}/download`

### 4. 获取下载链接
**POST** `/api/files/{file_id}/download-link`

**响应示例:**
```json
{
    "success": true,
    "data": {
        "download_url": "http://localhost:8080/api/download/temp/abc123",
        "expires_at": "2024-01-01T13:00:00Z",
        "requires_password": false
    }
}
```

### 5. 加密下载
**GET** `/api/files/{file_id}/download/encrypted`

**响应示例:**
```json
{
    "success": true,
    "data": {
        "download_url": "http://localhost:8080/api/download/encrypted/xyz789",
        "password": "abc123def",
        "expires_at": "2024-01-01T13:00:00Z"
    }
}
```

## 🖼️ 缩略图服务

### 1. 获取缩略图
**GET** `/api/thumbnails/{file_id}`

**请求参数:**
- `size`: 缩略图尺寸 (small, medium, large, xlarge)

**响应:** 图像文件流

### 2. 生成缩略图
**POST** `/api/admin/thumbnails/{file_id}/generate`

### 3. 批量生成缩略图
**POST** `/api/admin/thumbnails/batch/generate`

**请求参数:**
```json
{
    "folder_id": 1,
    "file_types": [".jpg", ".png", ".pdf"]
}
```

### 4. 清理缩略图缓存
**DELETE** `/api/admin/thumbnails/cache`

## ⚙️ 系统管理

### 1. 获取系统信息
**GET** `/api/admin/system/info`

**响应示例:**
```json
{
    "success": true,
    "data": {
        "version": "1.0.0",
        "uptime": 3600,
        "python_version": "3.10.0",
        "database_version": "8.0.33",
        "disk_usage": {
            "total": 1000000000000,
            "used": 500000000000,
            "free": 500000000000
        },
        "memory_usage": {
            "total": 16777216000,
            "used": **********,
            "free": **********
        }
    }
}
```

### 2. 系统健康检查
**GET** `/api/health`

**响应示例:**
```json
{
    "success": true,
    "data": {
        "status": "healthy",
        "services": {
            "database": "ok",
            "file_service": "ok",
            "search_service": "ok",
            "thumbnail_service": "ok"
        },
        "timestamp": "2024-01-01T12:00:00Z"
    }
}
```

### 3. 系统配置管理
**GET** `/api/admin/config`

获取完整系统配置（管理员权限）

**响应示例:**
```json
{
    "success": true,
    "data": {
        "server": {
            "host": "0.0.0.0",
            "port": 8086,
            "frontend_port": 8084,
            "max_workers": 10,
            "timeout": 30
        },
        "file_share": {
            "max_file_size": **********,
            "allowed_extensions": [".jpg", ".png", ".pdf"],
            "thumbnail_sizes": {
                "small": [150, 150],
                "medium": [300, 300],
                "large": [600, 600],
                "xlarge": [1200, 1200]
            }
        },
        "download": {
            "enable_single_download": true,
            "enable_batch_download": true,
            "enable_folder_download": true,
            "max_batch_files": 100,
            "max_package_size": 524288000
        },
        "search": {
            "enable_text_search": true,
            "enable_image_search": true,
            "max_search_results": 1000
        }
    }
}
```

**PUT** `/api/admin/config`

更新系统配置（管理员权限）

**请求参数:**
```json
{
    "server.port": 8087,
    "file_share.max_file_size": 2147483648,
    "download.max_batch_files": 200
}
```

**响应示例:**
```json
{
    "success": true,
    "message": "配置更新成功",
    "restart_required": true
}
```

### 4. 公开配置获取
**GET** `/api/config/public`

获取前端需要的公开配置信息（无需认证）

**响应示例:**
```json
{
    "success": true,
    "data": {
        "server": {
            "host": "0.0.0.0",
            "port": 8086,
            "frontend_port": 8084
        },
        "file_share": {
            "allowed_extensions": [".jpg", ".png", ".pdf"],
            "max_file_size": **********,
            "thumbnail_sizes": {
                "small": [150, 150],
                "medium": [300, 300],
                "large": [600, 600],
                "xlarge": [1200, 1200]
            }
        },
        "download": {
            "enable_single_download": true,
            "enable_batch_download": true,
            "enable_folder_download": true,
            "max_batch_files": 100,
            "max_package_size": 524288000
        }
    }
}
```

### 4. 数据库备份
**POST** `/api/admin/system/backup`

### 5. 清理系统缓存
**DELETE** `/api/admin/system/cache`

## 📊 监控统计

### 1. 获取系统统计
**GET** `/api/admin/stats`

**响应示例:**
```json
{
    "success": true,
    "data": {
        "total_users": 100,
        "online_users": 15,
        "total_files": 10000,
        "total_folders": 50,
        "total_downloads": 5000,
        "today_downloads": 150,
        "storage_used": 500000000000,
        "bandwidth_used": 1000000000
    }
}
```

### 2. 获取用户活动日志
**GET** `/api/admin/logs/activity`

**请求参数:**
- `user_id`: 用户ID筛选
- `action`: 操作类型筛选
- `date_from`: 开始日期
- `date_to`: 结束日期
- `page`: 页码
- `page_size`: 每页数量

**响应示例:**
```json
{
    "success": true,
    "data": {
        "logs": [
            {
                "id": 1,
                "user_id": 1,
                "username": "admin",
                "action": "download_file",
                "resource": "document.pdf",
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0...",
                "timestamp": "2024-01-01T12:00:00Z",
                "details": {
                    "file_id": 1,
                    "file_size": 1048576
                }
            }
        ],
        "total_count": 1000
    }
}
```

### 3. 获取下载统计
**GET** `/api/admin/stats/downloads`

**请求参数:**
- `period`: 统计周期 (day, week, month, year)
- `date_from`: 开始日期
- `date_to`: 结束日期

### 4. 获取在线用户
**GET** `/api/admin/users/online`

### 5. 获取系统性能指标
**GET** `/api/admin/stats/performance`

## 🔌 WebSocket通信

### 连接地址
`ws://localhost:8080/ws`

### 认证
连接时发送认证消息:
```json
{
    "type": "auth",
    "token": "your_jwt_token"
}
```

### 消息类型

#### 1. 系统通知
```json
{
    "type": "notification",
    "data": {
        "title": "系统通知",
        "message": "系统将在10分钟后维护",
        "level": "warning",
        "timestamp": "2024-01-01T12:00:00Z"
    }
}
```

#### 2. 文件上传进度
```json
{
    "type": "upload_progress",
    "data": {
        "upload_id": "abc123",
        "filename": "large_file.zip",
        "progress": 75,
        "speed": "2.5 MB/s",
        "eta": 30
    }
}
```

#### 3. 用户状态更新
```json
{
    "type": "user_status",
    "data": {
        "user_id": 1,
        "status": "online",
        "last_activity": "2024-01-01T12:00:00Z"
    }
}
```

## ❌ 错误代码

### HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求过于频繁
- `500`: 服务器内部错误

### 业务错误代码
```json
{
    "success": false,
    "error": {
        "code": "USER_NOT_FOUND",
        "message": "用户不存在",
        "details": {
            "user_id": 123
        }
    }
}
```

#### 常见错误代码
- `INVALID_CREDENTIALS`: 登录凭据无效
- `TOKEN_EXPIRED`: Token已过期
- `INSUFFICIENT_PERMISSIONS`: 权限不足
- `FILE_NOT_FOUND`: 文件不存在
- `FOLDER_NOT_FOUND`: 文件夹不存在
- `USER_NOT_FOUND`: 用户不存在
- `DUPLICATE_USERNAME`: 用户名已存在
- `FILE_TOO_LARGE`: 文件过大
- `INVALID_FILE_TYPE`: 文件类型不支持
- `QUOTA_EXCEEDED`: 配额超限
- `SERVICE_UNAVAILABLE`: 服务不可用

## 📝 使用示例

### JavaScript示例
```javascript
// 登录
const login = async (username, password) => {
    const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
    });
    
    const result = await response.json();
    if (result.success) {
        localStorage.setItem('token', result.data.token);
        return result.data;
    }
    throw new Error(result.error.message);
};

// 获取文件列表
const getFiles = async (folderId) => {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/files/folders/${folderId}/files`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    
    return await response.json();
};

// 下载文件
const downloadFile = async (fileId) => {
    const token = localStorage.getItem('token');
    const response = await fetch(`/api/files/${fileId}/download`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    
    if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'filename';
        a.click();
    }
};
```

### Python示例
```python
import requests

class FileShareClient:
    def __init__(self, base_url):
        self.base_url = base_url
        self.token = None
    
    def login(self, username, password):
        response = requests.post(f'{self.base_url}/api/auth/login', json={
            'username': username,
            'password': password
        })
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                self.token = data['data']['token']
                return data['data']
        
        raise Exception('Login failed')
    
    def get_files(self, folder_id):
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(
            f'{self.base_url}/api/files/folders/{folder_id}/files',
            headers=headers
        )
        
        return response.json()
    
    def download_file(self, file_id, save_path):
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(
            f'{self.base_url}/api/files/{file_id}/download',
            headers=headers,
            stream=True
        )
        
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            return True
        
        return False

# 使用示例
client = FileShareClient('http://localhost:8080')
client.login('admin', 'admin123')
files = client.get_files(1)
client.download_file(1, 'downloaded_file.pdf')
```

## 🔧 开发工具

### Postman集合
我们提供了完整的Postman API集合，包含所有接口的示例请求。

### SDK支持
- **JavaScript/TypeScript**: 官方Web SDK
- **Python**: 官方Python SDK
- **Java**: 社区维护的Java SDK
- **C#**: 社区维护的.NET SDK

### 测试环境
- **测试服务器**: `http://test.example.com:8080`
- **测试账号**: `test_user` / `test123`
- **API文档**: `http://localhost:8080/api/docs`

## 📈 性能指标

### 响应时间
- **认证接口**: < 100ms
- **文件列表**: < 200ms
- **搜索接口**: < 500ms
- **文件下载**: 取决于文件大小和网络

### 并发限制
- **单用户**: 10个并发请求
- **系统总计**: 1000个并发连接
- **下载限制**: 每用户5个并发下载

### 配额限制
- **API调用**: 1000次/小时/用户
- **文件上传**: 100MB/文件，1GB/天/用户
- **搜索请求**: 100次/小时/用户

## 🔄 版本更新

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的用户管理功能
- 文件管理和下载功能
- 双搜索引擎支持
- 缩略图服务
- WebSocket实时通信

### 即将发布
- **v1.1.0**: 文件版本控制
- **v1.2.0**: 高级权限管理
- **v1.3.0**: 移动端API优化

## 🛡️ 安全说明

### HTTPS支持
生产环境强烈建议使用HTTPS协议。

### Token安全
- Token有效期为1小时
- 支持自动刷新机制
- 建议定期轮换密钥

### 文件安全
- 支持文件加密存储
- 下载链接临时有效
- 敏感文件自动保护

### 访问控制
- 基于角色的权限控制
- IP白名单支持
- 操作审计日志

---

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **开发者社区**: https://community.example.com
- **GitHub仓库**: https://github.com/example/file-share-system

### 常见问题
1. **Q: Token过期怎么办？**
   A: 使用refresh接口刷新Token，或重新登录。

2. **Q: 文件上传失败？**
   A: 检查文件大小限制和文件类型限制。

3. **Q: 搜索结果不准确？**
   A: 确保搜索索引已更新，可以手动触发重建索引。

4. **Q: 缩略图不显示？**
   A: 检查缩略图服务状态，确保支持的图像格式。

### 错误排查
1. 检查API响应状态码
2. 查看错误消息详情
3. 验证请求参数格式
4. 确认用户权限
5. 检查服务器日志

**文档版本**: v1.0.0
**最后更新**: 2024-01-01
**维护团队**: 企业级文件共享系统开发团队
