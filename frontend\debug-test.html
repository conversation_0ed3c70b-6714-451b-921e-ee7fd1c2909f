<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 前端调试测试页面</h1>
        <p>此页面用于测试前端JavaScript模块的加载和初始化</p>
        
        <div class="controls">
            <button class="btn" onclick="testModuleLoading()">测试模块加载</button>
            <button class="btn" onclick="testAPIConnection()">测试API连接</button>
            <button class="btn" onclick="testConfigAPI()">测试配置API</button>
            <button class="btn" onclick="clearLogs()">清空日志</button>
        </div>
        
        <div id="status"></div>
    </div>

    <div class="container" id="logs-container">
        <h2>📋 测试日志</h2>
        <pre id="logs"></pre>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/components.js"></script>
    <script src="js/file-manager.js"></script>
    <script src="js/upload.js"></script>
    <script src="js/search.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            logs.push(logEntry);
            updateLogsDisplay();
            console.log(logEntry);
        }

        function updateLogsDisplay() {
            const logsElement = document.getElementById('logs');
            logsElement.textContent = logs.join('\n');
            logsElement.scrollTop = logsElement.scrollHeight;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLogs() {
            logs = [];
            updateLogsDisplay();
            showStatus('日志已清空', 'info');
        }

        // 测试模块加载
        function testModuleLoading() {
            log('开始测试模块加载...');
            
            const modules = {
                'CONFIG': typeof CONFIG !== 'undefined',
                'Utils': typeof Utils !== 'undefined',
                'Components': typeof Components !== 'undefined',
                'api': typeof api !== 'undefined',
                'ConfigAPI': typeof ConfigAPI !== 'undefined',
                'SystemAPI': typeof SystemAPI !== 'undefined',
                'FileManager': typeof FileManager !== 'undefined',
                'FileUploader': typeof FileUploader !== 'undefined',
                'SearchManager': typeof SearchManager !== 'undefined',
                'NotificationManager': typeof NotificationManager !== 'undefined',
                'FileShareApp': typeof FileShareApp !== 'undefined'
            };
            
            let loadedCount = 0;
            let totalCount = Object.keys(modules).length;
            
            for (const [name, loaded] of Object.entries(modules)) {
                if (loaded) {
                    log(`✅ ${name} 模块已加载`, 'success');
                    loadedCount++;
                } else {
                    log(`❌ ${name} 模块未加载`, 'error');
                }
            }
            
            const percentage = Math.round((loadedCount / totalCount) * 100);
            showStatus(`模块加载测试完成：${loadedCount}/${totalCount} (${percentage}%)`, 
                      percentage === 100 ? 'success' : 'error');
        }

        // 测试API连接
        async function testAPIConnection() {
            log('开始测试API连接...');
            
            try {
                if (typeof api === 'undefined') {
                    throw new Error('API客户端未加载');
                }
                
                // 测试健康检查
                const response = await api.get('/health');
                log('✅ API健康检查成功', 'success');
                log(`API响应: ${JSON.stringify(response)}`, 'info');
                
                showStatus('API连接测试成功', 'success');
                
            } catch (error) {
                log(`❌ API连接测试失败: ${error.message}`, 'error');
                showStatus(`API连接测试失败: ${error.message}`, 'error');
            }
        }

        // 测试配置API
        async function testConfigAPI() {
            log('开始测试配置API...');
            
            try {
                if (typeof ConfigAPI === 'undefined') {
                    throw new Error('ConfigAPI未加载');
                }
                
                // 测试获取公开配置
                const config = await ConfigAPI.getPublicConfig();
                log('✅ 获取公开配置成功', 'success');
                log(`配置内容: ${JSON.stringify(config, null, 2)}`, 'info');
                
                showStatus('配置API测试成功', 'success');
                
            } catch (error) {
                log(`❌ 配置API测试失败: ${error.message}`, 'error');
                showStatus(`配置API测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，开始自动测试...');
            showStatus('页面加载完成，可以开始测试', 'info');
            
            // 延迟一下确保所有模块都加载完成
            setTimeout(() => {
                testModuleLoading();
            }, 1000);
        });

        // 监听错误
        window.addEventListener('error', (e) => {
            log(`❌ JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
        });

        window.addEventListener('unhandledrejection', (e) => {
            log(`❌ 未处理的Promise拒绝: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
