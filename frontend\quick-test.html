<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn.primary {
            background: #007bff;
            color: white;
        }
        .btn.success {
            background: #28a745;
            color: white;
        }
        .btn.warning {
            background: #ffc107;
            color: black;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }
        .input-group input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 快速测试</h1>
        <p>测试登录和API调用</p>

        <div class="section">
            <h3>🔐 登录测试</h3>
            <div class="input-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="input-group">
                <label>密码:</label>
                <input type="password" id="password" value="admin123">
            </div>
            <button class="btn primary" onclick="testLogin()">登录</button>
            <button class="btn warning" onclick="checkAuth()">检查认证</button>
            <div id="login-result" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📁 API测试</h3>
            <button class="btn success" onclick="testFoldersAPI()">测试文件夹API</button>
            <button class="btn success" onclick="testFilesAPI()">测试文件API</button>
            <button class="btn success" onclick="testFullFlow()">完整流程测试</button>
            <div id="api-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8086/api';

        function showResult(elementId, content) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = content;
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                showResult('login-result', '正在登录...');

                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // 保存认证信息
                    const authData = {
                        token: data.token,
                        user: data.user,
                        expires: data.expires
                    };
                    localStorage.setItem('fileShareAuth', JSON.stringify(authData));

                    showResult('login-result', `✅ 登录成功！
用户: ${data.user.username}
Token: ${data.token.substring(0, 20)}...
过期时间: ${data.expires}`);
                } else {
                    showResult('login-result', `❌ 登录失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                showResult('login-result', `❌ 登录请求失败: ${error.message}`);
            }
        }

        function checkAuth() {
            const authData = localStorage.getItem('fileShareAuth');
            if (authData) {
                try {
                    const auth = JSON.parse(authData);
                    showResult('login-result', `✅ 认证数据存在:
${JSON.stringify(auth, null, 2)}`);
                } catch (error) {
                    showResult('login-result', `❌ 认证数据格式错误: ${error.message}`);
                }
            } else {
                showResult('login-result', '❌ 未找到认证数据');
            }
        }

        async function testFoldersAPI() {
            try {
                showResult('api-result', '正在测试文件夹API...');

                const response = await fetch(`${API_BASE}/files/folders`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                showResult('api-result', `📁 文件夹API测试结果:
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                showResult('api-result', `❌ 文件夹API测试失败: ${error.message}`);
            }
        }

        async function testFilesAPI() {
            try {
                showResult('api-result', '正在测试文件API...');

                // 获取认证token
                const authData = localStorage.getItem('fileShareAuth');
                const headers = {
                    'Content-Type': 'application/json'
                };

                if (authData) {
                    const auth = JSON.parse(authData);
                    headers['Authorization'] = `Bearer ${auth.token}`;
                }

                const response = await fetch(`${API_BASE}/files`, {
                    method: 'GET',
                    headers
                });

                const data = await response.json();

                showResult('api-result', `📄 文件API测试结果:
状态码: ${response.status}
响应数据:
${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                showResult('api-result', `❌ 文件API测试失败: ${error.message}`);
            }
        }

        async function testFullFlow() {
            try {
                showResult('api-result', '开始完整流程测试...\n\n');

                let log = '';

                // 1. 检查认证
                log += '1. 检查认证状态\n';
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    log += '   ❌ 未找到认证数据，请先登录\n';
                    showResult('api-result', log);
                    return;
                }
                log += '   ✅ 认证数据存在\n\n';

                const auth = JSON.parse(authData);
                const headers = {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${auth.token}`
                };

                // 2. 测试文件夹API
                log += '2. 测试文件夹API\n';
                const foldersResponse = await fetch(`${API_BASE}/files/folders`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                const foldersData = await foldersResponse.json();
                log += `   状态: ${foldersResponse.status}\n`;
                log += `   数据: ${Array.isArray(foldersData) ? foldersData.length + ' 个文件夹' : '格式异常'}\n\n`;

                // 3. 测试文件API
                log += '3. 测试文件API\n';
                const filesResponse = await fetch(`${API_BASE}/files`, {
                    method: 'GET',
                    headers
                });
                const filesData = await filesResponse.json();
                log += `   状态: ${filesResponse.status}\n`;
                if (filesData.files) {
                    const folders = filesData.files.filter(f => f.type === 'folder');
                    const files = filesData.files.filter(f => f.type !== 'folder');
                    log += `   文件夹: ${folders.length} 个\n`;
                    log += `   文件: ${files.length} 个\n`;
                } else {
                    log += `   数据格式: ${JSON.stringify(filesData)}\n`;
                }

                showResult('api-result', log);

            } catch (error) {
                showResult('api-result', `❌ 完整流程测试失败: ${error.message}`);
            }
        }

        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
    </script>
</body>
</html>
