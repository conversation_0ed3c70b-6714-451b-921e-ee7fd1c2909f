<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏夹修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.danger {
            background: #dc3545;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .fix-list {
            background: #e7f3ff;
            border: 1px solid #b3d7ff;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .fix-list h4 {
            margin-top: 0;
            color: #0056b3;
        }
        .fix-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .fix-list li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 收藏夹功能修复验证</h1>
        
        <div class="fix-list">
            <h4>🎯 本次修复内容</h4>
            <ul>
                <li><strong>分页问题修复</strong>：增加页面大小到1000，避免只显示前50个收藏</li>
                <li><strong>刷新状态保持</strong>：修复刷新时收藏夹状态被重置的问题</li>
                <li><strong>导航状态管理</strong>：修复面包屑导航重置收藏夹状态的问题</li>
                <li><strong>收藏操作优化</strong>：在收藏夹中取消收藏时直接从列表移除，不刷新整个页面</li>
                <li><strong>侧边栏事件绑定</strong>：添加收藏夹菜单的点击事件处理</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📊 收藏夹状态测试</h3>
            <div id="favorites-status" class="status info">等待测试...</div>
            <button class="btn" onclick="testFavoritesLoad()">🔄 测试收藏夹加载</button>
            <button class="btn success" onclick="testFavoritesNavigation()">🧭 测试导航保持</button>
            <button class="btn warning" onclick="testRefreshBehavior()">🔄 测试刷新行为</button>
        </div>

        <div class="test-section">
            <h3>🎯 功能验证步骤</h3>
            <div>
                <p><strong>请按以下步骤手动验证修复效果：</strong></p>
                <ol>
                    <li>在主页面登录系统</li>
                    <li>收藏一些图片文件（确保超过50个以测试分页）</li>
                    <li>点击侧边栏的"收藏夹"菜单</li>
                    <li>验证是否显示所有收藏的文件</li>
                    <li>在收藏夹页面点击刷新按钮</li>
                    <li>验证是否仍然显示收藏夹而不是首页</li>
                    <li>在收藏夹中取消收藏某个文件</li>
                    <li>验证该文件是否立即从列表中消失</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 API测试</h3>
            <button class="btn" onclick="testAPI()">🧪 测试API连接</button>
            <button class="btn" onclick="testFavoritesAPI()">📥 测试收藏API</button>
            <button class="btn danger" onclick="clearLog()">🗑️ 清除日志</button>
            <div id="api-log" class="log">等待测试...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        let authToken = localStorage.getItem('auth_token');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('api-log');
            logElement.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type}] ${message}`);
        }

        function clearLog() {
            document.getElementById('api-log').textContent = '';
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('favorites-status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(url, {
                    ...options,
                    headers
                });

                const data = await response.json();
                
                if (response.ok) {
                    return { success: true, data };
                } else {
                    return { success: false, error: data.error || '未知错误' };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testAPI() {
            log('开始API连接测试...');
            const result = await apiRequest('/system/info');
            if (result.success) {
                log('✅ API连接正常', 'success');
            } else {
                log('❌ API连接失败: ' + result.error, 'error');
            }
        }

        async function testFavoritesAPI() {
            log('开始收藏API测试...');
            
            if (!authToken) {
                log('❌ 未找到认证令牌，请先登录', 'error');
                return;
            }

            // 测试默认分页
            log('测试1: 默认分页参数');
            const result1 = await apiRequest('/favorites');
            if (result1.success) {
                const data = result1.data;
                log(`✅ 默认分页: ${data.favorites?.length || 0}/${data.total_count || 0} 个收藏`);
            } else {
                log(`❌ 默认分页失败: ${result1.error}`, 'error');
            }

            // 测试大页面大小
            log('测试2: 大页面大小 (1000)');
            const result2 = await apiRequest('/favorites?page=1&page_size=1000');
            if (result2.success) {
                const data = result2.data;
                log(`✅ 大页面大小: ${data.favorites?.length || 0}/${data.total_count || 0} 个收藏`);
                
                if (data.total_count > 50 && data.favorites?.length === data.total_count) {
                    log('🎉 分页修复成功：能够获取所有收藏！', 'success');
                } else if (data.total_count <= 50) {
                    log('ℹ️ 收藏数量较少，无法验证分页修复效果', 'info');
                } else {
                    log('⚠️ 可能仍有分页问题', 'warning');
                }
            } else {
                log(`❌ 大页面大小测试失败: ${result2.error}`, 'error');
            }
        }

        async function testFavoritesLoad() {
            updateStatus('正在测试收藏夹加载...', 'info');
            log('开始收藏夹加载测试...');

            try {
                const result = await apiRequest('/favorites?page=1&page_size=1000');
                if (result.success) {
                    const data = result.data;
                    const count = data.favorites?.length || 0;
                    const total = data.total_count || 0;
                    
                    updateStatus(`✅ 收藏夹加载成功：${count}/${total} 个文件`, 'success');
                    log(`收藏夹加载成功：${count}/${total} 个文件`);
                    
                    if (total > 50 && count === total) {
                        log('🎉 分页修复验证成功！', 'success');
                    }
                } else {
                    updateStatus(`❌ 收藏夹加载失败：${result.error}`, 'error');
                    log(`收藏夹加载失败：${result.error}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ 测试异常：${error.message}`, 'error');
                log(`测试异常：${error.message}`, 'error');
            }
        }

        async function testFavoritesNavigation() {
            updateStatus('正在测试导航状态保持...', 'info');
            log('开始导航状态保持测试...');
            
            // 这个测试需要在主页面进行，这里只是提示
            updateStatus('ℹ️ 请在主页面手动测试导航功能', 'info');
            log('导航状态保持测试需要在主页面进行：');
            log('1. 点击收藏夹菜单');
            log('2. 点击面包屑导航');
            log('3. 验证是否正确切换视图');
        }

        async function testRefreshBehavior() {
            updateStatus('正在测试刷新行为...', 'info');
            log('开始刷新行为测试...');
            
            // 这个测试需要在主页面进行，这里只是提示
            updateStatus('ℹ️ 请在主页面手动测试刷新功能', 'info');
            log('刷新行为测试需要在主页面进行：');
            log('1. 进入收藏夹视图');
            log('2. 点击刷新按钮');
            log('3. 验证是否保持在收藏夹视图');
        }

        // 页面加载时自动测试
        window.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始自动测试...');
            
            if (!authToken) {
                updateStatus('⚠️ 未找到认证令牌，请先登录', 'error');
                log('未找到认证令牌，请先在主页面登录', 'error');
            } else {
                updateStatus('✅ 找到认证令牌，可以开始测试', 'success');
                testAPI();
            }
        });
    </script>
</body>
</html>
