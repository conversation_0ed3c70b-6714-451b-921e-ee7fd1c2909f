<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果视图切换测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #dbeafe;
            --primary-color-alpha: rgba(37, 99, 235, 0.1);
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e2e8f0;
            --gray-500: #6b7280;
            --gray-800: #1f2937;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --spacing-4: 1rem;
            --spacing-6: 1.5rem;
            --transition: all 0.2s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-secondary);
            color: var(--gray-800);
            line-height: 1.6;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .test-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .test-header h1 {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .test-header p {
            font-size: 1.1rem;
            color: var(--gray-500);
        }

        /* 模拟导航栏 */
        .navbar {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .nav-brand {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        /* 搜索框样式 */
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 28px;
            transition: all 0.2s ease;
            overflow: hidden;
            height: 44px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            width: 400px;
        }

        .search-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-color-alpha);
        }

        .search-container .fa-search {
            position: absolute;
            left: 18px;
            color: var(--gray-500);
            font-size: 16px;
            z-index: 2;
        }

        .search-container input {
            width: 100%;
            padding: 12px 56px 12px 48px;
            font-size: 15px;
            border: none;
            background: transparent;
            color: var(--gray-800);
            outline: none;
        }

        .search-type-indicator {
            position: absolute;
            right: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            border-radius: 50%;
            color: white;
            font-size: 12px;
        }

        /* 面包屑和视图控制 */
        .breadcrumb {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .breadcrumb-nav {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--gray-500);
            text-decoration: none;
        }

        .breadcrumb-item:hover {
            color: var(--primary-color);
        }

        .view-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .view-toggle {
            display: flex;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .view-btn {
            padding: 8px 12px;
            background: var(--bg-primary);
            border: none;
            color: var(--gray-500);
            cursor: pointer;
            transition: var(--transition);
        }

        .view-btn:hover {
            background: var(--bg-secondary);
        }

        .view-btn.active {
            background: var(--primary-color);
            color: white;
        }

        /* 文件网格 */
        .file-grid {
            display: grid;
            gap: 1rem;
            padding: 1rem 0;
        }

        .file-grid.extra-large-icons {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        }

        .file-grid.large-icons {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }

        .file-grid.medium-icons {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        }

        .file-grid.small-icons {
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        }

        .file-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1rem;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition);
        }

        .file-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .file-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .file-grid.extra-large-icons .file-icon {
            font-size: 4rem;
        }

        .file-grid.medium-icons .file-icon {
            font-size: 2.5rem;
        }

        .file-grid.small-icons .file-icon {
            font-size: 2rem;
        }

        .file-name {
            text-align: center;
            font-size: 0.9rem;
            color: var(--gray-800);
            word-break: break-word;
        }

        .file-grid.small-icons .file-name {
            font-size: 0.8rem;
        }

        /* 状态面板 */
        .status-panel {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid var(--border-color);
        }

        .status-panel h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-value {
            font-weight: 600;
        }

        .status-value.success {
            color: var(--success-color);
        }

        .status-value.warning {
            color: var(--warning-color);
        }

        .highlight-box {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .highlight-box h4 {
            color: #0369a1;
            margin-bottom: 0.5rem;
        }

        .test-buttons {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }

        .test-btn {
            padding: 0.5rem 1rem;
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            background: transparent;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .test-btn:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-th"></i> 搜索结果视图切换测试</h1>
            <p>测试在搜索结果页面切换视图模式时，不会回到原始目录</p>
        </div>

        <!-- 模拟导航栏 -->
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <i class="fas fa-cloud"></i> 文件共享系统
                </div>
                
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" placeholder="搜索图片文件..." value="3">
                    <div class="search-type-indicator">
                        <i class="fas fa-image"></i>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 面包屑和视图控制 -->
        <div class="breadcrumb">
            <div class="breadcrumb-nav">
                <a href="#" class="breadcrumb-item" onclick="clearSearch()">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <span class="breadcrumb-item">
                    <i class="fas fa-search"></i>
                    搜索: "3"
                </span>
            </div>
            
            <div class="view-controls">
                <div class="view-toggle">
                    <button class="view-btn" data-view="extra-large-icons" title="超大图标">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button class="view-btn active" data-view="large-icons" title="大图标">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="medium-icons" title="中等图标">
                        <i class="fas fa-grip-horizontal"></i>
                    </button>
                    <button class="view-btn" data-view="small-icons" title="小图标">
                        <i class="fas fa-grip-vertical"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h4>🔧 修复说明</h4>
            <p><strong>问题：</strong>在搜索结果页面点击视图切换按钮时，会回到原始目录，丢失搜索结果</p>
            <p><strong>修复：</strong>添加搜索状态管理，确保视图切换时保持搜索结果不变</p>
        </div>

        <!-- 模拟搜索结果 -->
        <div class="file-grid large-icons" id="file-grid">
            <div class="file-item">
                <div class="file-icon"><i class="fas fa-image"></i></div>
                <div class="file-name">image_3.jpg</div>
            </div>
            <div class="file-item">
                <div class="file-icon"><i class="fas fa-image"></i></div>
                <div class="file-name">photo_03.png</div>
            </div>
            <div class="file-item">
                <div class="file-icon"><i class="fas fa-image"></i></div>
                <div class="file-name">design_v3.psd</div>
            </div>
            <div class="file-item">
                <div class="file-icon"><i class="fas fa-image"></i></div>
                <div class="file-name">logo_3d.ai</div>
            </div>
            <div class="file-item">
                <div class="file-icon"><i class="fas fa-image"></i></div>
                <div class="file-name">banner_03.eps</div>
            </div>
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="simulateSearch()">模拟搜索</button>
            <button class="test-btn" onclick="clearSearch()">清空搜索</button>
            <button class="test-btn" onclick="addMoreResults()">添加更多结果</button>
        </div>

        <div class="status-panel">
            <h3><i class="fas fa-chart-line"></i> 测试状态</h3>
            <div class="status-item">
                <span>当前状态:</span>
                <span class="status-value success" id="current-status">搜索模式</span>
            </div>
            <div class="status-item">
                <span>搜索关键词:</span>
                <span class="status-value" id="search-query">"3"</span>
            </div>
            <div class="status-item">
                <span>当前视图模式:</span>
                <span class="status-value" id="current-view">大图标</span>
            </div>
            <div class="status-item">
                <span>搜索结果数量:</span>
                <span class="status-value" id="result-count">5</span>
            </div>
            <div class="status-item">
                <span>视图切换保持搜索:</span>
                <span class="status-value success" id="view-toggle-status">已修复</span>
            </div>
        </div>
    </div>

    <script>
        let isInSearchMode = true;
        let searchResults = [
            { name: 'image_3.jpg', icon: 'fas fa-image' },
            { name: 'photo_03.png', icon: 'fas fa-image' },
            { name: 'design_v3.psd', icon: 'fas fa-image' },
            { name: 'logo_3d.ai', icon: 'fas fa-image' },
            { name: 'banner_03.eps', icon: 'fas fa-image' }
        ];

        // 视图切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const viewBtns = document.querySelectorAll('.view-btn');
            const fileGrid = document.getElementById('file-grid');
            
            viewBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const viewMode = this.dataset.view;
                    
                    // 更新按钮状态
                    viewBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新网格类名
                    fileGrid.className = `file-grid ${viewMode}`;
                    
                    // 更新状态显示
                    const viewNames = {
                        'extra-large-icons': '超大图标',
                        'large-icons': '大图标',
                        'medium-icons': '中等图标',
                        'small-icons': '小图标'
                    };
                    document.getElementById('current-view').textContent = viewNames[viewMode];
                    
                    // 关键：在搜索模式下保持搜索结果
                    if (isInSearchMode) {
                        console.log(`✅ 视图切换到${viewNames[viewMode]}，保持搜索结果`);
                        renderSearchResults(); // 重新渲染搜索结果，不回到目录
                    }
                });
            });
        });

        function renderSearchResults() {
            const fileGrid = document.getElementById('file-grid');
            fileGrid.innerHTML = '';
            
            searchResults.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-icon"><i class="${file.icon}"></i></div>
                    <div class="file-name">${file.name}</div>
                `;
                fileGrid.appendChild(fileItem);
            });
            
            document.getElementById('result-count').textContent = searchResults.length;
        }

        function simulateSearch() {
            isInSearchMode = true;
            document.getElementById('current-status').textContent = '搜索模式';
            document.getElementById('current-status').className = 'status-value success';
            document.getElementById('search-query').textContent = '"3"';
            
            // 更新面包屑
            const breadcrumbNav = document.querySelector('.breadcrumb-nav');
            breadcrumbNav.innerHTML = `
                <a href="#" class="breadcrumb-item" onclick="clearSearch()">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <span class="breadcrumb-item">
                    <i class="fas fa-search"></i>
                    搜索: "3"
                </span>
            `;
            
            renderSearchResults();
            console.log('🔍 模拟搜索完成，进入搜索模式');
        }

        function clearSearch() {
            isInSearchMode = false;
            document.getElementById('current-status').textContent = '普通模式';
            document.getElementById('current-status').className = 'status-value';
            document.getElementById('search-query').textContent = '-';
            
            // 更新面包屑
            const breadcrumbNav = document.querySelector('.breadcrumb-nav');
            breadcrumbNav.innerHTML = `
                <a href="#" class="breadcrumb-item">
                    <i class="fas fa-home"></i>
                    首页
                </a>
            `;
            
            // 清空搜索框
            document.getElementById('search-input').value = '';
            
            // 显示普通文件列表
            const fileGrid = document.getElementById('file-grid');
            fileGrid.innerHTML = '<div style="text-align: center; padding: 2rem; color: #6b7280;">已清空搜索，显示原始文件列表</div>';
            
            document.getElementById('result-count').textContent = '0';
            console.log('🔄 搜索已清空，回到普通模式');
        }

        function addMoreResults() {
            if (isInSearchMode) {
                const newResults = [
                    { name: 'texture_3.tif', icon: 'fas fa-image' },
                    { name: 'mockup_v3.png', icon: 'fas fa-image' }
                ];
                searchResults.push(...newResults);
                renderSearchResults();
                console.log('➕ 添加了更多搜索结果');
            } else {
                alert('请先进入搜索模式');
            }
        }

        // 初始化
        simulateSearch();
    </script>
</body>
</html>
