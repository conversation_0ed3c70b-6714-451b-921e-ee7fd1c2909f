<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能测试 - 真实数据</title>
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-size: 12px;
        }
        .debug-log {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 11px;
        }
        .debug-log div {
            margin-bottom: 2px;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .debug-log .info { background: #d1ecf1; }
        .debug-log .success { background: #d4edda; }
        .debug-log .warning { background: #fff3cd; }
        .debug-log .error { background: #f8d7da; }
        .favorites-display {
            margin-top: 10px;
            padding: 8px;
            background: #e9ecef;
            border-radius: 4px;
        }
        .test-controls {
            margin-bottom: 10px;
        }
        .test-controls button {
            margin: 2px;
            padding: 4px 8px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>收藏功能测试 - 真实文件数据</h1>
        
        <div class="debug-panel">
            <h4>调试面板</h4>
            <div class="test-controls">
                <button onclick="clearFavorites()">清空收藏</button>
                <button onclick="showFavorites()">显示收藏</button>
                <button onclick="clearLog()">清空日志</button>
                <button onclick="testLocalStorage()">测试存储</button>
            </div>
            
            <div class="favorites-display" id="favorites-display">
                收藏数量: <span id="favorites-count">0</span>
            </div>
            
            <div class="debug-log" id="debug-log"></div>
        </div>
        
        <div class="file-grid" id="demo-grid">
            <!-- 真实文件数据 - 模拟您截图中的文件 -->
            <div class="file-item" data-file-id="5efedafd5dce35bee3d665673" data-file-type="file">
                <div class="file-icon">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmOGZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzMzNzNkYyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzwvdGV4dD4KPC9zdmc+" alt="664ace4d9f28cb621a39679e3d665673.jpg" />
                </div>
                <div class="file-name" title="664ace4d9f28cb621a39679e3d665673.jpg">664ace4d9f28cb621a39679e3d665673.jpg</div>
                <div class="file-meta">
                    <span class="file-size">31.46 KB</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn" data-action="favorite" title="收藏">
                        <i class="far fa-star"></i>
                    </button>
                </div>
            </div>
            
            <!-- 另一个测试文件 -->
            <div class="file-item" data-file-id="test-file-12345" data-file-type="file">
                <div class="file-icon">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuekuuS+i+WbvueJhzI8L3RleHQ+Cjwvc3ZnPg==" alt="测试图片2" />
                </div>
                <div class="file-name" title="design-mockup.psd">design-mockup.psd</div>
                <div class="file-meta">
                    <span class="file-size">2.3 MB</span>
                </div>
                <div class="file-actions">
                    <button class="action-btn" data-action="download" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn" data-action="preview" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn" data-action="favorite" title="收藏">
                        <i class="far fa-star"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载必要的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    
    <script>
        // 模拟文件管理器的收藏功能
        class TestFileManager {
            constructor() {
                this.files = [
                    {
                        id: '5efedafd5dce35bee3d665673',
                        name: '664ace4d9f28cb621a39679e3d665673.jpg',
                        type: 'file',
                        size: 32215
                    },
                    {
                        id: 'test-file-12345',
                        name: 'design-mockup.psd',
                        type: 'file',
                        size: 2411724
                    }
                ];
                this.currentFolderId = null;
                this.currentFolder = { name: '测试文件夹' };
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.updateAllFavoriteStates();
                this.updateFavoritesDisplay();
            }
            
            bindEvents() {
                document.addEventListener('click', (e) => {
                    const actionBtn = e.target.closest('.action-btn');
                    if (!actionBtn) return;
                    
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const action = actionBtn.dataset.action;
                    const fileItem = actionBtn.closest('.file-item');
                    const fileId = fileItem?.dataset.fileId;
                    
                    this.debugLog(`操作按钮点击: action=${action}, fileId=${fileId}`, 'info');
                    
                    if (!fileId) {
                        this.debugLog('未找到文件ID', 'error');
                        return;
                    }
                    
                    switch (action) {
                        case 'favorite':
                            this.debugLog(`执行收藏: ${fileId}`, 'info');
                            this.favoriteFile(fileId);
                            break;
                        case 'preview':
                            this.debugLog(`执行预览: ${fileId}`, 'info');
                            this.showToast('预览功能演示', 'info');
                            break;
                        case 'download':
                            this.debugLog(`执行下载: ${fileId}`, 'info');
                            this.showToast('下载功能演示', 'info');
                            break;
                    }
                });
            }
            
            async favoriteFile(fileId) {
                try {
                    this.debugLog(`开始收藏操作，文件ID: ${fileId}`, 'info');
                    
                    if (!fileId) {
                        this.debugLog('文件ID为空', 'error');
                        this.showToast('文件ID无效', 'error');
                        return;
                    }
                    
                    const file = this.files.find(f => f.id === fileId);
                    if (!file) {
                        this.debugLog(`未找到文件: ${fileId}`, 'error');
                        this.showToast('文件不存在', 'error');
                        return;
                    }
                    
                    this.debugLog(`找到文件: ${file.name}`, 'info');
                    
                    const favorites = this.getFavorites();
                    const isFavorited = favorites.some(fav => fav.id === fileId);
                    
                    this.debugLog(`当前收藏状态: ${isFavorited}, 收藏列表长度: ${favorites.length}`, 'info');
                    
                    if (isFavorited) {
                        // 取消收藏
                        const updatedFavorites = favorites.filter(fav => fav.id !== fileId);
                        this.saveFavorites(updatedFavorites);
                        this.showToast(`已取消收藏 "${file.name}"`, 'warning');
                        this.updateFavoriteButtonState(fileId, false);
                        this.debugLog(`取消收藏成功: ${file.name}`, 'success');
                    } else {
                        // 添加收藏
                        const favoriteItem = {
                            id: fileId,
                            name: file.name,
                            type: file.type,
                            size: file.size,
                            modified_at: new Date().toISOString(),
                            favorited_at: new Date().toISOString(),
                            folder_id: this.currentFolderId,
                            folder_name: this.currentFolder?.name || '根目录'
                        };
                        
                        favorites.push(favoriteItem);
                        this.saveFavorites(favorites);
                        this.showToast(`已收藏 "${file.name}"`, 'success');
                        this.updateFavoriteButtonState(fileId, true);
                        this.debugLog(`添加收藏成功: ${file.name}`, 'success');
                    }
                    
                    this.updateFavoritesDisplay();
                    
                } catch (error) {
                    this.debugLog(`收藏操作失败: ${error.message}`, 'error');
                    this.showToast('收藏操作失败', 'error');
                }
            }
            
            getFavorites() {
                try {
                    const favoritesData = localStorage.getItem('fileShareFavorites');
                    const favorites = favoritesData ? JSON.parse(favoritesData) : [];
                    this.debugLog(`获取收藏列表: ${favorites.length} 个项目`, 'info');
                    return favorites;
                } catch (error) {
                    this.debugLog(`获取收藏列表失败: ${error.message}`, 'error');
                    try {
                        localStorage.removeItem('fileShareFavorites');
                        this.debugLog('已清除损坏的收藏数据', 'warning');
                    } catch (e) {
                        this.debugLog(`清除损坏数据失败: ${e.message}`, 'error');
                    }
                    return [];
                }
            }
            
            saveFavorites(favorites) {
                try {
                    if (!Array.isArray(favorites)) {
                        throw new Error('收藏列表必须是数组');
                    }
                    
                    const dataToSave = JSON.stringify(favorites);
                    localStorage.setItem('fileShareFavorites', dataToSave);
                    this.debugLog(`保存收藏列表成功: ${favorites.length} 个项目`, 'success');
                    
                    // 验证保存是否成功
                    const savedData = localStorage.getItem('fileShareFavorites');
                    if (savedData !== dataToSave) {
                        throw new Error('数据保存验证失败');
                    }
                    
                } catch (error) {
                    this.debugLog(`保存收藏失败: ${error.message}`, 'error');
                    this.showToast('保存收藏失败，可能是存储空间不足', 'error');
                }
            }
            
            updateFavoriteButtonState(fileId, isFavorited) {
                this.debugLog(`更新收藏按钮状态: ${fileId} -> ${isFavorited}`, 'info');
                
                const fileItems = document.querySelectorAll(`[data-file-id="${fileId}"]`);
                this.debugLog(`找到 ${fileItems.length} 个文件项需要更新`, 'info');
                
                fileItems.forEach((item, index) => {
                    const favoriteBtn = item.querySelector('[data-action="favorite"]');
                    if (favoriteBtn) {
                        const icon = favoriteBtn.querySelector('i');
                        if (icon) {
                            if (isFavorited) {
                                icon.className = 'fas fa-star';
                                favoriteBtn.classList.add('favorited');
                                favoriteBtn.title = '取消收藏';
                                this.debugLog(`文件项 ${index + 1}: 设置为已收藏状态`, 'success');
                            } else {
                                icon.className = 'far fa-star';
                                favoriteBtn.classList.remove('favorited');
                                favoriteBtn.title = '收藏';
                                this.debugLog(`文件项 ${index + 1}: 设置为未收藏状态`, 'success');
                            }
                        } else {
                            this.debugLog(`文件项 ${index + 1}: 未找到收藏图标`, 'warning');
                        }
                    } else {
                        this.debugLog(`文件项 ${index + 1}: 未找到收藏按钮`, 'warning');
                    }
                });
            }
            
            isFileFavorited(fileId) {
                const favorites = this.getFavorites();
                return favorites.some(fav => fav.id === fileId);
            }
            
            updateAllFavoriteStates() {
                this.files.forEach(file => {
                    const isFavorited = this.isFileFavorited(file.id);
                    this.updateFavoriteButtonState(file.id, isFavorited);
                });
            }
            
            updateFavoritesDisplay() {
                const favorites = this.getFavorites();
                document.getElementById('favorites-count').textContent = favorites.length;
            }
            
            debugLog(message, type = 'info') {
                const logContainer = document.getElementById('debug-log');
                const logEntry = document.createElement('div');
                logEntry.className = type;
                logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
                console.log(`[DEBUG ${type.toUpperCase()}]`, message);
            }
            
            showToast(message, type = 'info') {
                const existingToast = document.querySelector('.demo-toast');
                if (existingToast) {
                    existingToast.remove();
                }
                
                const toast = document.createElement('div');
                toast.className = `demo-toast demo-toast-${type}`;
                toast.textContent = message;
                toast.style.cssText = `
                    position: fixed;
                    top: 60px;
                    right: 20px;
                    background: ${type === 'success' ? '#d4edda' : type === 'warning' ? '#fff3cd' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
                    color: ${type === 'success' ? '#155724' : type === 'warning' ? '#856404' : type === 'error' ? '#721c24' : '#0c5460'};
                    border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'warning' ? '#ffeaa7' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
                    border-radius: 8px;
                    padding: 12px 16px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 10001;
                    font-size: 14px;
                    max-width: 300px;
                    animation: slideIn 0.3s ease;
                `;
                
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.style.animation = 'slideOut 0.3s ease';
                        setTimeout(() => toast.remove(), 300);
                    }
                }, 3000);
            }
        }
        
        // 全局函数
        function clearFavorites() {
            localStorage.removeItem('fileShareFavorites');
            testFileManager.debugLog('清空所有收藏', 'warning');
            testFileManager.updateAllFavoriteStates();
            testFileManager.updateFavoritesDisplay();
        }
        
        function showFavorites() {
            const favorites = testFileManager.getFavorites();
            testFileManager.debugLog(`当前收藏列表: ${JSON.stringify(favorites, null, 2)}`, 'info');
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        function testLocalStorage() {
            try {
                const testKey = 'test-storage-' + Date.now();
                const testValue = { test: 'data', timestamp: Date.now() };
                localStorage.setItem(testKey, JSON.stringify(testValue));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                localStorage.removeItem(testKey);
                
                if (JSON.stringify(retrieved) === JSON.stringify(testValue)) {
                    testFileManager.debugLog('localStorage 测试通过', 'success');
                    testFileManager.showToast('存储功能正常', 'success');
                } else {
                    testFileManager.debugLog('localStorage 数据不匹配', 'error');
                    testFileManager.showToast('存储功能异常', 'error');
                }
            } catch (error) {
                testFileManager.debugLog(`localStorage 测试失败: ${error.message}`, 'error');
                testFileManager.showToast('存储功能不可用', 'error');
            }
        }
        
        // 初始化
        let testFileManager;
        document.addEventListener('DOMContentLoaded', function() {
            testFileManager = new TestFileManager();
            testFileManager.debugLog('测试页面初始化完成', 'success');
        });
        
        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
