<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文件夹显示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .folder-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .folder-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }
        .folder-item:hover {
            background: #e9e9e9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .folder-icon {
            font-size: 48px;
            color: #ffa500;
            margin-bottom: 10px;
        }
        .folder-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .folder-info {
            font-size: 12px;
            color: #666;
        }
        .status {
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.loading {
            background: #e3f2fd;
            color: #1976d2;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>文件夹显示测试</h1>
        <div id="status" class="status loading">正在加载文件夹...</div>
        <div id="folder-grid" class="folder-grid"></div>
    </div>

    <script>
        // 简化的配置
        const CONFIG = {
            API: {
                BASE_URL: 'http://localhost:8086/api'
            }
        };

        // 简化的API调用
        async function getFolders() {
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    throw new Error('请先登录');
                }

                const response = await fetch(`${CONFIG.API.BASE_URL}/files/folders`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const folders = await response.json();
                return folders;
            } catch (error) {
                console.error('获取文件夹失败:', error);
                throw error;
            }
        }

        // 渲染文件夹
        function renderFolders(folders) {
            const container = document.getElementById('folder-grid');
            const status = document.getElementById('status');
            
            if (!folders || folders.length === 0) {
                status.className = 'status error';
                status.textContent = '没有找到共享文件夹';
                return;
            }

            status.className = 'status success';
            status.textContent = `找到 ${folders.length} 个共享文件夹`;

            container.innerHTML = '';
            
            folders.forEach(folder => {
                const folderElement = document.createElement('div');
                folderElement.className = 'folder-item';
                folderElement.innerHTML = `
                    <div class="folder-icon">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="folder-name">${folder.name || '未命名文件夹'}</div>
                    <div class="folder-info">
                        ID: ${folder.id}<br>
                        文件数: ${folder.file_count || folder.statistics?.file_count || 0}<br>
                        路径: ${folder.path || '未知'}
                    </div>
                `;
                container.appendChild(folderElement);
            });
        }

        // 页面加载时获取文件夹
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const folders = await getFolders();
                console.log('获取到的文件夹:', folders);
                renderFolders(folders);
            } catch (error) {
                const status = document.getElementById('status');
                status.className = 'status error';
                status.textContent = `加载失败: ${error.message}`;
            }
        });
    </script>
</body>
</html>
