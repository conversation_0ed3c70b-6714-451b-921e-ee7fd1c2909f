<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载记录集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .download-records-view {
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
    </style>
    <!-- 引入主应用的CSS和JS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔽 下载记录集成测试</h1>
            <p>测试主应用中的下载记录功能</p>
        </div>

        <div class="test-section">
            <h3>1. 登录状态检查</h3>
            <button onclick="checkLoginStatus()">检查登录状态</button>
            <button onclick="loginAsTestUser()">使用测试用户登录</button>
            <div id="loginResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 下载记录功能测试</h3>
            <button onclick="testDownloadRecordsAPI()">测试下载记录API</button>
            <button onclick="testDownloadRecordsView()">测试下载记录视图</button>
            <div id="downloadResult" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 下载记录视图预览</h3>
            <div id="downloadRecordsPreview" class="download-records-view">
                <!-- 这里将显示下载记录视图 -->
            </div>
        </div>
    </div>

    <!-- 引入主应用的JS文件 -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/components.js"></script>

    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        function checkLoginStatus() {
            const token = localStorage.getItem('token');
            const authInfo = localStorage.getItem('fileShareAuth');
            
            if (token && authInfo) {
                try {
                    const auth = JSON.parse(authInfo);
                    showResult('loginResult', `已登录\nToken: ${token}\n用户信息: ${JSON.stringify(auth, null, 2)}`, 'success');
                } catch (e) {
                    showResult('loginResult', `Token存在但用户信息解析失败: ${e.message}`, 'error');
                }
            } else {
                showResult('loginResult', '未登录，请先登录', 'error');
            }
        }

        async function loginAsTestUser() {
            try {
                const response = await fetch('http://localhost:8086/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'fjj',
                        password: '123456'
                    })
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    localStorage.setItem('token', result.token);
                    localStorage.setItem('fileShareAuth', JSON.stringify({
                        token: result.token,
                        user: result.user,
                        loginTime: new Date().toISOString()
                    }));
                    showResult('loginResult', `登录成功！\nToken: ${result.token}\n用户信息: ${JSON.stringify(result.user, null, 2)}`, 'success');
                } else {
                    showResult('loginResult', `登录失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('loginResult', `登录请求失败: ${error.message}`, 'error');
            }
        }

        async function testDownloadRecordsAPI() {
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    showResult('downloadResult', '请先登录', 'error');
                    return;
                }

                const response = await fetch('http://localhost:8086/api/download/records', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const result = await response.json();
                
                showResult('downloadResult', `API测试结果:\nStatus: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}`, response.ok ? 'success' : 'error');

                // 如果成功，保存结果用于视图测试
                if (response.ok && result.records) {
                    window.testRecords = result.records;
                }
            } catch (error) {
                showResult('downloadResult', `API测试失败: ${error.message}`, 'error');
            }
        }

        function testDownloadRecordsView() {
            const records = window.testRecords;
            const preview = document.getElementById('downloadRecordsPreview');
            
            if (!records) {
                preview.innerHTML = '<p>请先运行API测试获取数据</p>';
                return;
            }

            // 模拟主应用的下载记录视图
            let html = `
                <div class="download-records-header">
                    <h2>
                        <i class="fas fa-download"></i>
                        我的下载记录
                    </h2>
                    <div class="download-records-controls">
                        <button class="btn btn-primary">
                            <i class="fas fa-sync-alt"></i>
                            刷新记录
                        </button>
                    </div>
                </div>
                <div class="download-records-content">
            `;

            if (records.length === 0) {
                html += `
                    <div class="empty-state">
                        <i class="fas fa-download"></i>
                        <h3>暂无下载记录</h3>
                        <p>您还没有下载过任何文件</p>
                    </div>
                `;
            } else {
                html += '<div class="download-records-list">';
                records.forEach(record => {
                    html += createDownloadRecordCard(record);
                });
                html += '</div>';
            }

            html += '</div>';
            preview.innerHTML = html;
        }

        function createDownloadRecordCard(record) {
            const fileIcon = getFileIcon(record.filename);
            const fileSize = formatFileSize(record.file_size || 0);
            const downloadTime = formatDateTime(record.download_time);
            const isEncrypted = record.is_encrypted;
            const downloadCount = record.download_count || 0;
            
            return `
                <div class="download-record-card" data-record-id="${record.id}">
                    <div class="record-file-info">
                        <div class="record-file-icon">
                            <i class="${fileIcon}"></i>
                        </div>
                        <div class="record-file-details">
                            <h4 title="${record.filename}">${record.filename}</h4>
                            <p class="record-file-path" title="${record.file_path || ''}">${record.file_path || ''}</p>
                        </div>
                    </div>
                    
                    <div class="record-meta">
                        <div class="record-meta-item">
                            <span class="record-meta-label">文件大小:</span>
                            <span class="record-meta-value">${fileSize}</span>
                        </div>
                        <div class="record-meta-item">
                            <span class="record-meta-label">下载时间:</span>
                            <span class="record-meta-value">${downloadTime}</span>
                        </div>
                        <div class="record-meta-item">
                            <span class="record-meta-label">下载次数:</span>
                            <span class="record-meta-value">${downloadCount}</span>
                        </div>
                        <div class="record-meta-item">
                            <span class="record-meta-label">状态:</span>
                            <span class="record-status ${isEncrypted ? 'encrypted' : 'normal'}">
                                ${isEncrypted ? '🔒 已加密' : '✅ 正常'}
                            </span>
                        </div>
                    </div>
                    
                    <div class="record-actions">
                        <button class="btn btn-primary btn-sm">
                            <i class="fas fa-download"></i>
                            重新下载
                        </button>
                        ${isEncrypted ? `
                            <button class="btn btn-secondary btn-sm">
                                <i class="fas fa-key"></i>
                                获取密码
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function getFileIcon(filename) {
            if (!filename) return 'fas fa-file';
            
            const ext = filename.split('.').pop().toLowerCase();
            const iconMap = {
                'jpg': 'fas fa-image', 'jpeg': 'fas fa-image', 'png': 'fas fa-image',
                'gif': 'fas fa-image', 'bmp': 'fas fa-image', 'tif': 'fas fa-image',
                'pdf': 'fas fa-file-pdf', 'doc': 'fas fa-file-word', 'docx': 'fas fa-file-word',
                'zip': 'fas fa-file-archive', 'rar': 'fas fa-file-archive',
                'mp4': 'fas fa-file-video', 'avi': 'fas fa-file-video',
                'mp3': 'fas fa-file-audio', 'wav': 'fas fa-file-audio'
            };
            
            return iconMap[ext] || 'fas fa-file';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDateTime(dateString) {
            if (!dateString) return '未知时间';
            
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return '无效时间';
                
                const now = new Date();
                const diffMs = now - date;
                const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                
                if (diffDays === 0) {
                    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays === 1) {
                    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays < 7) {
                    return diffDays + '天前';
                } else {
                    return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'short', day: 'numeric' });
                }
            } catch (error) {
                return '时间格式错误';
            }
        }

        // 页面加载时检查登录状态
        window.onload = function() {
            checkLoginStatus();
        };
    </script>
</body>
</html>
