@echo off
chcp 65001 >nul
title 文件共享系统 - 一键启动

echo.
echo ========================================
echo 🚀 文件共享系统 - 一键启动工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 💡 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

:: 进入后端目录
cd /d "%~dp0backend"

:: 检查是否需要初始化
if not exist "database_initialized.flag" (
    echo.
    echo 🔧 首次运行，正在初始化系统...
    echo.
    
    :: 运行自动迁移脚本
    python auto_migrate.py
    
    if errorlevel 1 (
        echo.
        echo ❌ 初始化失败，请检查错误信息
        pause
        exit /b 1
    )
    
    :: 创建初始化标记文件
    echo initialized > database_initialized.flag
    
    echo.
    echo ✅ 系统初始化完成
    echo.
)

:: 启动服务器
echo 🚀 启动文件共享系统...
echo.
echo 📋 服务信息:
echo    前端地址: http://localhost:3000
echo    API地址:  http://localhost:8080
echo    管理员:   admin / admin123
echo.
echo 💡 按 Ctrl+C 停止服务器
echo.

python main.py

echo.
echo 👋 服务器已停止
pause
