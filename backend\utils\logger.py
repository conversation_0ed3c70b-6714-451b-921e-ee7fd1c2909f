#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
"""

import logging
import os
from datetime import datetime
from pathlib import Path
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler

def setup_logger(name: str, log_level: str = "INFO", log_dir: str = "logs") -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_level: 日志级别
        log_dir: 日志目录
    
    Returns:
        配置好的日志记录器
    """
    
    # 创建日志目录
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 如果已经有处理器，直接返回
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器 - 按日期轮转
    file_handler = TimedRotatingFileHandler(
        filename=log_path / f"{name}.log",
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 错误文件处理器
    error_handler = RotatingFileHandler(
        filename=log_path / f"{name}_error.log",
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    logger.addHandler(error_handler)
    
    return logger

class DatabaseLogger:
    """数据库日志记录器"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("DatabaseLogger")
    
    def log_activity(self, user_id: int, action: str, details: dict = None, 
                    ip_address: str = None, user_agent: str = None):
        """记录用户活动"""
        try:
            from models.activity_log import ActivityLog
            
            with self.db_manager.get_session() as session:
                log_entry = ActivityLog(
                    user_id=user_id,
                    action=action,
                    details=details or {},
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                session.add(log_entry)
                session.commit()
                
        except Exception as e:
            self.logger.error(f"记录活动日志失败: {e}")
    
    def log_file_access(self, user_id: int, file_id: int, action: str, 
                       ip_address: str = None):
        """记录文件访问"""
        details = {
            'file_id': file_id,
            'access_type': action
        }
        self.log_activity(user_id, f"file_{action}", details, ip_address)
    
    def log_login(self, user_id: int, success: bool, ip_address: str = None, 
                 user_agent: str = None):
        """记录登录"""
        action = "login_success" if success else "login_failed"
        self.log_activity(user_id, action, {}, ip_address, user_agent)
    
    def log_download(self, user_id: int, file_ids: list, download_type: str,
                    file_size: int = 0, ip_address: str = None):
        """记录下载"""
        details = {
            'file_ids': file_ids,
            'download_type': download_type,
            'file_size': file_size
        }
        self.log_activity(user_id, "download", details, ip_address)
    
    def log_search(self, user_id: int, query: str, search_type: str,
                  results_count: int = 0, ip_address: str = None):
        """记录搜索"""
        details = {
            'query': query,
            'search_type': search_type,
            'results_count': results_count
        }
        self.log_activity(user_id, "search", details, ip_address)
    
    def log_admin_action(self, admin_id: int, action: str, target_id: int = None,
                        details: dict = None, ip_address: str = None):
        """记录管理员操作"""
        admin_details = details or {}
        admin_details['target_id'] = target_id
        self.log_activity(admin_id, f"admin_{action}", admin_details, ip_address)

class SecurityLogger:
    """安全日志记录器"""
    
    def __init__(self):
        self.logger = setup_logger("SecurityLogger")
    
    def log_security_event(self, event_type: str, details: dict, 
                          severity: str = "INFO"):
        """记录安全事件"""
        log_method = getattr(self.logger, severity.lower(), self.logger.info)
        log_method(f"安全事件 [{event_type}]: {details}")
    
    def log_suspicious_activity(self, ip_address: str, user_id: int = None,
                               activity: str = "", details: dict = None):
        """记录可疑活动"""
        event_details = {
            'ip_address': ip_address,
            'user_id': user_id,
            'activity': activity,
            'details': details or {}
        }
        self.log_security_event("SUSPICIOUS_ACTIVITY", event_details, "WARNING")
    
    def log_failed_login(self, username: str, ip_address: str, reason: str):
        """记录登录失败"""
        details = {
            'username': username,
            'ip_address': ip_address,
            'reason': reason
        }
        self.log_security_event("LOGIN_FAILED", details, "WARNING")
    
    def log_access_denied(self, user_id: int, resource: str, ip_address: str):
        """记录访问被拒绝"""
        details = {
            'user_id': user_id,
            'resource': resource,
            'ip_address': ip_address
        }
        self.log_security_event("ACCESS_DENIED", details, "WARNING")
    
    def log_rate_limit_exceeded(self, ip_address: str, endpoint: str):
        """记录频率限制超出"""
        details = {
            'ip_address': ip_address,
            'endpoint': endpoint
        }
        self.log_security_event("RATE_LIMIT_EXCEEDED", details, "WARNING")

class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self):
        self.logger = setup_logger("PerformanceLogger")
    
    def log_performance_metric(self, metric_name: str, value: float, 
                              unit: str = "", details: dict = None):
        """记录性能指标"""
        message = f"性能指标 [{metric_name}]: {value} {unit}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_slow_query(self, query: str, execution_time: float, 
                      threshold: float = 1.0):
        """记录慢查询"""
        if execution_time > threshold:
            details = {
                'query': query,
                'execution_time': execution_time,
                'threshold': threshold
            }
            self.logger.warning(f"慢查询检测: {details}")
    
    def log_memory_usage(self, process_name: str, memory_mb: float):
        """记录内存使用"""
        self.log_performance_metric(f"memory_usage_{process_name}", 
                                   memory_mb, "MB")
    
    def log_cpu_usage(self, process_name: str, cpu_percent: float):
        """记录CPU使用"""
        self.log_performance_metric(f"cpu_usage_{process_name}", 
                                   cpu_percent, "%")

# 全局日志记录器实例
main_logger = setup_logger("FileShareSystem")
security_logger = SecurityLogger()
performance_logger = PerformanceLogger()
