#!/usr/bin/env python3
"""
测试收藏功能API
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8086/api"
USERNAME = "test"
PASSWORD = "123456"

def test_favorites_api():
    """测试收藏功能API"""
    
    # 1. 登录获取token
    print("1. 登录...")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": USERNAME,
        "password": PASSWORD
    })
    
    if login_response.status_code != 200:
        print(f"登录失败: {login_response.status_code} - {login_response.text}")
        return
    
    login_data = login_response.json()
    token = login_data.get('token')
    if not token:
        print("登录失败：未获取到token")
        return
    
    print(f"登录成功，token: {token[:20]}...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 2. 获取收藏列表
    print("\n2. 获取收藏列表...")
    favorites_response = requests.get(f"{BASE_URL}/favorites", headers=headers)
    print(f"状态码: {favorites_response.status_code}")
    print(f"响应: {favorites_response.text}")
    
    # 3. 切换收藏状态（假设文件ID为9）
    print("\n3. 切换收藏状态...")
    toggle_response = requests.post(f"{BASE_URL}/favorites/toggle", 
                                   headers=headers,
                                   json={"file_id": 9})
    print(f"状态码: {toggle_response.status_code}")
    print(f"响应: {toggle_response.text}")
    
    # 4. 再次获取收藏列表
    print("\n4. 再次获取收藏列表...")
    favorites_response2 = requests.get(f"{BASE_URL}/favorites", headers=headers)
    print(f"状态码: {favorites_response2.status_code}")
    print(f"响应: {favorites_response2.text}")
    
    # 5. 检查收藏状态
    print("\n5. 检查收藏状态...")
    status_response = requests.post(f"{BASE_URL}/favorites/status",
                                   headers=headers,
                                   json={"file_ids": [9, 10, 11]})
    print(f"状态码: {status_response.status_code}")
    print(f"响应: {status_response.text}")

if __name__ == "__main__":
    test_favorites_api()
