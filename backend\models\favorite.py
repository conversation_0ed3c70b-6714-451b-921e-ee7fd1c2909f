#!/usr/bin/env python3
"""
收藏功能数据模型
"""

from sqlalchemy import Column, Integer, ForeignKey, DateTime, Text, Boolean, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class UserFavorite(Base):
    """用户收藏模型"""
    
    __tablename__ = 'user_favorites'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='用户ID')
    file_id = Column(Integer, ForeignKey('shared_files.id'), nullable=False, comment='文件ID')
    
    # 收藏信息
    favorited_at = Column(DateTime, default=func.now(), nullable=False, comment='收藏时间')
    notes = Column(Text, nullable=True, comment='收藏备注')
    
    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment='是否有效')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关联关系
    user = relationship("User", backref="favorites")
    file = relationship("SharedFile", backref="favorited_by")
    
    # 索引
    __table_args__ = (
        Index('idx_user_file', 'user_id', 'file_id', unique=True),  # 用户-文件唯一索引
        Index('idx_user_favorited', 'user_id', 'favorited_at'),     # 用户收藏时间索引
        Index('idx_file_favorited', 'file_id', 'favorited_at'),     # 文件收藏时间索引
        Index('idx_active_favorites', 'is_active', 'favorited_at'), # 有效收藏索引
    )
    
    def __init__(self, user_id: int, file_id: int, **kwargs):
        self.user_id = user_id
        self.file_id = file_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'file_id': self.file_id,
            'favorited_at': self.favorited_at.isoformat() if self.favorited_at else None,
            'notes': self.notes,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            # 关联文件信息
            'file': {
                'id': self.file.id,
                'filename': self.file.filename,
                'relative_path': self.file.relative_path,
                'file_size': self.file.file_size,
                'mime_type': self.file.mime_type,
                'is_image': self.file.is_image,
                'has_thumbnail': self.file.has_thumbnail,
                'thumbnail_path': self.file.thumbnail_path,
                'folder_id': self.file.folder_id,
                'folder_name': self.file.folder.name if self.file.folder else None
            } if self.file else None
        }
    
    def __repr__(self):
        return f"<UserFavorite(id={self.id}, user_id={self.user_id}, file_id={self.file_id})>"


class FavoriteFolder(Base):
    """收藏夹分组模型"""
    
    __tablename__ = 'favorite_folders'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='用户ID')
    
    # 分组信息
    name = Column(Text, nullable=False, comment='分组名称')
    description = Column(Text, nullable=True, comment='分组描述')
    color = Column(Text, nullable=True, comment='分组颜色')
    icon = Column(Text, nullable=True, comment='分组图标')
    
    # 排序和状态
    sort_order = Column(Integer, default=0, comment='排序顺序')
    is_default = Column(Boolean, default=False, comment='是否默认分组')
    is_active = Column(Boolean, default=True, comment='是否有效')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关联关系
    user = relationship("User", backref="favorite_folders")
    
    # 索引
    __table_args__ = (
        Index('idx_user_folder_name', 'user_id', 'name'),
        Index('idx_user_sort', 'user_id', 'sort_order'),
    )
    
    def __init__(self, user_id: int, name: str, **kwargs):
        self.user_id = user_id
        self.name = name
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'description': self.description,
            'color': self.color,
            'icon': self.icon,
            'sort_order': self.sort_order,
            'is_default': self.is_default,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f"<FavoriteFolder(id={self.id}, user_id={self.user_id}, name='{self.name}')>"


class FavoriteFolderItem(Base):
    """收藏夹分组项目关联模型"""
    
    __tablename__ = 'favorite_folder_items'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    folder_id = Column(Integer, ForeignKey('favorite_folders.id'), nullable=False, comment='收藏夹分组ID')
    favorite_id = Column(Integer, ForeignKey('user_favorites.id'), nullable=False, comment='收藏项ID')
    
    # 排序信息
    sort_order = Column(Integer, default=0, comment='在分组中的排序')
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    
    # 关联关系
    folder = relationship("FavoriteFolder", backref="items")
    favorite = relationship("UserFavorite", backref="folder_items")
    
    # 索引
    __table_args__ = (
        Index('idx_folder_favorite', 'folder_id', 'favorite_id', unique=True),
        Index('idx_folder_sort', 'folder_id', 'sort_order'),
    )
    
    def __init__(self, folder_id: int, favorite_id: int, **kwargs):
        self.folder_id = folder_id
        self.favorite_id = favorite_id
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'folder_id': self.folder_id,
            'favorite_id': self.favorite_id,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f"<FavoriteFolderItem(id={self.id}, folder_id={self.folder_id}, favorite_id={self.favorite_id})>"
