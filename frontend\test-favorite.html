<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #eee;
            border-radius: 5px;
            background: #fafafa;
        }
        .file-icon {
            margin-right: 15px;
            font-size: 24px;
            color: #666;
        }
        .file-name {
            flex: 1;
            font-weight: bold;
        }
        .file-actions {
            display: flex;
            gap: 10px;
        }
        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
            transition: all 0.2s;
        }
        .action-btn:hover {
            background: #0056b3;
        }
        .action-btn.favorited {
            background: #ffc107;
            color: #000;
        }
        .action-btn.favorited:hover {
            background: #e0a800;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .controls {
            margin-bottom: 20px;
        }
        .controls button {
            margin-right: 10px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #28a745;
            color: white;
        }
        .controls button:hover {
            background: #218838;
        }
        .favorites-list {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-star"></i> 收藏功能测试</h1>
        
        <div class="controls">
            <button onclick="clearFavorites()">清空收藏</button>
            <button onclick="showFavorites()">显示收藏列表</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>测试文件列表</h3>
            <div id="test-files">
                <!-- 测试文件将在这里生成 -->
            </div>
        </div>

        <div class="test-section">
            <h3>收藏列表</h3>
            <div id="favorites-display" class="favorites-list">
                <p>暂无收藏</p>
            </div>
        </div>

        <div class="test-section">
            <h3>操作日志</h3>
            <div id="log-area" class="log-area"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/file-manager.js"></script>

    <script>
        // 测试数据
        const testFiles = [
            { id: 'test1', name: 'image1.jpg', type: 'file', size: 1024000 },
            { id: 'test2', name: 'image2.png', type: 'file', size: 2048000 },
            { id: 'test3', name: 'image3.gif', type: 'file', size: 512000 },
            { id: 'test4', name: 'document.pdf', type: 'file', size: 3072000 },
            { id: 'test5', name: 'photo.tiff', type: 'file', size: 4096000 }
        ];

        // 创建文件管理器实例
        const fileManager = new FileManager();
        
        // 重写一些方法以适应测试环境
        fileManager.showToast = function(message, type) {
            log(`[${type.toUpperCase()}] ${message}`);
        };

        // 日志函数
        function log(message) {
            const logArea = document.getElementById('log-area');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            document.getElementById('log-area').innerHTML = '';
        }

        // 清空收藏
        function clearFavorites() {
            localStorage.removeItem('fileShareFavorites');
            log('已清空所有收藏');
            showFavorites();
            updateFavoriteButtons();
        }

        // 显示收藏列表
        function showFavorites() {
            const favorites = fileManager.getFavorites();
            const display = document.getElementById('favorites-display');
            
            if (favorites.length === 0) {
                display.innerHTML = '<p>暂无收藏</p>';
            } else {
                display.innerHTML = '<h4>收藏的文件：</h4>' + 
                    favorites.map(fav => `
                        <div style="margin: 5px 0; padding: 5px; background: white; border-radius: 3px;">
                            <strong>${fav.name}</strong> - ${Utils.formatFileSize(fav.size)}
                            <small style="color: #666;">(收藏于: ${new Date(fav.favorited_at).toLocaleString()})</small>
                        </div>
                    `).join('');
            }
            log(`当前收藏数量: ${favorites.length}`);
        }

        // 更新收藏按钮状态
        function updateFavoriteButtons() {
            testFiles.forEach(file => {
                const isFavorited = fileManager.isFileFavorited(file.id);
                fileManager.updateFavoriteButtonState(file.id, isFavorited);
            });
        }

        // 创建测试文件项
        function createTestFileItem(file) {
            const isFavorited = fileManager.isFileFavorited(file.id);
            
            return `
                <div class="file-item" data-file-id="${file.id}" data-file-type="${file.type}">
                    <div class="file-icon">
                        <i class="fas fa-file-image"></i>
                    </div>
                    <div class="file-name">${file.name}</div>
                    <div class="file-actions">
                        <button class="action-btn ${isFavorited ? 'favorited' : ''}" 
                                data-action="favorite" 
                                title="${isFavorited ? '取消收藏' : '收藏'}"
                                onclick="testFavorite('${file.id}')">
                            <i class="${isFavorited ? 'fas' : 'far'} fa-star"></i>
                        </button>
                    </div>
                </div>
            `;
        }

        // 测试收藏功能
        async function testFavorite(fileId) {
            log(`点击收藏按钮: ${fileId}`);
            
            const file = testFiles.find(f => f.id === fileId);
            if (!file) {
                log(`错误: 未找到文件 ${fileId}`);
                return;
            }

            // 模拟文件管理器的files数组
            fileManager.files = testFiles;
            fileManager.currentFolderId = null;
            fileManager.currentFolder = { name: '测试文件夹' };

            try {
                await fileManager.favoriteFile(fileId);
                showFavorites();
                log(`收藏操作完成: ${file.name}`);
            } catch (error) {
                log(`收藏操作失败: ${error.message}`);
            }
        }

        // 初始化测试页面
        function initTest() {
            const container = document.getElementById('test-files');
            container.innerHTML = testFiles.map(createTestFileItem).join('');
            
            showFavorites();
            log('测试页面初始化完成');
            log('点击星星图标来测试收藏功能');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
