<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态面板移除验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover { background-color: #0056b3; }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .element-check {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .element-check:last-child { border-bottom: none; }
        .check-result {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .removed { background-color: #d4edda; color: #155724; }
        .exists { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🗑️ 系统状态面板移除验证</h1>
        <p>此页面用于验证左下角系统状态面板是否已完全移除。</p>
        
        <div class="controls">
            <button class="btn" onclick="runFullTest()">运行完整测试</button>
            <button class="btn" onclick="checkMainPage()">检查主页面</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results" class="test-results" style="display: none;">
            <h3>🔍 检测结果</h3>
            <div id="elementChecks"></div>
            <div id="overallResult"></div>
        </div>
    </div>

    <script>
        function clearResults() {
            document.getElementById('results').style.display = 'none';
        }

        function showResults() {
            document.getElementById('results').style.display = 'block';
        }

        function addElementCheck(elementName, selector, exists) {
            const container = document.getElementById('elementChecks');
            const item = document.createElement('div');
            item.className = 'element-check';
            
            const resultClass = exists ? 'exists' : 'removed';
            const resultText = exists ? '❌ 仍存在' : '✅ 已移除';
            
            item.innerHTML = `
                <span><strong>${elementName}</strong> (${selector})</span>
                <span class="check-result ${resultClass}">${resultText}</span>
            `;
            
            container.appendChild(item);
            return !exists; // 返回true表示测试通过（元素不存在）
        }

        function runFullTest() {
            clearResults();
            showResults();
            
            const container = document.getElementById('elementChecks');
            container.innerHTML = '';
            
            console.log('🔍 开始检测系统状态面板元素...');
            
            // 检测需要移除的元素
            const elementsToCheck = [
                { name: '在线用户计数', selector: '#online-users-count' },
                { name: '服务器状态', selector: '#server-status' },
                { name: '服务器运行时间', selector: '#server-uptime' },
                { name: '系统状态容器', selector: '.system-status' },
                { name: '状态项目', selector: '.status-item' },
                { name: '状态信息', selector: '.status-info' },
                { name: '状态标签', selector: '.status-label' },
                { name: '状态值', selector: '.status-value' }
            ];
            
            let passedTests = 0;
            let totalTests = elementsToCheck.length;
            
            elementsToCheck.forEach(element => {
                const exists = document.querySelector(element.selector) !== null;
                const passed = addElementCheck(element.name, element.selector, exists);
                if (passed) passedTests++;
                
                console.log(`${element.name} (${element.selector}): ${exists ? '仍存在' : '已移除'}`);
            });
            
            // 检查侧边栏中是否有"系统状态"标题
            const sidebarSections = document.querySelectorAll('.sidebar-section h3');
            let hasSystemStatusTitle = false;
            sidebarSections.forEach(h3 => {
                if (h3.textContent.includes('系统状态')) {
                    hasSystemStatusTitle = true;
                }
            });
            
            const titlePassed = addElementCheck('系统状态标题', '.sidebar-section h3', hasSystemStatusTitle);
            if (titlePassed) passedTests++;
            totalTests++;
            
            // 显示总体结果
            const overallResult = document.getElementById('overallResult');
            const successRate = (passedTests / totalTests * 100).toFixed(1);
            
            if (passedTests === totalTests) {
                overallResult.innerHTML = `
                    <div class="status success">
                        ✅ 完美！所有系统状态面板元素已完全移除 (${passedTests}/${totalTests})
                    </div>
                `;
                console.log('✅ 系统状态面板移除测试：完全通过');
            } else {
                overallResult.innerHTML = `
                    <div class="status warning">
                        ⚠️ 部分元素仍存在，移除成功率: ${successRate}% (${passedTests}/${totalTests})
                    </div>
                `;
                console.log(`⚠️ 系统状态面板移除测试：部分通过 (${successRate}%)`);
            }
        }

        function checkMainPage() {
            const mainPageUrl = 'index.html';
            window.open(mainPageUrl, '_blank');
            
            setTimeout(() => {
                const result = document.getElementById('overallResult');
                result.innerHTML = `
                    <div class="status info">
                        📋 已打开主页面，请手动检查左下角是否还有系统状态面板
                    </div>
                `;
                showResults();
            }, 500);
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            console.log('系统状态面板移除验证页面已加载');
            
            // 延迟1秒后自动运行测试
            setTimeout(() => {
                runFullTest();
            }, 1000);
        });
    </script>
</body>
</html>
