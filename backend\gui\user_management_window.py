#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, Optional

class UserManagementWindow:
    """用户管理窗口类"""
    
    def __init__(self, parent, server_instance):
        self.parent = parent
        self.server = server_instance
        self.window = None
        self.users_tree = None
        self.current_users = []
        
    def show(self):
        """显示用户管理窗口"""
        if self.window:
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent)
        self.window.title("用户管理")
        self.window.geometry("1000x700")
        self.window.transient(self.parent)
        
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.create_widgets()
        self.load_users()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="用户管理", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 工具栏
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar_frame, text="添加用户", command=self.add_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="编辑用户", command=self.edit_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="删除用户", command=self.delete_user).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="刷新", command=self.load_users).pack(side=tk.LEFT, padx=(0, 5))
        
        # 搜索框
        search_frame = ttk.Frame(toolbar_frame)
        search_frame.pack(side=tk.RIGHT)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=(0, 5))
        search_entry.bind('<Return>', lambda e: self.search_users())
        ttk.Button(search_frame, text="搜索", command=self.search_users).pack(side=tk.LEFT)
        
        # 用户列表
        list_frame = ttk.LabelFrame(main_frame, text="用户列表")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建树形视图
        columns = ("ID", "用户名", "姓名", "邮箱", "用户组", "状态", "最后登录", "登录次数")
        self.users_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {"ID": 50, "用户名": 100, "姓名": 100, "邮箱": 150, 
                        "用户组": 80, "状态": 80, "最后登录": 150, "登录次数": 80}
        
        for col in columns:
            self.users_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.users_tree.column(col, width=column_widths.get(col, 100))
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        scrollbar_x = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 绑定双击事件
        self.users_tree.bind('<Double-1>', lambda e: self.edit_user())
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        self.count_label = ttk.Label(status_frame, text="用户数: 0")
        self.count_label.pack(side=tk.RIGHT)
    
    def load_users(self):
        """加载用户列表"""
        try:
            self.status_label.config(text="正在加载用户...")
            
            # 清空现有数据
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # 获取用户服务
            user_service = self.server.services.get('user')
            if not user_service:
                self.status_label.config(text="用户服务不可用")
                return
            
            # 获取用户列表
            result = user_service.get_user_list(page=1, page_size=1000)
            
            if result.get('success', False):
                users = result.get('users', [])
                self.current_users = users
                
                # 添加用户到树形视图
                for user in users:
                    status = "正常"
                    if user.get('is_banned', False):
                        status = "禁用"
                    elif not user.get('is_active', True):
                        status = "未激活"
                    
                    last_login = user.get('last_login', '')
                    if last_login:
                        last_login = last_login[:19]  # 只显示日期时间部分
                    
                    self.users_tree.insert('', 'end', values=(
                        user.get('id', ''),
                        user.get('username', ''),
                        user.get('full_name', ''),
                        user.get('email', ''),
                        user.get('user_group', ''),
                        status,
                        last_login,
                        user.get('login_count', 0)
                    ))
                
                self.count_label.config(text=f"用户数: {len(users)}")
                self.status_label.config(text="加载完成")
            else:
                self.status_label.config(text=f"加载失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            self.status_label.config(text=f"加载失败: {e}")
            messagebox.showerror("错误", f"加载用户列表失败: {e}")
    
    def search_users(self):
        """搜索用户"""
        try:
            search_query = self.search_var.get().strip()
            
            if not search_query:
                self.load_users()
                return
            
            self.status_label.config(text="正在搜索...")
            
            # 清空现有数据
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # 在当前用户列表中搜索
            filtered_users = []
            for user in self.current_users:
                if (search_query.lower() in user.get('username', '').lower() or
                    search_query.lower() in user.get('full_name', '').lower() or
                    search_query.lower() in user.get('email', '').lower()):
                    filtered_users.append(user)
            
            # 添加搜索结果到树形视图
            for user in filtered_users:
                status = "正常"
                if user.get('is_banned', False):
                    status = "禁用"
                elif not user.get('is_active', True):
                    status = "未激活"
                
                last_login = user.get('last_login', '')
                if last_login:
                    last_login = last_login[:19]
                
                self.users_tree.insert('', 'end', values=(
                    user.get('id', ''),
                    user.get('username', ''),
                    user.get('full_name', ''),
                    user.get('email', ''),
                    user.get('user_group', ''),
                    status,
                    last_login,
                    user.get('login_count', 0)
                ))
            
            self.count_label.config(text=f"搜索结果: {len(filtered_users)}")
            self.status_label.config(text="搜索完成")
            
        except Exception as e:
            self.status_label.config(text=f"搜索失败: {e}")
    
    def add_user(self):
        """添加用户"""
        dialog = UserEditDialog(self.window, "添加用户")
        result = dialog.show()
        
        if result:
            try:
                user_service = self.server.services.get('user')
                if user_service:
                    create_result = user_service.create_user(**result)
                    
                    if create_result.get('success', False):
                        messagebox.showinfo("成功", "用户创建成功")
                        self.load_users()
                    else:
                        messagebox.showerror("错误", f"创建用户失败: {create_result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "用户服务不可用")
                    
            except Exception as e:
                messagebox.showerror("错误", f"创建用户失败: {e}")
    
    def edit_user(self):
        """编辑用户"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的用户")
            return
        
        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        
        # 查找用户数据
        user_data = None
        for user in self.current_users:
            if user.get('id') == user_id:
                user_data = user
                break
        
        if not user_data:
            messagebox.showerror("错误", "找不到用户数据")
            return
        
        dialog = UserEditDialog(self.window, "编辑用户", user_data)
        result = dialog.show()
        
        if result:
            try:
                user_service = self.server.services.get('user')
                if user_service:
                    update_result = user_service.update_user(user_id, result)
                    
                    if update_result.get('success', False):
                        messagebox.showinfo("成功", "用户更新成功")
                        self.load_users()
                    else:
                        messagebox.showerror("错误", f"更新用户失败: {update_result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "用户服务不可用")
                    
            except Exception as e:
                messagebox.showerror("错误", f"更新用户失败: {e}")
    
    def delete_user(self):
        """删除用户"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的用户")
            return
        
        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]
        
        if messagebox.askyesno("确认", f"确定要删除用户 '{username}' 吗？\n此操作不可撤销！"):
            try:
                user_service = self.server.services.get('user')
                if user_service:
                    delete_result = user_service.delete_user(user_id)
                    
                    if delete_result.get('success', False):
                        messagebox.showinfo("成功", "用户删除成功")
                        self.load_users()
                    else:
                        messagebox.showerror("错误", f"删除用户失败: {delete_result.get('error', '未知错误')}")
                else:
                    messagebox.showerror("错误", "用户服务不可用")
                    
            except Exception as e:
                messagebox.showerror("错误", f"删除用户失败: {e}")
    
    def sort_by_column(self, column):
        """按列排序"""
        # 这里可以实现排序功能
        pass
    
    def on_closing(self):
        """窗口关闭事件"""
        self.window.destroy()
        self.window = None

class UserEditDialog:
    """用户编辑对话框"""
    
    def __init__(self, parent, title, user_data=None):
        self.parent = parent
        self.title = title
        self.user_data = user_data or {}
        self.result = None
        self.dialog = None
    
    def show(self):
        """显示对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x500")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self.create_widgets()
        
        # 等待对话框关闭
        self.dialog.wait_window()
        
        return self.result
    
    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 用户名
        ttk.Label(main_frame, text="用户名:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar(value=self.user_data.get('username', ''))
        username_entry = ttk.Entry(main_frame, textvariable=self.username_var, width=30)
        username_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(main_frame, textvariable=self.password_var, show="*", width=30)
        password_entry.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # 姓名
        ttk.Label(main_frame, text="姓名:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.full_name_var = tk.StringVar(value=self.user_data.get('full_name', ''))
        full_name_entry = ttk.Entry(main_frame, textvariable=self.full_name_var, width=30)
        full_name_entry.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 邮箱
        ttk.Label(main_frame, text="邮箱:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar(value=self.user_data.get('email', ''))
        email_entry = ttk.Entry(main_frame, textvariable=self.email_var, width=30)
        email_entry.grid(row=3, column=1, sticky=tk.W, pady=5)
        
        # 用户组
        ttk.Label(main_frame, text="用户组:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.user_group_var = tk.StringVar(value=self.user_data.get('user_group', 'user'))
        user_group_combo = ttk.Combobox(main_frame, textvariable=self.user_group_var, 
                                       values=['admin', 'user', 'guest'], width=27)
        user_group_combo.grid(row=4, column=1, sticky=tk.W, pady=5)
        
        # 是否管理员
        self.is_admin_var = tk.BooleanVar(value=self.user_data.get('is_admin', False))
        admin_check = ttk.Checkbutton(main_frame, text="管理员", variable=self.is_admin_var)
        admin_check.grid(row=5, column=1, sticky=tk.W, pady=5)
        
        # 是否激活
        self.is_active_var = tk.BooleanVar(value=self.user_data.get('is_active', True))
        active_check = ttk.Checkbutton(main_frame, text="激活", variable=self.is_active_var)
        active_check.grid(row=6, column=1, sticky=tk.W, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
    
    def ok_clicked(self):
        """确定按钮点击"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username:
            messagebox.showerror("错误", "用户名不能为空")
            return
        
        if not self.user_data and not password:  # 新用户必须有密码
            messagebox.showerror("错误", "密码不能为空")
            return
        
        self.result = {
            'username': username,
            'full_name': self.full_name_var.get().strip(),
            'email': self.email_var.get().strip(),
            'user_group': self.user_group_var.get(),
            'is_admin': self.is_admin_var.get(),
            'is_active': self.is_active_var.get()
        }
        
        if password:  # 只有在输入密码时才更新
            self.result['password'] = password
        
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按钮点击"""
        self.result = None
        self.dialog.destroy()
