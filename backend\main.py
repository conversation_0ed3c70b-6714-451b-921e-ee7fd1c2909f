#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级文件共享系统 - 服务端主程序
作者: 系统开发团队
版本: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from pathlib import Path
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模块时添加错误处理
try:
    from config.settings import SystemSettings
    from utils.logger import setup_logger

    # 可选导入，如果失败则使用简化版本
    try:
        from config.database import DatabaseManager
        HAS_DATABASE = True
    except ImportError as e:
        print(f"数据库模块导入失败: {e}")
        HAS_DATABASE = False

    try:
        from gui.main_window import MainWindow
        HAS_GUI = True
    except ImportError as e:
        print(f"GUI模块导入失败: {e}")
        HAS_GUI = False

    try:
        from services.file_service import FileService
        HAS_FILE_SERVICE = True
    except ImportError as e:
        print(f"文件服务模块导入失败: {e}")
        HAS_FILE_SERVICE = False

    try:
        from services.search_service_simple import SearchService
        HAS_SEARCH_SERVICE = True
    except ImportError as e:
        print(f"搜索服务模块导入失败: {e}")
        HAS_SEARCH_SERVICE = False

    try:
        from services.monitoring_service import MonitoringService
        HAS_MONITORING_SERVICE = True
    except ImportError as e:
        print(f"监控服务模块导入失败: {e}")
        HAS_MONITORING_SERVICE = False

    try:
        from api.server import APIServer
        HAS_API_SERVER = True
    except ImportError as e:
        print(f"API服务器模块导入失败: {e}")
        HAS_API_SERVER = False

except ImportError as e:
    print(f"核心模块导入失败: {e}")
    sys.exit(1)

class FileShareServer:
    """文件共享系统服务端主类"""

    def __init__(self):
        self.logger = setup_logger("FileShareServer")
        self.settings = SystemSettings()

        # 加载配置
        self.config = {
            'download': {
                'encryption_after_downloads': 3,  # 默认3次下载后加密
                'password_request_limit': 5,      # 默认密码申请限制5次
                'max_file_size': 100 * 1024 * 1024,  # 默认100MB
                'allowed_extensions': None,       # 默认允许所有扩展名
                'temp_cleanup_hours': 24         # 临时文件清理时间（小时）
            }
        }

        # 初始化数据库管理器（如果可用）
        if HAS_DATABASE:
            try:
                self.db_manager = DatabaseManager()
                self.logger.info("数据库管理器初始化成功")
            except Exception as e:
                self.logger.error(f"数据库管理器初始化失败: {e}")
                self.db_manager = None
        else:
            self.db_manager = None
            self.logger.warning("数据库模块不可用，使用简化模式")

        self.api_server = None
        self.services = {}
        self.running = False

        # 初始化GUI
        self.root = tk.Tk()
        if HAS_GUI:
            try:
                self.main_window = MainWindow(self.root, self)
                self.logger.info("GUI界面初始化成功")
            except Exception as e:
                self.logger.error(f"GUI界面初始化失败: {e}")
                self.main_window = None
        else:
            self.main_window = None
            self.logger.warning("GUI模块不可用")

        self.logger.info("文件共享系统服务端初始化完成")
    
    def initialize_services(self):
        """初始化所有服务"""
        try:
            # 初始化数据库（如果可用）
            if self.db_manager:
                try:
                    self.db_manager.initialize()
                    self.logger.info("数据库初始化成功")
                except Exception as e:
                    self.logger.error(f"数据库初始化失败: {e}")
                    if messagebox:
                        messagebox.showerror("数据库错误", f"数据库初始化失败: {e}\n\n系统将以简化模式运行")
                    self.db_manager = None

            # 初始化用户服务
            try:
                from services.user_service import UserService
                self.services['user'] = UserService(self.db_manager)
                self.logger.info("用户服务初始化成功")
            except Exception as e:
                self.logger.error(f"用户服务初始化失败: {e}")

            # 初始化文件服务
            if HAS_FILE_SERVICE:
                try:
                    self.services['file'] = FileService(self.db_manager)
                    self.logger.info("文件服务初始化成功")
                except Exception as e:
                    self.logger.error(f"文件服务初始化失败: {e}")

            # 初始化搜索服务
            if HAS_SEARCH_SERVICE:
                try:
                    self.services['search'] = SearchService(self.db_manager)
                    self.logger.info("搜索服务初始化成功")
                except Exception as e:
                    self.logger.error(f"搜索服务初始化失败: {e}")

            # 初始化监控服务
            if HAS_MONITORING_SERVICE:
                try:
                    self.services['monitoring'] = MonitoringService(self.db_manager)
                    self.logger.info("监控服务初始化成功")
                except Exception as e:
                    self.logger.error(f"监控服务初始化失败: {e}")

            # 初始化缩略图服务
            try:
                from services.thumbnail_service import ThumbnailService
                self.services['thumbnail'] = ThumbnailService()
                self.logger.info("缩略图服务初始化成功")
            except Exception as e:
                self.logger.error(f"缩略图服务初始化失败: {e}")

            # 初始化加密服务
            try:
                from services.encryption_service import EncryptionService
                self.services['encryption'] = EncryptionService(db_manager=self.db_manager)
                self.logger.info("加密服务初始化成功")
            except Exception as e:
                self.logger.error(f"加密服务初始化失败: {e}")

            # 初始化下载服务
            try:
                from services.download_service import DownloadService
                self.services['download'] = DownloadService(
                    db_manager=self.db_manager,
                    config=self.config
                )
                self.logger.info("下载服务初始化成功")
            except Exception as e:
                self.logger.error(f"下载服务初始化失败: {e}")

            # 初始化收藏服务
            try:
                from services.favorite_service_simple import SimpleFavoriteService
                self.services['favorite'] = SimpleFavoriteService()
                self.logger.info("收藏服务初始化成功")
            except Exception as e:
                self.logger.error(f"收藏服务初始化失败: {e}")
                # 如果简化服务也失败，尝试原始服务
                try:
                    from services.favorite_service import FavoriteService
                    self.services['favorite'] = FavoriteService(self.db_manager)
                    self.logger.info("收藏服务（原始版本）初始化成功")
                except Exception as e2:
                    self.logger.error(f"收藏服务（原始版本）初始化也失败: {e2}")

            # 初始化API服务器
            if HAS_API_SERVER:
                try:
                    self.api_server = APIServer(self.services, self.settings)
                    self.logger.info("API服务器初始化成功")
                except Exception as e:
                    self.logger.error(f"API服务器初始化失败: {e}")

            self.logger.info(f"服务初始化完成，已启动 {len(self.services)} 个服务")
            return True

        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            if messagebox:
                messagebox.showerror("错误", f"服务初始化失败: {e}")
            return False
    
    def start_server(self):
        """启动服务器"""
        if self.running:
            return
            
        try:
            if not self.initialize_services():
                return
                
            # 在新线程中启动API服务器
            server_thread = threading.Thread(
                target=self.api_server.run,
                daemon=True
            )
            server_thread.start()

            # 启动前端服务器
            self.start_frontend_server()

            self.running = True
            self.main_window.update_status("服务器运行中", "green")
            self.logger.info("文件共享服务器启动成功")
            
        except Exception as e:
            self.logger.error(f"服务器启动失败: {e}")
            messagebox.showerror("错误", f"服务器启动失败: {e}")

    def start_frontend_server(self):
        """启动前端服务器"""
        try:
            import http.server
            import socketserver
            import os
            from pathlib import Path

            # 前端文件夹路径
            frontend_dir = Path(__file__).parent.parent / "frontend"

            # 如果前端文件夹不存在，创建它
            if not frontend_dir.exists():
                self.create_frontend_structure()

            # 获取前端端口（从配置文件读取）
            frontend_port = self.settings.get('server.frontend_port', 8082)

            # 创建自定义HTTP处理器，指定前端目录
            class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, directory=str(frontend_dir), **kwargs)

                def end_headers(self):
                    # 添加CORS头
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
                    super().end_headers()

            # 创建HTTP服务器
            httpd = socketserver.TCPServer(("", frontend_port), CustomHTTPRequestHandler)

            # 在新线程中启动前端服务器
            frontend_thread = threading.Thread(
                target=httpd.serve_forever,
                daemon=True
            )
            frontend_thread.start()

            self.frontend_server = httpd
            self.logger.info(f"前端服务器启动成功: http://localhost:{frontend_port}")

            # 自动打开登录页面
            try:
                import webbrowser
                webbrowser.open(f'http://localhost:{frontend_port}/login.html')
                self.logger.info("已自动打开登录页面")
            except Exception as e:
                self.logger.warning(f"无法自动打开浏览器: {e}")

        except Exception as e:
            self.logger.error(f"前端服务器启动失败: {e}")

    def create_frontend_structure(self):
        """创建前端文件结构"""
        try:
            frontend_dir = Path(__file__).parent.parent / "frontend"
            frontend_dir.mkdir(exist_ok=True)

            # 创建基本文件结构
            (frontend_dir / "css").mkdir(exist_ok=True)
            (frontend_dir / "js").mkdir(exist_ok=True)
            (frontend_dir / "assets").mkdir(exist_ok=True)
            (frontend_dir / "assets" / "images").mkdir(exist_ok=True)

            # 检查是否已有前端文件
            index_file = frontend_dir / "index.html"
            if not index_file.exists():
                self.logger.info("前端文件不存在，请确保前端文件已正确创建")

            self.logger.info("前端文件结构创建成功")

        except Exception as e:
            self.logger.error(f"创建前端文件结构失败: {e}")

    def stop_server(self):
        """停止服务器"""
        if not self.running:
            return
            
        try:
            if self.api_server:
                self.api_server.stop()
            
            self.running = False
            self.main_window.update_status("服务器已停止", "red")
            self.logger.info("文件共享服务器已停止")
            
        except Exception as e:
            self.logger.error(f"服务器停止失败: {e}")
    
    def get_server_status(self):
        """获取服务器状态"""
        return {
            'running': self.running,
            'services': len(self.services),
            'uptime': time.time() - self.start_time if hasattr(self, 'start_time') else 0
        }
    
    def run(self):
        """运行主程序"""
        try:
            self.start_time = time.time()

            if self.main_window and self.root:
                # GUI模式
                self.logger.info("启动GUI模式")

                # 设置窗口关闭事件
                self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

                # 启动GUI主循环
                self.root.mainloop()
            else:
                # 命令行模式
                self.logger.info("启动命令行模式")
                print("企业级文件共享系统 - 命令行模式")
                print("=" * 50)

                # 自动启动服务器
                if self.start_server():
                    print("服务器启动成功！")
                    print("按 Ctrl+C 停止服务器")

                    try:
                        while True:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        print("\n正在停止服务器...")
                        self.stop_server()
                        print("服务器已停止")
                else:
                    print("服务器启动失败")

        except Exception as e:
            self.logger.error(f"程序运行错误: {e}")
            print(f"程序运行错误: {e}")
            traceback.print_exc()

    def on_closing(self):
        """窗口关闭事件处理"""
        try:
            if messagebox.askokcancel("退出", "确定要退出文件共享系统吗？"):
                self.stop_server()
                self.root.destroy()
        except Exception as e:
            self.logger.error(f"关闭程序时出错: {e}")
            self.root.destroy()

def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("错误: 需要Python 3.7或更高版本")
            sys.exit(1)

        # 检查命令行参数
        api_only = '--api-only' in sys.argv or '--no-gui' in sys.argv

        if api_only:
            # 只启动API服务器，不启动GUI
            print("🚀 启动API服务器模式...")
            run_api_only()
        else:
            # 创建并运行完整服务器（包含GUI）
            server = FileShareServer()
            server.run()

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)

def run_api_only():
    """只运行API服务器"""
    try:
        from config.database import DatabaseManager
        from services.file_service import FileService
        from services.user_service import UserService
        from services.monitoring_service import MonitoringService
        from services.thumbnail_service import ThumbnailService
        from api.server import APIServer

        print("📊 初始化数据库...")
        db_manager = DatabaseManager()
        db_manager.initialize()

        print("🔧 初始化服务...")
        services = {
            'file': FileService(db_manager),
            'user': UserService(db_manager),
            'monitoring': MonitoringService(db_manager),
            'thumbnail': ThumbnailService()
        }

        # 初始化收藏服务
        try:
            from services.favorite_service_simple import SimpleFavoriteService
            services['favorite'] = SimpleFavoriteService()
            print("✅ 收藏服务初始化成功")
        except Exception as e:
            print(f"⚠️ 收藏服务初始化失败: {e}")
            # 如果简化服务也失败，尝试原始服务
            try:
                from services.favorite_service import FavoriteService
                services['favorite'] = FavoriteService(db_manager)
                print("✅ 收藏服务（原始版本）初始化成功")
            except Exception as e2:
                print(f"⚠️ 收藏服务（原始版本）初始化也失败: {e2}")

        print("🌐 启动API服务器...")
        settings = SystemSettings()
        api_server = APIServer(services, settings)

        print("✅ API服务器启动成功！")
        api_port = settings.get('server.port', 8080)
        print(f"📍 访问地址: http://localhost:{api_port}")
        print(f"🔧 健康检查: http://localhost:{api_port}/api/health")
        print(f"📁 文件API: http://localhost:{api_port}/api/files")
        print("\n按 Ctrl+C 停止服务器")

        # 运行服务器
        api_server.run()

    except Exception as e:
        print(f"❌ API服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
