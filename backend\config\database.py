#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接和管理模块
"""

import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
import logging
from typing import Optional

# 创建基础模型类
Base = declarative_base()

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: dict = None):
        self.config = config or {
            'host': 'localhost',
            'port': 3306,
            'username': 'root',
            'password': '123456',
            'database': 'file_share_system',
            'charset': 'utf8mb4'
        }
        
        self.engine = None
        self.SessionLocal = None
        self.logger = logging.getLogger(__name__)
    
    def initialize(self):
        """初始化数据库连接"""
        try:
            # 首先创建数据库（如果不存在）
            self.create_database_if_not_exists()

            # 创建数据库连接字符串
            db_url = (
                f"mysql+pymysql://{self.config['username']}:{self.config['password']}"
                f"@{self.config['host']}:{self.config['port']}"
                f"/{self.config['database']}?charset={self.config['charset']}"
            )

            # 创建引擎
            self.engine = create_engine(
                db_url,
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False
            )

            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )

            # 测试连接
            self.test_connection()

            # 创建数据库表
            self.create_tables()

            # 检查和修复表结构
            self.check_and_fix_table_structure()

            self.logger.info("数据库初始化成功")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def create_database_if_not_exists(self):
        """创建数据库（如果不存在）"""
        try:
            # 连接到MySQL服务器（不指定数据库）
            temp_url = (
                f"mysql+pymysql://{self.config['username']}:{self.config['password']}"
                f"@{self.config['host']}:{self.config['port']}"
                f"?charset={self.config['charset']}"
            )
            
            temp_engine = create_engine(temp_url)
            
            with temp_engine.connect() as conn:
                # 检查数据库是否存在
                result = conn.execute(text(
                    f"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA "
                    f"WHERE SCHEMA_NAME = '{self.config['database']}'"
                ))
                
                if not result.fetchone():
                    # 创建数据库
                    conn.execute(text(
                        f"CREATE DATABASE {self.config['database']} "
                        f"CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
                    ))
                    conn.commit()
                    self.logger.info(f"数据库 {self.config['database']} 创建成功")
            
            temp_engine.dispose()
            
        except Exception as e:
            self.logger.error(f"创建数据库失败: {e}")
            raise
    
    def create_tables(self):
        """创建数据表"""
        try:
            # 导入所有模型
            from models.user import User
            from models.file_share import SharedFolder, SharedFile
            from models.activity_log import ActivityLog
            from models.permission import Permission, UserPermission
            from models.favorite import UserFavorite, FavoriteFolder, FavoriteFolderItem
            
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            self.logger.info("数据表创建成功")
            
        except Exception as e:
            self.logger.error(f"创建数据表失败: {e}")
            raise
    
    def test_connection(self):
        """测试数据库连接"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            self.logger.info("数据库连接测试成功")
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            raise

    def check_and_fix_table_structure(self):
        """检查和修复表结构"""
        try:
            with self.engine.connect() as conn:
                # 检查 download_records 表是否有 folder_id 字段
                result = conn.execute(text("""
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = 'file_share_system'
                    AND TABLE_NAME = 'download_records'
                    AND COLUMN_NAME = 'folder_id'
                """))

                if not result.fetchone():
                    self.logger.info("添加 download_records.folder_id 字段")
                    # 添加 folder_id 字段
                    conn.execute(text("""
                        ALTER TABLE download_records
                        ADD COLUMN folder_id INT NULL COMMENT '文件夹ID（文件夹下载时使用）'
                        AFTER file_id
                    """))

                    # 修改 file_id 为可空
                    conn.execute(text("""
                        ALTER TABLE download_records
                        MODIFY COLUMN file_id INT NULL COMMENT '文件ID（单文件/批量下载时使用）'
                    """))

                    # 添加索引
                    try:
                        conn.execute(text("""
                            CREATE INDEX idx_folder_download
                            ON download_records (folder_id, downloaded_at)
                        """))
                    except:
                        pass  # 索引可能已存在

                    conn.commit()
                    self.logger.info("download_records 表结构更新完成")

                # 更新现有记录的用户ID
                result = conn.execute(text("SELECT id FROM users WHERE username = 'fjj'"))
                user_row = result.fetchone()
                if user_row:
                    user_id = user_row[0]
                    result = conn.execute(text("""
                        UPDATE download_records
                        SET user_id = :user_id
                        WHERE user_id IS NULL
                    """), {"user_id": user_id})

                    if result.rowcount > 0:
                        conn.commit()
                        self.logger.info(f"更新了 {result.rowcount} 条下载记录的用户ID")

        except Exception as e:
            self.logger.error(f"表结构检查和修复失败: {e}")
            # 不抛出异常，允许系统继续运行
    
    @contextmanager
    def get_session(self):
        """获取数据库会话（上下文管理器）"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def get_session_direct(self):
        """直接获取数据库会话"""
        return self.SessionLocal()
    
    def execute_sql(self, sql: str, params: dict = None):
        """执行SQL语句"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(sql), params or {})
                conn.commit()
                return result
        except Exception as e:
            self.logger.error(f"SQL执行失败: {sql}, 错误: {e}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            self.logger.info("数据库连接已关闭")
    
    def backup_database(self, backup_file: str):
        """备份数据库"""
        try:
            import subprocess
            
            cmd = [
                'mysqldump',
                f"--host={self.config['host']}",
                f"--port={self.config['port']}",
                f"--user={self.config['username']}",
                f"--password={self.config['password']}",
                '--single-transaction',
                '--routines',
                '--triggers',
                self.config['database']
            ]
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                subprocess.run(cmd, stdout=f, check=True)
            
            self.logger.info(f"数据库备份成功: {backup_file}")
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            raise
    
    def restore_database(self, backup_file: str):
        """恢复数据库"""
        try:
            import subprocess
            
            cmd = [
                'mysql',
                f"--host={self.config['host']}",
                f"--port={self.config['port']}",
                f"--user={self.config['username']}",
                f"--password={self.config['password']}",
                self.config['database']
            ]
            
            with open(backup_file, 'r', encoding='utf-8') as f:
                subprocess.run(cmd, stdin=f, check=True)
            
            self.logger.info(f"数据库恢复成功: {backup_file}")
            
        except Exception as e:
            self.logger.error(f"数据库恢复失败: {e}")
            raise
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        try:
            with self.get_session() as session:
                # 获取表信息
                tables_sql = """
                SELECT TABLE_NAME, TABLE_ROWS, DATA_LENGTH, INDEX_LENGTH
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = :database
                """
                
                result = session.execute(text(tables_sql), {'database': self.config['database']})
                tables = result.fetchall()
                
                stats = {
                    'tables': [],
                    'total_size': 0,
                    'total_rows': 0
                }
                
                for table in tables:
                    table_info = {
                        'name': table[0],
                        'rows': table[1] or 0,
                        'data_size': table[2] or 0,
                        'index_size': table[3] or 0
                    }
                    stats['tables'].append(table_info)
                    stats['total_size'] += table_info['data_size'] + table_info['index_size']
                    stats['total_rows'] += table_info['rows']
                
                return stats
                
        except Exception as e:
            self.logger.error(f"获取数据库统计信息失败: {e}")
            return None
