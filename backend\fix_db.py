import pymysql

conn = pymysql.connect(host='localhost', user='root', password='123456', database='file_share_system', charset='utf8mb4')
cursor = conn.cursor()

try:
    cursor.execute("ALTER TABLE download_records ADD COLUMN folder_id INT NULL COMMENT '文件夹ID' AFTER file_id")
    print("添加 folder_id 字段成功")
except:
    print("folder_id 字段已存在")

try:
    cursor.execute("ALTER TABLE download_records MODIFY COLUMN file_id INT NULL")
    print("修改 file_id 为可空成功")
except Exception as e:
    print(f"修改失败: {e}")

cursor.execute("SELECT id FROM users WHERE username = 'fjj'")
user = cursor.fetchone()
if user:
    cursor.execute("UPDATE download_records SET user_id = %s WHERE user_id IS NULL", (user[0],))
    print(f"更新了 {cursor.rowcount} 条记录")

conn.commit()
conn.close()
print("完成")
