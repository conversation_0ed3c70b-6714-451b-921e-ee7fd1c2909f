<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能演示 - 文件分享系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>文件分享系统 - 前端功能演示</h1>
        
        <div class="demo-section">
            <h3>🔐 用户认证状态</h3>
            <div id="authInfo">检查中...</div>
            <button onclick="checkAuth()">检查认证</button>
            <button onclick="testLogout()">测试登出</button>
        </div>
        
        <div class="demo-section">
            <h3>📊 系统信息</h3>
            <div id="systemInfo">加载中...</div>
            <button onclick="loadSystemInfo()">刷新系统信息</button>
        </div>
        
        <div class="demo-section">
            <h3>👥 在线用户统计</h3>
            <div id="onlineUsers">加载中...</div>
            <button onclick="loadOnlineUsers()">刷新在线用户</button>
        </div>
        
        <div class="demo-section">
            <h3>📈 系统统计</h3>
            <div id="systemStats">加载中...</div>
            <button onclick="loadSystemStats()">刷新统计信息</button>
        </div>
        
        <div class="demo-section">
            <h3>🎯 前端功能特性</h3>
            <ul class="feature-list">
                <li>用户认证与会话管理</li>
                <li>实时系统状态显示</li>
                <li>在线用户统计</li>
                <li>存储空间监控</li>
                <li>响应式用户界面</li>
                <li>用户菜单与操作</li>
                <li>通知系统（已优化）</li>
                <li>文件上传界面</li>
                <li>搜索功能界面</li>
                <li>文件管理界面</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h3>🔧 API测试</h3>
            <div id="apiTests">
                <button onclick="testAllAPIs()">测试所有API</button>
                <pre id="apiResults">等待测试...</pre>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script>
        // 检查认证状态
        async function checkAuth() {
            const authDiv = document.getElementById('authInfo');
            
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    authDiv.innerHTML = '<span class="status-error">❌ 未登录</span>';
                    return;
                }
                
                const auth = JSON.parse(authData);
                const response = await fetch(`${auth.serverUrl}/api/auth/verify`, {
                    headers: { 'Authorization': `Bearer ${auth.token}` }
                });
                
                const result = await response.json();
                
                if (result.valid) {
                    authDiv.innerHTML = `
                        <div class="status-ok">✅ 已登录</div>
                        <div class="info-grid">
                            <div class="info-card">
                                <div class="info-label">用户名</div>
                                <div class="info-value">${result.user.username}</div>
                            </div>
                            <div class="info-card">
                                <div class="info-label">角色</div>
                                <div class="info-value">${result.user.is_admin ? '管理员' : '普通用户'}</div>
                            </div>
                            <div class="info-card">
                                <div class="info-label">登录时间</div>
                                <div class="info-value">${new Date(auth.loginTime).toLocaleString()}</div>
                            </div>
                        </div>
                    `;
                } else {
                    authDiv.innerHTML = '<span class="status-error">❌ 认证失败</span>';
                }
            } catch (error) {
                authDiv.innerHTML = `<span class="status-error">❌ 检查失败: ${error.message}</span>`;
            }
        }
        
        // 加载系统信息
        async function loadSystemInfo() {
            const infoDiv = document.getElementById('systemInfo');
            
            try {
                const response = await SystemAPI.getSystemInfo();
                
                infoDiv.innerHTML = `
                    <div class="info-grid">
                        <div class="info-card">
                            <div class="info-label">系统版本</div>
                            <div class="info-value">${response.version}</div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">运行时间</div>
                            <div class="info-value">${formatUptime(response.uptime)}</div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">在线用户</div>
                            <div class="info-value">${response.online_users || 0}</div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">服务状态</div>
                            <div class="info-value status-ok">${response.status === 'running' ? '运行中' : '已停止'}</div>
                        </div>
                    </div>
                `;
                
                if (response.storage) {
                    const storage = response.storage;
                    const usedPercent = (storage.used / storage.total * 100).toFixed(1);
                    infoDiv.innerHTML += `
                        <div class="info-card">
                            <div class="info-label">存储使用率</div>
                            <div class="info-value">${usedPercent}% (${formatBytes(storage.used)} / ${formatBytes(storage.total)})</div>
                        </div>
                    `;
                }
            } catch (error) {
                infoDiv.innerHTML = `<span class="status-error">❌ 加载失败: ${error.message}</span>`;
            }
        }
        
        // 加载在线用户
        async function loadOnlineUsers() {
            const usersDiv = document.getElementById('onlineUsers');
            
            try {
                const response = await SystemAPI.getOnlineUsers();
                const users = response.users || [];
                
                if (users.length === 0) {
                    usersDiv.innerHTML = '<div class="status-warning">⚠️ 暂无在线用户</div>';
                    return;
                }
                
                usersDiv.innerHTML = `
                    <div class="status-ok">✅ 在线用户: ${users.length} 人</div>
                    <div class="info-grid">
                        ${users.map(user => `
                            <div class="info-card">
                                <div class="info-label">${user.username}</div>
                                <div class="info-value">在线 ${formatDuration(user.online_duration)}</div>
                                <div style="font-size: 12px; color: #666;">IP: ${user.ip_address || '未知'}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } catch (error) {
                usersDiv.innerHTML = `<span class="status-error">❌ 加载失败: ${error.message}</span>`;
            }
        }
        
        // 加载系统统计
        async function loadSystemStats() {
            const statsDiv = document.getElementById('systemStats');
            
            try {
                const response = await SystemAPI.getStatistics();
                
                statsDiv.innerHTML = `
                    <div class="info-grid">
                        <div class="info-card">
                            <div class="info-label">总用户数</div>
                            <div class="info-value">${response.total_users || 0}</div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">活跃用户</div>
                            <div class="info-value">${response.active_users || 0}</div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">管理员</div>
                            <div class="info-value">${response.admin_users || 0}</div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">总活动数</div>
                            <div class="info-value">${response.total_activities || 0}</div>
                        </div>
                        <div class="info-card">
                            <div class="info-label">24小时活动</div>
                            <div class="info-value">${response.recent_activities_24h || 0}</div>
                        </div>
                    </div>
                `;
            } catch (error) {
                statsDiv.innerHTML = `<span class="status-error">❌ 加载失败: ${error.message}</span>`;
            }
        }
        
        // 测试登出
        async function testLogout() {
            if (confirm('确定要测试登出功能吗？这将清除登录状态。')) {
                try {
                    await UserAPI.logout();
                    localStorage.removeItem('fileShareAuth');
                    alert('登出成功！页面将跳转到登录页面。');
                    window.location.href = 'login.html';
                } catch (error) {
                    alert(`登出失败: ${error.message}`);
                }
            }
        }
        
        // 测试所有API
        async function testAllAPIs() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.textContent = '正在测试API...';
            
            const results = [];
            
            // 测试健康检查
            try {
                const response = await fetch('http://localhost:8086/api/health');
                const data = await response.json();
                results.push(`✅ 健康检查: ${data.status}`);
            } catch (error) {
                results.push(`❌ 健康检查失败: ${error.message}`);
            }
            
            // 测试系统信息
            try {
                await SystemAPI.getSystemInfo();
                results.push('✅ 系统信息API正常');
            } catch (error) {
                results.push(`❌ 系统信息API失败: ${error.message}`);
            }
            
            // 测试在线用户（需要管理员权限）
            try {
                await SystemAPI.getOnlineUsers();
                results.push('✅ 在线用户API正常');
            } catch (error) {
                results.push(`⚠️ 在线用户API: ${error.message}`);
            }
            
            resultsDiv.textContent = results.join('\n');
        }
        
        // 工具函数
        function formatUptime(seconds) {
            if (seconds < 60) return `${Math.floor(seconds)}秒`;
            if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`;
            if (seconds < 86400) return `${Math.floor(seconds / 3600)}小时`;
            return `${Math.floor(seconds / 86400)}天`;
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatDuration(duration) {
            // 简单的持续时间格式化
            return duration.replace(/(\d+):(\d+):(\d+).*/, '$1小时$2分钟');
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            checkAuth();
            loadSystemInfo();
        });
    </script>
</body>
</html>
